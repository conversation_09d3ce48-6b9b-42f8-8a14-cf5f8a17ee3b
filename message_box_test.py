import sys
from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PySide6.QtMultimedia import QSoundEffect
from PySide6.QtCore import QUrl
import os

def get_absolute_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(os.path.dirname(__file__))
    
    return os.path.join(base_path, relative_path)

class CustomMessageBox(QMessageBox):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        try:
            self.sound = QSoundEffect()
            
            # Get absolute path to the sound file
            sound_path = get_absolute_path(os.path.join("assets", "notification.wav"))
            print(f"Sound path: {sound_path}")
            
            if not os.path.exists(sound_path):
                print(f"Error: Sound file not found at {sound_path}")
                return
                
            url = QUrl.fromLocalFile(sound_path)
            self.sound.setSource(url)
            self.sound.setVolume(1.0)
            
            # Load and check status
            if self.sound.status() == QSoundEffect.Error:
                print("Error loading sound file")
            elif self.sound.status() == QSoundEffect.Ready:
                print("Sound file loaded successfully")
            
        except Exception as e:
            print(f"Error initializing sound: {str(e)}")

    def showEvent(self, event):
        try:
            if hasattr(self, 'sound') and self.sound.status() == QSoundEffect.Ready:
                print("Playing sound...")
                self.sound.play()
        except Exception as e:
            print(f"Error playing sound: {str(e)}")
        finally:
            super().showEvent(event)

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Message Box Sound Test")
        self.setGeometry(100, 100, 400, 300)

        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # Create buttons for different message types
        warning_btn = QPushButton("Show Warning Message")
        error_btn = QPushButton("Show Error Message")
        info_btn = QPushButton("Show Info Message")
        question_btn = QPushButton("Show Question Message")

        # Connect buttons to their respective functions
        warning_btn.clicked.connect(lambda: self.show_message(
            "Warning", "This is a warning message!", QMessageBox.Warning))
        error_btn.clicked.connect(lambda: self.show_message(
            "Error", "This is an error message!", QMessageBox.Critical))
        info_btn.clicked.connect(lambda: self.show_message(
            "Information", "This is an information message!", QMessageBox.Information))
        question_btn.clicked.connect(lambda: self.show_message(
            "Question", "This is a question message?", QMessageBox.Question))

        # Add buttons to layout
        layout.addWidget(warning_btn)
        layout.addWidget(error_btn)
        layout.addWidget(info_btn)
        layout.addWidget(question_btn)

    def show_message(self, title, message, icon_type):
        """Show a message box with custom sound"""
        msg_box = CustomMessageBox(self)
        msg_box.setIcon(icon_type)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        return msg_box.exec()

def main():
    # Create the Qt Application
    app = QApplication(sys.argv)
    
    # Create and show the main window
    window = TestWindow()
    window.show()
    
    # Start the event loop
    sys.exit(app.exec())

if __name__ == "__main__":
    main()