"""
Centralized Color Manager for consistent color assignment across the application
"""

import random

class ColorManager:
    """Singleton class to manage color assignments consistently across the application"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ColorManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not ColorManager._initialized:
            # Use the EXACT color palette from _initialize_data_structures method in gui_main.py
            self.color_palette = [
                '#511D43',      # Purple
            '#901E3E',      # Meroon
            '#DC2525',      # Red
            '#9BC09C',      # Light Green
            '#0B1D51',      # Dark Blue
            '#FCEF91',      # Yellow
            '#FF7601',      # Orange
            '#4DA8DA',      # Light Blue
            '#16610E',      # Dark Green
            '#4B352A',      # Brown
            ]
            
            # Dictionary to store column -> color mappings (named 'color' as requested)
            self.color = {}

            # Keep track of available colors for random selection
            self.available_colors = self.color_palette.copy()

            ColorManager._initialized = True
    


    def get_color(self, column_name: str) -> str:
        """
        Get color for a column name. If not assigned, assigns a random color.

        Args:
            column_name: Name of the column

        Returns:
            Hex color string
        """
        if column_name not in self.color:
            # Assign random color from available colors
            if self.available_colors:
                # Pick a random color from available colors
                selected_color = random.choice(self.available_colors)
                self.color[column_name] = selected_color
                # Remove the selected color from available colors to avoid duplicates
                self.available_colors.remove(selected_color)
                print(f"ColorManager: Randomly assigned {selected_color} to '{column_name}'")
            else:
                # If all colors are used, start reusing colors randomly
                selected_color = random.choice(self.color_palette)
                self.color[column_name] = selected_color
                print(f"ColorManager: Randomly assigned {selected_color} to '{column_name}' (reusing colors)")
        else:
            print(f"ColorManager: Reusing {self.color[column_name]} for '{column_name}'")

        return self.color[column_name]
    
    def get_all_assignments(self) -> dict:
        """Get all current color assignments"""
        return self.color.copy()

    def clear_assignments(self):
        """Clear all color assignments (for new session)"""
        self.color.clear()
        # Reset available colors for random selection
        self.available_colors = self.color_palette.copy()
        print("ColorManager: Cleared all color assignments")

    def set_color(self, column_name: str, color: str):
        """Manually set color for a column"""
        self.color[column_name] = color
        # Remove the color from available colors if it was there
        if color in self.available_colors:
            self.available_colors.remove(color)
        print(f"ColorManager: Manually set {color} for '{column_name}'")

    def get_assignment_count(self) -> int:
        """Get number of columns that have been assigned colors"""
        return len(self.color)

    def print_assignments(self):
        """Print all current color assignments for debugging"""
        print("\n=== Current Color Assignments ===")
        for column, color in self.color.items():
            print(f"  {column}: {color}")
        print(f"Total assignments: {len(self.color)}")
        print(f"Available colors remaining: {len(self.available_colors)}")
        print("=" * 35)

    def get_color_dict(self) -> dict:
        """Get the color dictionary (for compatibility with existing code)"""
        return self.color


# Global instance for easy access
color_manager = ColorManager()


def get_column_color(column_name: str) -> str:
    """
    Convenience function to get color for a column
    
    Args:
        column_name: Name of the column
        
    Returns:
        Hex color string
    """
    return color_manager.get_color(column_name)


def clear_color_assignments():
    """Convenience function to clear all color assignments"""
    color_manager.clear_assignments()


def print_color_assignments():
    """Convenience function to print all color assignments"""
    color_manager.print_assignments()
