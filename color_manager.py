"""
Centralized Color Manager for consistent color assignment across the application
"""

class ColorManager:
    """Singleton class to manage color assignments consistently across the application"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ColorManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not ColorManager._initialized:
            # High-contrast, distinct color palette optimized for visibility
            self.color_palette = [
                '#01a5e3',  # Bright blue
                '#32c133',  # Bright green  
                '#fb6238',  # Bright orange
                '#ee0100',  # Bright red
                '#f7d02e',  # Bright yellow
                '#9467bd',  # Bright purple
                '#17becf',  # Bright cyan
                '#ff7f0e',  # Safety orange (distinct from fb6238)
                '#2ca02c',  # Forest green (distinct from 32c133)
                '#d62728',  # Crimson red (distinct from ee0100)
                '#1f77b4',  # Steel blue (distinct from 01a5e3)
                '#e377c2',  # Pink
                '#8c564b',  # <PERSON>
                '#bcbd22',  # Olive green
                '#7f7f7f',  # Gray
                '#ff1493',  # Deep pink
                '#00ced1',  # Dark turquoise
                '#ffa500',  # Orange
                '#9932cc',  # Dark orchid
                '#228b22',  # Forest green variant
            ]
            
            # Dictionary to store column -> color mappings
            self.color_assignments = {}
            self.assignment_counter = 0
            ColorManager._initialized = True
    
    def get_color(self, column_name: str) -> str:
        """
        Get color for a column name. If not assigned, assigns a new color.
        
        Args:
            column_name: Name of the column
            
        Returns:
            Hex color string
        """
        if column_name not in self.color_assignments:
            # Assign new color from palette
            color_index = self.assignment_counter % len(self.color_palette)
            self.color_assignments[column_name] = self.color_palette[color_index]
            self.assignment_counter += 1
            print(f"ColorManager: Assigned {self.color_assignments[column_name]} to '{column_name}'")
        
        return self.color_assignments[column_name]
    
    def get_all_assignments(self) -> dict:
        """Get all current color assignments"""
        return self.color_assignments.copy()
    
    def clear_assignments(self):
        """Clear all color assignments (for new session)"""
        self.color_assignments.clear()
        self.assignment_counter = 0
        print("ColorManager: Cleared all color assignments")
    
    def set_color(self, column_name: str, color: str):
        """Manually set color for a column"""
        self.color_assignments[column_name] = color
        print(f"ColorManager: Manually set {color} for '{column_name}'")
    
    def get_assignment_count(self) -> int:
        """Get number of columns that have been assigned colors"""
        return len(self.color_assignments)
    
    def print_assignments(self):
        """Print all current color assignments for debugging"""
        print("\n=== Current Color Assignments ===")
        for column, color in self.color_assignments.items():
            print(f"  {column}: {color}")
        print(f"Total assignments: {len(self.color_assignments)}")
        print("=" * 35)


# Global instance for easy access
color_manager = ColorManager()


def get_column_color(column_name: str) -> str:
    """
    Convenience function to get color for a column
    
    Args:
        column_name: Name of the column
        
    Returns:
        Hex color string
    """
    return color_manager.get_color(column_name)


def clear_color_assignments():
    """Convenience function to clear all color assignments"""
    color_manager.clear_assignments()


def print_color_assignments():
    """Convenience function to print all color assignments"""
    color_manager.print_assignments()
