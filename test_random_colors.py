#!/usr/bin/env python3
"""
Test script to verify random color assignment system
"""

from color_manager import color_manager, get_column_color, print_color_assignments
from color_utils import assign_color

def test_random_color_assignment():
    """Test the random color assignment system"""
    print("=" * 70)
    print("TESTING RANDOM COLOR ASSIGNMENT SYSTEM")
    print("=" * 70)
    
    # Clear any existing assignments
    color_manager.clear_assignments()
    
    # Your actual column names
    test_columns = [
        "Tank Bottom (°C)",
        "Tank Flange (°C)", 
        "Thruster ka Flange (°C)",
        "Nozzle ki Convergent (°C)",
        "Nozzle ki Exit (°C)",
        "Tank Mid (°C)",
        "Tank ka Lid (°C)",
        "Thruster ka Chamber (°C)"
    ]
    
    print("\n1. Testing random color assignment:")
    print("-" * 50)
    colors_assigned = {}
    for column in test_columns:
        color = color_manager.get_color(column)
        colors_assigned[column] = color
        print(f"  {column}: {color}")
    
    print("\n2. Verifying color dictionary storage:")
    print("-" * 50)
    color_dict = color_manager.get_color_dict()
    print(f"  Color dictionary has {len(color_dict)} entries:")
    for column, color in color_dict.items():
        print(f"    {column}: {color}")
    
    print("\n3. Testing persistence (getting same colors again):")
    print("-" * 50)
    persistent = True
    for column in test_columns:
        color_again = color_manager.get_color(column)
        if color_again != colors_assigned[column]:
            print(f"  ❌ FAILED: {column}")
            print(f"     First:  {colors_assigned[column]}")
            print(f"     Second: {color_again}")
            persistent = False
        else:
            print(f"  ✅ PERSISTENT: {column} -> {color_again}")
    
    print("\n4. Testing color uniqueness:")
    print("-" * 50)
    all_colors = list(colors_assigned.values())
    unique_colors = set(all_colors)
    
    print(f"  Total columns: {len(test_columns)}")
    print(f"  Unique colors: {len(unique_colors)}")
    
    if len(unique_colors) == len(test_columns):
        print("  ✅ All colors are unique!")
    else:
        print("  ⚠️  Some colors are duplicated")
        # Find duplicates
        from collections import Counter
        color_counts = Counter(all_colors)
        duplicates = {color: count for color, count in color_counts.items() if count > 1}
        for color, count in duplicates.items():
            columns_with_color = [col for col, c in colors_assigned.items() if c == color]
            print(f"    Color {color} used {count} times: {columns_with_color}")
    
    print("\n5. Testing randomness (multiple runs):")
    print("-" * 50)
    print("  Running 3 separate tests to verify randomness...")
    
    all_assignments = []
    for run in range(3):
        color_manager.clear_assignments()
        run_colors = []
        for column in test_columns[:3]:  # Test with first 3 columns
            color = color_manager.get_color(column)
            run_colors.append(color)
        all_assignments.append(run_colors)
        print(f"    Run {run + 1}: {run_colors}")
    
    # Check if assignments are different across runs
    all_same = all(assignment == all_assignments[0] for assignment in all_assignments)
    if all_same:
        print("  ⚠️  All runs produced identical assignments (might not be truly random)")
    else:
        print("  ✅ Different assignments across runs (randomness working)")
    
    print("\n6. Testing color_utils compatibility:")
    print("-" * 50)
    color_manager.clear_assignments()
    dummy_dict = {}  # This won't be used but kept for compatibility
    
    utils_colors = {}
    for column in test_columns:
        color = assign_color(column, dummy_dict)
        utils_colors[column] = color
        print(f"  {column}: {color}")
    
    # Verify that color_utils uses the same centralized system
    manager_colors = color_manager.get_color_dict()
    utils_consistent = all(utils_colors[col] == manager_colors[col] for col in test_columns)
    
    if utils_consistent:
        print("  ✅ color_utils is consistent with color_manager")
    else:
        print("  ❌ color_utils is NOT consistent with color_manager")
    
    print("\n7. Final color assignments:")
    print("-" * 50)
    print_color_assignments()
    
    print("\n" + "=" * 70)
    print("SUMMARY")
    print("=" * 70)
    
    success = (
        len(unique_colors) == len(test_columns) and
        persistent and
        utils_consistent
    )
    
    if success:
        print("✅ SUCCESS: Random color assignment system is working correctly!")
        print("   - Colors are assigned randomly from the palette")
        print("   - Each column gets a unique color")
        print("   - Colors are stored in the 'color' dictionary")
        print("   - Color assignments persist within the session")
        print("   - All components use the same centralized system")
    else:
        print("❌ ISSUES FOUND:")
        if len(unique_colors) != len(test_columns):
            print("   - Some colors are duplicated")
        if not persistent:
            print("   - Color assignments are not persistent")
        if not utils_consistent:
            print("   - color_utils is not using centralized system")
    
    print("\nExpected behavior after restart:")
    print("- Each column gets a randomly assigned unique color")
    print("- Same column always gets the same color in a session")
    print("- Colors are stored in color_manager.color dictionary")
    print("- All plots use consistent colors from this dictionary")
    
    return success

if __name__ == "__main__":
    success = test_random_color_assignment()
    exit(0 if success else 1)
