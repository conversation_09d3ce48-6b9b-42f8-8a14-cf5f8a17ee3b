import os
import sys
import traceback
import psycopg2
import pandas as pd
from psycopg2.extras import Json

# Database connection parameters - update these to match your configuration
DB_PARAMS = {
    'dbname': 'vapr_idex',
    'user': 'postgres',
    'password': 'postgres',
    'host': 'localhost',
    'port': '5432'
}

def fix_filtered_temp_data(test_id):
    """Fix the filtered temperature data for a specific test"""
    conn = None
    try:
        # Connect to the database
        conn = psycopg2.connect(**DB_PARAMS)
        cur = conn.cursor()
        
        # Get the test data
        cur.execute("SELECT performance_data FROM test_data WHERE test_id = %s", (test_id,))
        result = cur.fetchone()
        
        if not result or not result[0]:
            print(f"No performance data found for test_id {test_id}")
            return False
        
        perf_data = result[0]
        
        # Check if we have filtered temperature data
        filtered_data = None
        if 'filtered_temp_data' in perf_data:
            filtered_data = perf_data['filtered_temp_data']
            print(f"Found filtered_temp_data: {type(filtered_data)}")
        elif 'filtered_temperature_data' in perf_data:
            filtered_data = perf_data['filtered_temperature_data']
            print(f"Found filtered_temperature_data: {type(filtered_data)}")
        
        if not filtered_data:
            print(f"No filtered temperature data found for test_id {test_id}")
            return False
        
        # Get the temperature data
        cur.execute("SELECT time_data, temperature_data FROM temperature_data WHERE test_id = %s", (test_id,))
        temp_result = cur.fetchone()
        
        if not temp_result:
            print(f"No temperature data found for test_id {test_id}")
            return False
        
        time_data, temperature_data = temp_result
        
        # Create a DataFrame from the temperature data
        df = pd.DataFrame({'time': time_data})
        for col, values in temperature_data.items():
            df[col] = values
        
        # Check if we have selection criteria
        if isinstance(filtered_data, dict) and 'selected_columns' in filtered_data and 'selected_ranges' in filtered_data:
            selected_columns = filtered_data['selected_columns']
            selected_ranges = filtered_data['selected_ranges']
            
            # Create mask for selected ranges
            mask = pd.Series(False, index=df.index)
            for start, end in selected_ranges:
                mask |= ((df['time'] >= start) & (df['time'] <= end))
            
            # Filter data
            filtered_df = df[mask]
            
            # Select only chosen columns plus time
            columns_to_use = ['time'] + selected_columns
            available_columns = [col for col in columns_to_use if col in filtered_df.columns]
            filtered_df = filtered_df[available_columns]
            
            # Create the updated filtered_temp_data
            updated_filtered_data = {
                'selected_columns': selected_columns,
                'selected_ranges': selected_ranges,
                'time_data': filtered_df['time'].tolist(),
                'temperature_data': {col: filtered_df[col].tolist() for col in filtered_df.columns if col != 'time'}
            }
            
            # Update the performance_data
            perf_data['filtered_temp_data'] = updated_filtered_data
            if 'filtered_temperature_data' in perf_data:
                del perf_data['filtered_temperature_data']
            
            # Save the updated performance_data
            cur.execute(
                "UPDATE test_data SET performance_data = %s WHERE test_id = %s",
                (Json(perf_data), test_id)
            )
            conn.commit()
            
            print(f"Successfully fixed filtered temperature data for test_id {test_id}")
            return True
        else:
            print(f"Filtered temperature data for test_id {test_id} doesn't have selection criteria")
            return False
    
    except Exception as e:
        print(f"Error fixing filtered temperature data: {str(e)}")
        traceback.print_exc()
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()

def get_test_ids():
    """Get all test IDs from the database"""
    conn = None
    try:
        conn = psycopg2.connect(**DB_PARAMS)
        cur = conn.cursor()
        cur.execute("SELECT test_id, test_no FROM test_data ORDER BY test_id")
        results = cur.fetchall()
        conn.close()
        
        if results:
            print(f"Found {len(results)} tests in the database:")
            for test_id, test_no in results:
                print(f"  Test ID: {test_id}, Test No: {test_no}")
            return [test_id for test_id, _ in results]
        else:
            print("No tests found in the database.")
            return []
    except Exception as e:
        print(f"Error getting test IDs: {str(e)}")
        traceback.print_exc()
        return []
    finally:
        if conn:
            conn.close()

def main():
    print("=== Fix Filtered Temperature Data Tool ===\n")
    
    # Get all test IDs
    test_ids = get_test_ids()
    if not test_ids:
        print("No tests found, exiting.")
        return
    
    # Ask which test to fix
    while True:
        try:
            test_id_input = input("\nEnter test ID to fix (or 'q' to quit, 'a' for all): ")
            if test_id_input.lower() == 'q':
                break
            
            if test_id_input.lower() == 'a':
                # Fix all tests
                for test_id in test_ids:
                    print(f"\nFixing test ID {test_id}...")
                    fix_filtered_temp_data(test_id)
                break
            
            test_id = int(test_id_input)
            if test_id not in test_ids:
                print(f"Test ID {test_id} not found in the database.")
                continue
            
            # Fix this test
            fix_filtered_temp_data(test_id)
            
        except ValueError:
            print("Please enter a valid test ID.")
        except Exception as e:
            print(f"Error: {str(e)}")
            traceback.print_exc()

if __name__ == "__main__":
    main()
