#!/usr/bin/env python3
"""
Test script to verify PlotManager color assignment fix
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_plot_manager_colors():
    """Test the PlotManager color assignment"""
    print("Testing PlotManager color assignment...")
    
    # Import the PlotManager
    try:
        from src.visualization.plot_manager import PlotManager
        
        # Create PlotManager instance
        plot_manager = PlotManager()
        
        # Test with your actual column names
        test_columns = [
            "Tank Bottom (°C)",
            "Tank Flange (°C)", 
            "Thruster ka Flange (°C)",
            "Nozzle ki Convergent (°C)",
            "Nozzle ki Exit (°C)",
            "Tank Mid (°C)",
            "Tank ka Lid (°C)",
            "Thruster ka Chamber (°C)"
        ]
        
        print("Assigning colors to your temperature columns:")
        print("=" * 50)
        
        colors_assigned = {}
        for column in test_columns:
            color = plot_manager.assign_color(column)
            colors_assigned[column] = color
            print(f"{column}: {color}")
        
        # Check for duplicates
        color_values = list(colors_assigned.values())
        unique_colors = set(color_values)
        
        print("\n" + "=" * 50)
        print("RESULTS:")
        print(f"Total columns: {len(test_columns)}")
        print(f"Unique colors assigned: {len(unique_colors)}")
        
        if len(unique_colors) == len(test_columns):
            print("✅ SUCCESS: All columns got unique colors!")
        else:
            print("⚠️  WARNING: Some columns got duplicate colors")
        
        # Check for black colors
        black_colors = [col for col, color in colors_assigned.items() if color.lower() in ['#000000', '#000', 'black']]
        if black_colors:
            print(f"❌ PROBLEM: Found black colors for: {black_colors}")
        else:
            print("✅ SUCCESS: No black colors found!")
        
        return colors_assigned
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("This might be due to missing dependencies. The fix should still work in the main application.")
        return None
    except Exception as e:
        print(f"Error: {e}")
        return None

if __name__ == "__main__":
    print("=" * 60)
    print("PLOT MANAGER COLOR ASSIGNMENT TEST")
    print("=" * 60)
    
    colors = test_plot_manager_colors()
    
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    print("The PlotManager has been updated to use dynamic color assignment.")
    print("This should fix the black color issue in the 'All Temperature Distributions' plot.")
    print("\nTo see the fix in action:")
    print("1. Restart your VAPR-iDEX application")
    print("2. Load your temperature data")
    print("3. The plot should now show bright, distinct colors for all columns")
