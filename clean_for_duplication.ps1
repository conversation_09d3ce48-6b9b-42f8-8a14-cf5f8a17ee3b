# Clean VAPR-iDEX Project for Duplication
# Run this script in the NEW duplicated folder to clean up cached files and paths

Write-Host "🧹 Cleaning VAPR-iDEX project for duplication..." -ForegroundColor Green

# 1. Remove build and cache directories
$dirsToRemove = @("build", "dist", "__pycache__")
foreach ($dir in $dirsToRemove) {
    if (Test-Path $dir) {
        Write-Host "Removing $dir directory..." -ForegroundColor Yellow
        Remove-Item -Recurse -Force $dir -ErrorAction SilentlyContinue
    }
}

# 2. Remove all __pycache__ directories recursively
Write-Host "Removing all __pycache__ directories..." -ForegroundColor Yellow
Get-ChildItem -Recurse -Name "__pycache__" -Directory | ForEach-Object { 
    Remove-Item -Recurse -Force $_ -ErrorAction SilentlyContinue 
}

# 3. Remove font cache files
Write-Host "Removing font cache files..." -ForegroundColor Yellow
Remove-Item "fonts\*.pkl" -ErrorAction SilentlyContinue

# 4. Remove .pyc files
Write-Host "Removing .pyc files..." -ForegroundColor Yellow
Get-ChildItem -Recurse -Name "*.pyc" | ForEach-Object { 
    Remove-Item -Force $_ -ErrorAction SilentlyContinue 
}

# 5. Clean VAPR_iDEX.spec if it exists
if (Test-Path "VAPR_iDEX.spec") {
    Write-Host "Cleaning VAPR_iDEX.spec..." -ForegroundColor Yellow
    $content = Get-Content "VAPR_iDEX.spec" -Raw
    # Replace any hardcoded pathex with empty array
    $content = $content -replace "pathex=\['[^']*'\]", "pathex=[]"
    $content = $content -replace 'pathex=\["[^"]*"\]', "pathex=[]"
    Set-Content "VAPR_iDEX.spec" -Value $content
}

# 6. Verify fonts directory structure
if (Test-Path "fonts") {
    $requiredFonts = @("DejaVuSansCondensed.ttf", "DejaVuSansCondensed-Bold.ttf")
    foreach ($font in $requiredFonts) {
        if (Test-Path "fonts\$font") {
            Write-Host "✅ Font found: $font" -ForegroundColor Green
        } else {
            Write-Host "❌ Missing font: $font" -ForegroundColor Red
        }
    }
} else {
    Write-Host "❌ Fonts directory not found!" -ForegroundColor Red
}

# 7. Test font detection
Write-Host "Testing font detection..." -ForegroundColor Yellow
try {
    $result = python -c "import sys; sys.path.insert(0, 'src/report_generation'); import setup_fonts; print('Fonts dir:', setup_fonts.get_fonts_dir()); print('Setup result:', setup_fonts.setup_fonts())" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Font detection test passed" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Font detection test failed (may be due to missing dependencies)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ Could not test font detection" -ForegroundColor Yellow
}

Write-Host "🎉 Cleanup complete! Project is ready for use." -ForegroundColor Green
Write-Host ""
Write-Host "📋 Summary of actions taken:" -ForegroundColor Cyan
Write-Host "  • Removed build/, dist/, and __pycache__ directories" -ForegroundColor White
Write-Host "  • Removed font cache files (*.pkl)" -ForegroundColor White
Write-Host "  • Cleaned VAPR_iDEX.spec pathex entries" -ForegroundColor White
Write-Host "  • Verified font files exist" -ForegroundColor White
Write-Host ""
Write-Host "⚠️ Remember to:" -ForegroundColor Yellow
Write-Host "  • Update any configuration files with new paths" -ForegroundColor White
Write-Host "  • Test the application before deploying" -ForegroundColor White
