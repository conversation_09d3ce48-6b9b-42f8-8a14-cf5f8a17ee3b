"""
Main Window for VAPR-iDEX Application
Refactored to use modular components and controllers.
"""

import sys
from PySide6.QtWidgets import QMainWindow, QMessageBox
from PySide6.QtCore import QTimer
from PySide6.QtGui import QIcon

from gui import Ui_VaprIdexMainWindow
from config.app_config import AppConfig
from controllers import (
    MainController, 
    DatabaseController, 
    AuthenticationController,
    PlotController
)
from ui.dialogs import ExistingDataLoadDialog
from ui.managers.ui_manager import UIManager
from ui.managers.menu_manager import MenuManager
from ui.managers.connection_manager import ConnectionManager
from services.data_service import DataService
from services.icon_service import IconService
from data_recovery import AutoSaver
from src.utils import get_resource_path


class MainWindow(QMainWindow):
    """Main application window with modular architecture."""
    
    def __init__(self):
        super().__init__()
        
        # Initialize UI first
        self._initialize_ui()
        
        # Initialize controllers
        self._initialize_controllers()
        
        # Initialize managers
        self._initialize_managers()
        
        # Initialize services
        self._initialize_services()
        
        # Setup components
        self._setup_components()
        
        # Final setup
        self._finalize_setup()
    
    def _initialize_ui(self):
        """Initialize the main UI components."""
        self.ui = Ui_VaprIdexMainWindow()
        self.ui.setupUi(self)
        
        # Set window properties
        self.setWindowTitle(AppConfig.APP_NAME)
        icon_path = get_resource_path("assets/icon.ico")
        self.setWindowIcon(QIcon(icon_path))
        
        # Hide tab bars
        self.ui.tabWidget.tabBar().hide()
        self.ui.tabWidgetPlotSettings.tabBar().hide()
    
    def _initialize_controllers(self):
        """Initialize business logic controllers."""
        self.main_controller = MainController(self)
        self.db_controller = DatabaseController(self)
        self.auth_controller = AuthenticationController(self)
        self.plot_controller = PlotController(self)
    
    def _initialize_managers(self):
        """Initialize UI managers."""
        self.ui_manager = UIManager(self)
        self.menu_manager = MenuManager(self)
        self.connection_manager = ConnectionManager(self)
    
    def _initialize_services(self):
        """Initialize application services."""
        self.data_service = DataService(self)
        self.icon_service = IconService()
        
        # Initialize auto-saver
        self.auto_saver = AutoSaver(self)
    
    def _setup_components(self):
        """Setup various application components."""
        # Setup dialogs
        self.existing_data_dialog = ExistingDataLoadDialog(self)
        
        # Setup UI components
        self.ui_manager.setup_all()
        
        # Setup menu
        self.menu_manager.setup_menu()
        
        # Setup icons
        self.icon_service.setup_all_icons(self)
        
        # Setup connections
        self.connection_manager.setup_all_connections()
    
    def _finalize_setup(self):
        """Final setup steps."""
        # Set initial state
        self.ui.lblCurentSection.setText('Load Data')
        self.ui.btnPlots.setEnabled(False)
        
        # Set special value texts
        self.ui.subLnEdtPropRIBefFirg_2.setSpecialValueText("NA")
        self.ui.subLnEdtPropRIAftFirg_2.setSpecialValueText("NA")
        self.ui.subLnEdtPropRI.setSpecialValueText("NA")
        
        # Setup delayed plot button enabling
        QTimer.singleShot(
            AppConfig.Timeouts.PLOTS_BUTTON_ENABLE, 
            self.force_enable_plots_button
        )
    
    def force_enable_plots_button(self):
        """Enable plots button if valid data exists."""
        self.main_controller.force_enable_plots_button()
    
    def handle_mode_change(self, mode_name: str, mode_index: int):
        """Handle mode changes from toggle switch."""
        self.main_controller.handle_mode_change(mode_name, mode_index)
    
    def handle_section_change(self, section_name: str):
        """Handle section changes."""
        self.main_controller.handle_section_change(section_name)
    
    def closeEvent(self, event):
        """Handle window close event."""
        try:
            # Stop auto-saver
            if hasattr(self, 'auto_saver') and self.auto_saver is not None:
                try:
                    self.auto_saver.stop_timer()
                except Exception:
                    pass  # Ignore errors during cleanup
            
            # Close matplotlib figures
            try:
                import matplotlib.pyplot as plt
                plt.close('all')
            except Exception:
                pass
            
            # Database cleanup
            if hasattr(self, 'db_controller') and self.db_controller.is_connected():
                try:
                    self.db_controller.db_handler.cleanup_temp_plots()
                except Exception:
                    pass
                    
        except Exception as e:
            print(f"Error during cleanup: {e}")
        
        # Call parent close event
        super().closeEvent(event)
    
    def __del__(self):
        """Cleanup when window is destroyed."""
        try:
            # Close matplotlib figures
            import matplotlib.pyplot as plt
            plt.close('all')
            
            # Stop auto-saver
            if hasattr(self, 'auto_saver') and self.auto_saver is not None:
                try:
                    self.auto_saver.stop_timer()
                except Exception:
                    pass
        except Exception:
            pass  # Silently ignore all errors in destructor
