# VAPR-iDEX Modular Architecture Documentation

## Overview

The `gui_main.py` file has been refactored from a monolithic 5577-line file into a clean, modular, object-oriented architecture. This document outlines the new structure and how to use it.

## Architecture Overview

```
VAPR-iDEX Application
├── main.py                     # Application entry point
├── main_window.py              # Main window (simplified)
├── config/                     # Configuration and constants
├── ui/                         # UI components and managers
├── controllers/                # Business logic controllers
├── services/                   # Application services
├── utils/                      # Utility functions
└── [existing modules]          # src/, gui/, user_authentication/, etc.
```

## Key Benefits

1. **Separation of Concerns**: Each module has a single responsibility
2. **Maintainability**: Easier to locate and modify specific functionality
3. **Testability**: Individual components can be tested in isolation
4. **Reusability**: Components can be reused across different parts of the application
5. **Scalability**: New features can be added without affecting existing code

## Module Structure

### 1. Application Entry Point

**File**: `main.py`
- Application startup and initialization
- Logging configuration
- Exception handling
- Splash screen coordination

### 2. Main Window

**File**: `main_window.py`
- Simplified main window class (~200 lines vs 5000+)
- Coordinates between controllers and managers
- Handles window lifecycle events

### 3. Configuration

**Directory**: `config/`
- `app_config.py`: All application constants and settings
- Centralized configuration management
- UI styles, colors, timeouts, validation rules

### 4. UI Components

**Directory**: `ui/`

#### Dialogs (`ui/dialogs/`)
- `temperature_config_dialog.py`: Temperature data configuration
- `existing_data_dialog.py`: Existing data loading
- `photo_preview_dialog.py`: Photo preview and management

#### Widgets (`ui/widgets/`)
- `hover_button.py`: Custom button with hover effects
- Reusable UI components

#### Managers (`ui/managers/`)
- `ui_manager.py`: UI setup and state management
- `menu_manager.py`: Menu bar and toolbar management
- `connection_manager.py`: Signal-slot connections

### 5. Controllers

**Directory**: `controllers/`
- `main_controller.py`: Central coordination controller
- `database_controller.py`: Database operations
- `authentication_controller.py`: User authentication
- `plot_controller.py`: Plot management and generation

### 6. Services

**Directory**: `services/`
- `data_service.py`: Data operations and management
- `icon_service.py`: Icon setup and management

### 7. Utilities

**Directory**: `utils/`
- `data_utils.py`: Common data utility functions

## Usage Examples

### Starting the Application

```python
# main.py
from main_window import MainWindow
from ui.splash_screen import CustomSplashScreen

# Create splash screen
splash = CustomSplashScreen()
splash.show()

# Create main window
main_window = MainWindow()
```

### Adding New Functionality

#### 1. Adding a New Dialog

```python
# ui/dialogs/my_new_dialog.py
from PySide6.QtWidgets import QDialog

class MyNewDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()
    
    def _setup_ui(self):
        # Setup dialog UI
        pass
```

#### 2. Adding a New Controller

```python
# controllers/my_controller.py
from PySide6.QtCore import QObject

class MyController(QObject):
    def __init__(self, main_window):
        super().__init__(main_window)
        self.main_window = main_window
    
    def my_operation(self):
        # Implement business logic
        pass
```

#### 3. Adding a New Service

```python
# services/my_service.py
from PySide6.QtCore import QObject

class MyService(QObject):
    def __init__(self, main_window):
        super().__init__(main_window)
        self.main_window = main_window
    
    def process_data(self, data):
        # Process data
        return processed_data
```

## Migration Guide

### From Original gui_main.py

1. **UI Setup**: Moved to `ui/managers/ui_manager.py`
2. **Database Operations**: Moved to `controllers/database_controller.py`
3. **Authentication**: Moved to `controllers/authentication_controller.py`
4. **Plotting**: Moved to `controllers/plot_controller.py`
5. **Data Management**: Moved to `services/data_service.py`
6. **Icon Setup**: Moved to `services/icon_service.py`
7. **Configuration**: Moved to `config/app_config.py`

### Key Changes

1. **MainWindow Class**: Reduced from 5000+ lines to ~200 lines
2. **Initialization**: Split into logical initialization methods
3. **Signal Connections**: Centralized in `connection_manager.py`
4. **Data Structures**: Managed by `data_service.py`
5. **UI State**: Managed by `ui_manager.py`

## Best Practices

### 1. Controller Pattern
- Controllers handle business logic
- Keep UI code separate from business logic
- Use dependency injection for testability

### 2. Service Pattern
- Services handle data operations
- Stateless where possible
- Single responsibility principle

### 3. Manager Pattern
- Managers handle specific UI aspects
- Coordinate between UI components
- Maintain UI state consistency

### 4. Configuration Management
- Centralize all constants in `app_config.py`
- Use typed configuration classes
- Environment-specific configurations

## Testing Strategy

### Unit Testing
```python
# tests/test_controllers/test_main_controller.py
import unittest
from controllers.main_controller import MainController

class TestMainController(unittest.TestCase):
    def setUp(self):
        self.controller = MainController(mock_main_window)
    
    def test_mode_change(self):
        # Test mode change functionality
        pass
```

### Integration Testing
```python
# tests/test_integration/test_ui_flow.py
import unittest
from main_window import MainWindow

class TestUIFlow(unittest.TestCase):
    def test_section_navigation(self):
        # Test section navigation flow
        pass
```

## Future Enhancements

1. **Plugin Architecture**: Add plugin support for extensibility
2. **State Management**: Implement centralized state management
3. **Event System**: Add event-driven communication between components
4. **Dependency Injection**: Implement DI container for better testability
5. **Configuration UI**: Add runtime configuration management

## Conclusion

This modular architecture provides a solid foundation for maintaining and extending the VAPR-iDEX application. Each component has a clear responsibility, making the codebase more maintainable, testable, and scalable.
