#!/usr/bin/env python3
"""
Test script to verify the pressure absolute logic is working correctly
"""

def test_pressure_calculation():
    """Test the pressure calculation logic"""
    
    # Sample data
    y0_values = [0.1, 0.2, 0.3, 0.4, 0.5]  # Sample y0 values
    atmospheric_pressure = 1013  # mbar
    
    print("Testing Pressure Calculation Logic")
    print("=" * 50)
    print(f"Atmospheric Pressure: {atmospheric_pressure} mbar")
    print(f"Sample y0 values: {y0_values}")
    print()
    
    # Test absolute pressure calculation
    print("ABSOLUTE PRESSURE (is_absolute=True):")
    print("Formula: Vac. Chamber Pressure = y0 * 1000")
    for y0 in y0_values:
        vac_pressure_absolute = y0 * 1000
        print(f"  y0={y0} -> Vac. Chamber Pressure = {vac_pressure_absolute} mbar")
    
    print()
    
    # Test relative pressure calculation  
    print("RELATIVE PRESSURE (is_absolute=False):")
    print("Formula: Vac. Chamber Pressure = y0 * 1000 + atmospheric_pressure")
    for y0 in y0_values:
        vac_pressure_relative = y0 * 1000 + atmospheric_pressure
        print(f"  y0={y0} -> Vac. Chamber Pressure = {vac_pressure_relative} mbar")
    
    print()
    print("DIFFERENCE (Relative - Absolute):")
    for y0 in y0_values:
        absolute = y0 * 1000
        relative = y0 * 1000 + atmospheric_pressure
        difference = relative - absolute
        print(f"  y0={y0} -> Difference = {difference} mbar (should always be {atmospheric_pressure})")
    
    print()
    print("✅ Logic Test Complete!")
    print("The difference should always equal the atmospheric pressure (1013 mbar)")

def simulate_dialog_selection():
    """Simulate the dialog selection logic"""
    
    print("\nSimulating Dialog Selection Logic")
    print("=" * 50)
    
    # Simulate toggle switch selections
    selections = [
        ("Yes", 0, True),   # Yes = index 0 = is_absolute True
        ("No", 1, False)    # No = index 1 = is_absolute False
    ]
    
    for mode_name, index, expected_is_absolute in selections:
        # This mimics the logic in _show_pressure_absolute_dialog
        is_absolute = index == 0
        
        print(f"User selects: '{mode_name}' (index {index})")
        print(f"  -> is_absolute = {is_absolute}")
        print(f"  -> Expected: {expected_is_absolute}")
        print(f"  -> ✅ Correct!" if is_absolute == expected_is_absolute else "  -> ❌ Error!")
        print()

if __name__ == "__main__":
    test_pressure_calculation()
    simulate_dialog_selection()
