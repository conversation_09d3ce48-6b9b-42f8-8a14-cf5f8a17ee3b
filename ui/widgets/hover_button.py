"""
Custom Hover Button Widget
"""

from PySide6.QtWidgets import QPushButton
from PySide6.QtCore import QSize


class HoverButton(QPushButton):
    """Button with hover size animation effect."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.default_size = QSize(24, 24)
        self.hover_size = QSize(30, 30)  # Bigger size for hover state
        self.setIconSize(self.default_size)

    def enterEvent(self, event):
        """Handle mouse enter event."""
        self.setIconSize(self.hover_size)
        super().enterEvent(event)

    def leaveEvent(self, event):
        """Handle mouse leave event."""
        self.setIconSize(self.default_size)
        super().leaveEvent(event)
