"""
Custom Splash Screen for VAPR-iDEX Application
"""

import os
import logging
from PySide6.QtCore import Qt, QSize, QUrl, QTimer, Signal, QThread
from PySide6.QtGui import QMovie
from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QApplication
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput

from src.utils import get_resource_path
from config.app_config import AppConfig


class InitThread(QThread):
    """Background initialization thread."""
    
    status_update = Signal(str)
    finished = Signal()

    def run(self):
        """Run initialization steps."""
        import time
        
        # Simulate initialization steps
        steps = [
            "Loading configuration...",
            "Initializing serial connection...",
            "Setting up GUI components...",
            "Preparing system state..."
        ]

        for step in steps:
            self.status_update.emit(step)
            time.sleep(0.8)  # Simulate work being done

        self.finished.emit()


class CustomSplashScreen(QWidget):
    """Custom splash screen with animation and sound."""
    
    initialization_finished = Signal()
    
    def __init__(self):
        super().__init__()
        self._setup_window()
        self._setup_media_player()
        self._setup_ui()
        self._setup_initialization()
        
    def _setup_window(self):
        """Configure window properties."""
        self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # Set size and position
        self.resize(AppConfig.SPLASH_SIZE)
        screen = QApplication.primaryScreen().geometry()
        self.move(
            (screen.width() - self.width()) // 2,
            (screen.height() - self.height()) // 2
        )
    
    def _setup_media_player(self):
        """Initialize media player for boot sound."""
        self.player = QMediaPlayer()
        self.audio_output = QAudioOutput()
        self.player.setAudioOutput(self.audio_output)

        # Load the boot sound
        sound_path = get_resource_path("assets/boot_sound.wav")
        logging.debug(f"Loading boot sound from: {sound_path}")

        if os.path.exists(sound_path):
            url = QUrl.fromLocalFile(sound_path)
            self.player.setSource(url)
            logging.debug("Boot sound file loaded successfully")
        else:
            logging.error(f"Boot sound file not found at: {sound_path}")

        # Set Volume (0.0 to 1.0)
        self.audio_output.setVolume(1.0)

        # Connect error handling signals
        self.player.errorOccurred.connect(self._handle_media_error)
    
    def _setup_ui(self):
        """Setup the user interface."""
        # Create main layout
        layout = QVBoxLayout(self)

        # Create inner widget with border radius
        self.content_widget = QWidget()
        self.content_widget.setObjectName("SplashScreen")
        content_layout = QVBoxLayout(self.content_widget)

        # Style the content widget
        self.content_widget.setStyleSheet(AppConfig.Styles.SPLASH_SCREEN)

        # Add animated GIF
        self.movie_label = QLabel()
        gif_path = get_resource_path("assets/sine_wave_animation.gif")
        self.movie = QMovie(gif_path)
        self.movie.setScaledSize(QSize(400, 200))
        self.movie_label.setMovie(self.movie)

        # Add error handling for movie loading
        if self.movie.isValid():
            self.movie.start()
        else:
            self.movie_label.setText("Failed to load animation")
            logging.error(f"Failed to load animation from: {gif_path}")

        content_layout.addWidget(self.movie_label)

        # Add status label
        self.status_label = QLabel("Initializing...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: #333333; font-size: 12px;")
        content_layout.addWidget(self.status_label)

        # Add the content widget to the main layout
        layout.addWidget(self.content_widget)
    
    def _setup_initialization(self):
        """Setup initialization thread."""
        self.init_thread = InitThread()
        self.init_thread.status_update.connect(self.update_status)
        self.init_thread.finished.connect(self._on_init_finished)
        
        # Start initialization after a short delay
        QTimer.singleShot(100, self.init_thread.start)
    
    def update_status(self, message: str):
        """Update status message."""
        self.status_label.setText(message)

    def _handle_media_error(self, error, error_string: str):
        """Handle media player errors."""
        logging.error(f"Media player error: {error_string}")

    def play_boot_sound(self):
        """Play the boot-up sound."""
        if self.player.source().isValid():
            self.player.play()
            logging.debug("Playing boot sound")
        else:
            logging.error("Invalid media source for boot sound")
    
    def _on_init_finished(self):
        """Handle initialization completion."""
        self.initialization_finished.emit()
