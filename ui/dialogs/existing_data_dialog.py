"""
Existing Data Load Dialog
"""

from PySide6.QtWidgets import QDialog
from PySide6.QtCore import QSize
from PySide6.QtGui import QIcon

from gui.dialog import Ui_Dialog
from src.utils import get_resource_path


class ExistingDataLoadDialog(QDialog):
    """Dialog for loading existing test data."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.ui = Ui_Dialog()
        self.ui.setupUi(self)
        self._setup_icons()
    
    def _setup_icons(self):
        """Setup icons for dialog buttons."""
        # Load file icon for saved test data
        browse_file_icon = QIcon()
        browse_file_icon_path = get_resource_path("assets/browse_file_icon.png")
        browse_file_icon.addFile(browse_file_icon_path, QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        self.ui.btnBrowseJSONFile.setIcon(browse_file_icon)
        self.ui.btnBrowseJSONFile.setIconSize(QSize(80, 80))

        # Database icon
        database_icon = QIcon()
        database_icon_path = get_resource_path("assets/database_icon.png")
        database_icon.addFile(database_icon_path, QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        self.ui.btnBrowseDatabase.setIcon(database_icon)
        self.ui.btnBrowseDatabase.setIconSize(QSize(80, 80))
