"""
Menu Manager
Handles menu bar setup and management.
"""

from PySide6.QtWidgets import Q<PERSON>oolBar, QWidget, QHBoxLayout
from PySide6.QtCore import QObject
from PySide6.QtGui import QIcon

from gui import MultiStateToggleSwitch
from ui.widgets import Ho<PERSON><PERSON>utton
from config.app_config import AppConfig
from src.utils import get_resource_path


class MenuManager(QObject):
    """Manager for menu bar and toolbar setup."""
    
    def __init__(self, main_window):
        super().__init__(main_window)
        self.main_window = main_window
        self.ui = main_window.ui
        
    def setup_menu(self):
        """Setup menu bar and toolbar."""
        self._setup_toolbar()
        self._setup_mode_toggle()
        self._setup_database_button()
        self._setup_plot_settings_toggle()
        
    def _setup_toolbar(self):
        """Setup main toolbar."""
        self.toolbar = QToolBar()
        self.toolbar.setMovable(False)
        self.toolbar.setFloatable(False)
        self.main_window.addToolBar(self.toolbar)
        
        # Style the toolbar
        self.toolbar.setStyleSheet("""
            QToolBar {
                spacing: 0px;
                border: none;
                background-color: transparent;
            }
        """)
    
    def _setup_mode_toggle(self):
        """Setup mode toggle switch."""
        # Add spacer to push toggle switch to center
        spacer = QWidget()
        spacer.setSizePolicy(
            self.main_window.sizePolicy().Expanding, 
            self.main_window.sizePolicy().Expanding
        )
        self.toolbar.addWidget(spacer)
        
        # Add toggle switch
        self.mode_toggle = MultiStateToggleSwitch(["New Test"])
        self.mode_toggle.modeChanged.connect(self.main_window.handle_mode_change)
        self.toolbar.addWidget(self.mode_toggle)
        
        # Store reference in main window
        self.main_window.mode_toggle = self.mode_toggle
    
    def _setup_database_button(self):
        """Setup database connection button."""
        self.database_button = HoverButton(self.main_window)
        self.database_button.setIcon(QIcon(get_resource_path("assets/database_icon.png")))
        self.database_button.setToolTip("Connect to Database")
        self.database_button.clicked.connect(self._handle_database_connection)
        self.database_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.1);
                border-radius: 3px;
            }
        """)
        self.database_button_action = self.toolbar.addWidget(self.database_button)
        
        # Add spacer after the toggle switch
        spacer2 = QWidget()
        spacer2.setSizePolicy(
            self.main_window.sizePolicy().Expanding, 
            self.main_window.sizePolicy().Expanding
        )
        self.toolbar.addWidget(spacer2)
    
    def _setup_plot_settings_toggle(self):
        """Setup plot settings toggle switch."""
        self.mode_plot_settings = MultiStateToggleSwitch(['Temperature', 'Pressure'])
        self.mode_plot_settings.modeChanged.connect(self.main_window.handle_mode_change)
        self.mode_plot_settings.background_color = AppConfig.Colors.PRIMARY
        self.mode_plot_settings.handle_color = AppConfig.Colors.SECONDARY

        plot_settings_layout = QHBoxLayout(self.ui.plotSettingsTabSlider)
        plot_settings_layout.addWidget(self.mode_plot_settings)

        # Store reference in main window
        self.main_window.mode_plot_settings = self.mode_plot_settings
    
    def _handle_database_connection(self):
        """Handle database connection button click."""
        if self.main_window.db_controller.initialize_connection():
            # Update mode toggle to include database option
            self.mode_toggle.update_modes(["New Test", "Existing Test", "Database"])
            
            # Load initial data
            self.main_window.db_controller.load_all_records()
            
            # Hide the database button
            self.database_button_action.setVisible(False)
    
    def update_mode_toggle_modes(self, modes):
        """Update the modes available in the toggle switch."""
        self.mode_toggle.update_modes(modes)
