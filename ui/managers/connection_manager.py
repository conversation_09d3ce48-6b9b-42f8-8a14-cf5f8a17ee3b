"""
Connection Manager
Handles signal-slot connections for the UI.
"""

from PySide6.QtCore import QObject
from PySide6.QtGui import QKeySequence
from PySide6.QtCore import Qt


class ConnectionManager(QObject):
    """Manager for setting up signal-slot connections."""
    
    def __init__(self, main_window):
        super().__init__(main_window)
        self.main_window = main_window
        self.ui = main_window.ui
        
    def setup_all_connections(self):
        """Setup all signal-slot connections."""
        self._setup_section_connections()
        self._setup_plot_connections()
        self._setup_navigation_connections()
        self._setup_data_connections()
        self._setup_photo_connections()
        self._setup_database_connections()
        
    def _setup_section_connections(self):
        """Setup section navigation connections."""
        # Main section buttons
        self.ui.btnTestPrereq.clicked.connect(
            lambda: self.main_window.handle_section_change("Test Prerequisite")
        )
        self.ui.btnHtrOp.clicked.connect(
            lambda: self.main_window.handle_section_change("Heater Operation")
        )
        self.ui.btnPstTestAn.clicked.connect(
            lambda: self.main_window.handle_section_change("Post Test Analysis")
        )
        self.ui.btnPlots.clicked.connect(
            lambda: self.main_window.handle_section_change("PlotWindow")
        )
        self.ui.btnPerformance.clicked.connect(
            lambda: self.main_window.handle_section_change("Performance")
        )
        self.ui.btnTestAuthorization.clicked.connect(
            lambda: self.ui.contentStack.setCurrentWidget(self.ui.testAuthorization)
        )
        
        # Subsection buttons
        self.ui.btnBasicInfo.clicked.connect(
            lambda: self._update_content(self.ui.basicInformation)
        )
        self.ui.btnSysSpec.clicked.connect(
            lambda: self._update_content(self.ui.systemSpecification)
        )
        self.ui.btnPropSpec.clicked.connect(
            lambda: self._update_content(self.ui.propellantSpecification)
        )
        self.ui.btnCatSpec.clicked.connect(
            lambda: self._update_content(self.ui.catalystSpecification)
        )
        self.ui.btnCompDet.clicked.connect(
            lambda: self._update_content(self.ui.componentDetails)
        )
        self.ui.btnTestDet.clicked.connect(
            lambda: self._update_content(self.ui.testDetails)
        )
        
        # Heater operation subsections
        self.ui.btnHtrInfo.clicked.connect(
            lambda: self._update_content(self.ui.heaterInformation)
        )
        self.ui.btnHtrCyc.clicked.connect(
            lambda: self._update_content(self.ui.heaterCycles)
        )
        
        # Post test analysis subsections
        self.ui.btnPstTestObs.clicked.connect(
            lambda: self._update_content(self.ui.postTestingObs)
        )
        self.ui.btnCatPostAn.clicked.connect(
            lambda: self._update_content(self.ui.catalystPostAna)
        )
        self.ui.btnPropPostAn.clicked.connect(
            lambda: self._update_content(self.ui.propellantPostAn)
        )
    
    def _setup_plot_connections(self):
        """Setup plot-related connections."""
        # Temperature matrix button
        self.ui.btnTempMatrix.clicked.connect(
            self.main_window.main_controller.handle_temp_matrix_clicked
        )
        
        # Plot buttons
        self.ui.pressurePlotBtn.clicked.connect(
            self.main_window.plot_controller.create_pressure_plot
        )
        self.ui.tempPlotBtn.clicked.connect(
            self.main_window.plot_controller.create_temperature_plot
        )
        
        # Combo box changes
        self.ui.comboBoxYAxisTemp.itemCheckStateChanged.connect(
            self.main_window.plot_controller.create_temperature_plot
        )
        self.ui.comboBoxYAxisPressure.itemCheckStateChanged.connect(
            self.main_window.plot_controller.create_pressure_plot
        )
        
        # Include in report button
        self.ui.btnIncludeInReport.clicked.connect(
            self.main_window.plot_controller.handle_plot_inclusion
        )
        
        # Plot title changes
        self.ui.lnEditPlotTitle.editingFinished.connect(
            self.main_window.plot_controller.plot_with_title
        )
        
        # Back to plot button
        self.ui.btnBckToPlot.clicked.connect(
            lambda: self.ui.contentStack.setCurrentWidget(self.ui.plots)
        )
        
        # Maximum temperatures plot
        self.ui.btnMaxTempsPlot.clicked.connect(
            self.main_window.plot_controller.generate_max_temperatures_plot
        )
    
    def _setup_navigation_connections(self):
        """Setup navigation connections."""
        # Next and back buttons
        self.ui.nextBtn.setShortcut(QKeySequence(Qt.Key_Right))
        self.ui.backBtn.setShortcut(QKeySequence(Qt.Key_Left))
        self.ui.nextBtn.clicked.connect(
            lambda: self.main_window.main_controller.change_page(next=True)
        )
        self.ui.backBtn.clicked.connect(
            lambda: self.main_window.main_controller.change_page(next=False)
        )
    
    def _setup_data_connections(self):
        """Setup data-related connections."""
        # Performance calculation
        self.ui.btnCalculate.clicked.connect(
            self.main_window.main_controller.calculate_performance
        )
        
        # Report generation
        self.ui.showReport.clicked.connect(
            self.main_window.main_controller.generate_report
        )
        
        # Save to database
        self.ui.saveDataToDatabase.clicked.connect(
            self.main_window.main_controller.save_to_database
        )
        
        # Update table button
        self.ui.btnUpdateTable.clicked.connect(
            self.main_window.main_controller.update_table
        )
    
    def _setup_photo_connections(self):
        """Setup photo selection connections."""
        for photo_id, widgets in self.main_window.photo_widgets.items():
            widgets['button'].clicked.connect(
                lambda checked, pid=photo_id: self.main_window.main_controller.select_photo(pid)
            )
    
    def _setup_database_connections(self):
        """Setup database-related connections."""
        # Search connections
        if hasattr(self.ui, 'test_no'):
            self.ui.test_no.textChanged.connect(
                self.main_window.db_controller.trigger_delayed_search
            )
        if hasattr(self.ui, 'catalyst_name'):
            self.ui.catalyst_name.textChanged.connect(
                self.main_window.db_controller.trigger_delayed_search
            )
        if hasattr(self.ui, 'propellant_ri'):
            self.ui.propellant_ri.valueChanged.connect(
                self.main_window.db_controller.trigger_delayed_search
            )
        if hasattr(self.ui, 'tank_temp'):
            self.ui.tank_temp.valueChanged.connect(
                self.main_window.db_controller.trigger_delayed_search
            )
        
        # Table double click
        if hasattr(self.ui, 'results_table_2'):
            self.ui.results_table_2.doubleClicked.connect(
                self.main_window.db_controller.load_selected_test
            )
    
    def _update_content(self, widget):
        """Update content stack to show specified widget."""
        self.ui.contentStack.setCurrentWidget(widget)
        
        # Update button states
        current_page = widget.objectName()
        self.main_window.ui_manager.update_button_states(current_page)
        
        # Update section header
        if current_page in self.main_window.ui_manager.section_headers:
            self.ui.lblCurentSection.setText(
                self.main_window.ui_manager.section_headers[current_page]
            )
