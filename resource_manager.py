"""
Resource Manager for the VAPR application
Handles resource paths and ensures resources are properly bundled with PyInstaller
"""

import os
import sys
from PySide6.QtWidgets import QComboBox, QApplication
from PySide6.QtGui import QIcon, QPixmap

def get_resource_path(relative_path):
    """
    Get the absolute path to a resource, works for development and for PyInstaller
    
    Args:
        relative_path: The path relative to the application root
        
    Returns:
        The absolute path to the resource
    """
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        # We are not running in a PyInstaller bundle, use the script's directory
        base_path = os.path.abspath(".")
        
    return os.path.join(base_path, relative_path)

def fix_combo_box_arrows():
    """
    Fix the down arrow in combo boxes by applying a dynamic style that uses
    the correct resource path for the down-arrow.png file
    """
    # Get the correct path to the down arrow image
    down_arrow_path = get_resource_path("assets/down-arrow.png").replace('\\', '/')
    print(f"Using down arrow from: {down_arrow_path}")
    
    # Create a style sheet that uses the correct path
    down_arrow_style = """
        QComboBox::down-arrow {
            image: url("%s");
            width: 16px;
            height: 16px;
        }
        QComboBox::drop-down {
            border: none;
            background: transparent;
            width: 20px;
            margin-right: 5px;
        }
    """ % down_arrow_path
    
    # Monkey patch QComboBox to apply this style to all combo boxes
    original_init = QComboBox.__init__
    
    def new_init(self, *args, **kwargs):
        original_init(self, *args, **kwargs)
        current_style = self.styleSheet()
        self.setStyleSheet(current_style + down_arrow_style)
    
    QComboBox.__init__ = new_init
    
    print("Applied down arrow fix to all combo boxes")

def get_icon(icon_name):
    """
    Get an icon from the assets directory
    
    Args:
        icon_name: The name of the icon file (e.g., 'icon.ico')
        
    Returns:
        A QIcon object
    """
    icon_path = get_resource_path(f"assets/{icon_name}")
    return QIcon(icon_path)

def get_pixmap(image_name):
    """
    Get a pixmap from the assets directory
    
    Args:
        image_name: The name of the image file (e.g., 'down-arrow.png')
        
    Returns:
        A QPixmap object
    """
    image_path = get_resource_path(f"assets/{image_name}")
    return QPixmap(image_path)

def initialize_resources():
    """
    Initialize all resources for the application
    """
    # Fix combo box arrows
    fix_combo_box_arrows()
    
    # Set application icon
    app = QApplication.instance()
    if app:
        app.setWindowIcon(get_icon("icon.ico"))
    
    print("Resources initialized successfully")

if __name__ == "__main__":
    # Test the resource manager
    app = QApplication(sys.argv)
    initialize_resources()
    sys.exit(app.exec_())
