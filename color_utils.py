"""
Utility functions for color management in plots
"""

def assign_color(column, color_dict, color_palette):
    """
    Assign a color to a column name, creating a consistent mapping
    
    Args:
        column: The column name to assign a color to
        color_dict: Dictionary mapping column names to colors
        color_palette: List of colors to choose from
        
    Returns:
        The color for the column
    """
    # Normalize the column name to handle case and whitespace variations
    if isinstance(column, str):
        normalized_column = column.lower().strip()
    else:
        normalized_column = str(column).lower().strip()
        
    # Special case for 'Maximum Temperature'
    if normalized_column == 'maximum temperature' or column == 'Maximum Temperature':
        if 'Maximum Temperature' not in color_dict:
            color_dict['Maximum Temperature'] = '#9d004e'  # Keep the original color
        return color_dict['Maximum Temperature']
        
    # Check if we already have a color assigned for this column
    if normalized_column in color_dict:
        return color_dict[normalized_column]
        
    # If not, assign a new color from the palette
    color_index = len(color_dict) % len(color_palette)
    new_color = color_palette[color_index]
    
    # Store the color assignment
    color_dict[normalized_column] = new_color
    
    # Also store with the original column name for backward compatibility
    if normalized_column != column and isinstance(column, str):
        color_dict[column] = new_color
        
    print(f"Assigned color {new_color} to column {column}")
    return new_color
