"""
Utility functions for color management in plots
"""

from color_manager import color_manager

# Default bright color palette - optimized for visibility
DEFAULT_COLOR_PALETTE = [
    '#01a5e3',  # Bright blue
    '#32c133',  # Bright green
    '#fb6238',  # Bright orange
    '#ee0100',  # Bright red
    '#f7d02e',  # Bright yellow
    '#ff7f0e',  # Safety orange
    '#2ca02c',  # Bright green variant
    '#d62728',  # Bright red variant
    '#9467bd',  # Bright purple
    '#17becf',  # Bright cyan
    '#1f77b4',  # Medium blue
    '#bcbd22',  # Olive green
    '#8c564b',  # <PERSON>
    '#e377c2',  # Pink
    '#7f7f7f',  # Gray
    '#9d004e',  # Magenta (darker but still visible)
    '#165152'   # Teal (darker but still visible)
]

def assign_color(column, color_dict=None, color_palette=None):
    """
    Assign a color to a column name using centralized color manager.

    Args:
        column: The column name to assign a color to
        color_dict: Dictionary mapping column names to colors (deprecated, kept for compatibility)
        color_palette: List of colors to choose from (deprecated, kept for compatibility)

    Returns:
        The color for the column
    """
    # Use centralized color manager for consistent colors across the application
    return color_manager.get_color(column)
