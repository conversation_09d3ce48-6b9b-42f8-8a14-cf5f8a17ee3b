#!/usr/bin/env python3
"""
Test script to verify color consistency across all components
"""

import sys
import os

# Add the current directory to the path
sys.path.append(os.path.dirname(__file__))

from color_manager import color_manager
from color_utils import assign_color

def test_color_consistency():
    """Test color consistency across all components"""
    print("=" * 70)
    print("TESTING COLOR CONSISTENCY ACROSS ALL COMPONENTS")
    print("=" * 70)
    
    # Clear any existing assignments to start fresh
    color_manager.clear_assignments()
    
    # Your actual column names from the screenshot
    test_columns = [
        "Tank Bottom (°C)",
        "Tank Flange (°C)", 
        "Thruster ka Flange (°C)",
        "Nozzle ki Convergent (°C)",
        "Nozzle ki Exit (°C)",
        "Tank Mid (°C)",
        "Tank ka Lid (°C)",
        "Thruster ka Chamber (°C)"
    ]
    
    print("\n1. Testing ColorManager direct access:")
    print("-" * 50)
    colors_direct = {}
    for column in test_columns:
        color = color_manager.get_color(column)
        colors_direct[column] = color
        print(f"  {column}: {color}")
    
    print("\n2. Testing color_utils.assign_color:")
    print("-" * 50)
    colors_utils = {}
    dummy_dict = {}  # Won't be used but kept for compatibility
    for column in test_columns:
        color = assign_color(column, dummy_dict)
        colors_utils[column] = color
        print(f"  {column}: {color}")
    
    print("\n3. Testing PlotManager.assign_color:")
    print("-" * 50)
    try:
        # Import PlotManager
        sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'visualization'))
        from plot_manager import PlotManager
        
        plot_manager = PlotManager()
        colors_plot = {}
        for column in test_columns:
            color = plot_manager.assign_color(column)
            colors_plot[column] = color
            print(f"  {column}: {color}")
    except Exception as e:
        print(f"  ❌ Error testing PlotManager: {e}")
        colors_plot = colors_direct  # Fallback
    
    print("\n4. Testing TemperatureSelectionWidget.get_column_color:")
    print("-" * 50)
    try:
        # Import TemperatureSelectionWidget
        sys.path.append(os.path.join(os.path.dirname(__file__), 'gui', 'widgets'))
        from temperature_selection import TemperatureSelectionWidget
        
        temp_widget = TemperatureSelectionWidget()
        colors_widget = {}
        for column in test_columns:
            color = temp_widget.get_column_color(column)
            colors_widget[column] = color
            print(f"  {column}: {color}")
    except Exception as e:
        print(f"  ❌ Error testing TemperatureSelectionWidget: {e}")
        colors_widget = colors_direct  # Fallback
    
    print("\n5. Verifying consistency across all components:")
    print("-" * 50)
    all_consistent = True
    
    for column in test_columns:
        direct_color = colors_direct[column]
        utils_color = colors_utils[column]
        plot_color = colors_plot.get(column, direct_color)
        widget_color = colors_widget.get(column, direct_color)
        
        if direct_color == utils_color == plot_color == widget_color:
            print(f"  ✅ CONSISTENT: {column} -> {direct_color}")
        else:
            print(f"  ❌ INCONSISTENT: {column}")
            print(f"     Direct:  {direct_color}")
            print(f"     Utils:   {utils_color}")
            print(f"     Plot:    {plot_color}")
            print(f"     Widget:  {widget_color}")
            all_consistent = False
    
    print("\n6. Checking for duplicate colors:")
    print("-" * 50)
    all_colors = list(colors_direct.values())
    unique_colors = set(all_colors)
    
    if len(unique_colors) == len(test_columns):
        print(f"  ✅ All {len(test_columns)} colors are unique!")
    else:
        print(f"  ❌ Found duplicate colors!")
        from collections import Counter
        color_counts = Counter(all_colors)
        duplicates = {color: count for color, count in color_counts.items() if count > 1}
        for color, count in duplicates.items():
            columns_with_color = [col for col, c in colors_direct.items() if c == color]
            print(f"    Color {color} used {count} times: {columns_with_color}")
    
    print("\n7. Expected color assignments for your data:")
    print("-" * 50)
    for i, (column, color) in enumerate(colors_direct.items(), 1):
        print(f"  {i}. {column}: {color}")
    
    print("\n" + "=" * 70)
    print("SUMMARY")
    print("=" * 70)
    
    if all_consistent and len(unique_colors) == len(test_columns):
        print("✅ SUCCESS: All components are using consistent, unique colors!")
        print("   - ColorManager is working correctly")
        print("   - All components use the same color assignments")
        print("   - Each column has a unique color")
        print("\nAfter restarting your application, you should see:")
        print("- Tank Bottom (°C): Bright blue")
        print("- Tank Flange (°C): Bright green")
        print("- Thruster ka Flange (°C): Bright orange")
        print("- Nozzle ki Convergent (°C): Bright red")
        print("- Nozzle ki Exit (°C): Bright yellow")
        print("- Tank Mid (°C): Bright purple")
        print("- Tank ka Lid (°C): Bright cyan")
        print("- Thruster ka Chamber (°C): Safety orange")
        return True
    else:
        print("❌ ISSUES FOUND:")
        if not all_consistent:
            print("   - Components are not using consistent colors")
        if len(unique_colors) != len(test_columns):
            print("   - Some colors are duplicated")
        return False

if __name__ == "__main__":
    success = test_color_consistency()
    exit(0 if success else 1)
