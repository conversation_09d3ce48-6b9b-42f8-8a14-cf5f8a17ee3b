('D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\build\\VAPR_iDEX\\PYZ-00.pyz',
 [('PIL',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PySide6',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PySide6\\__init__.py',
   'PYMODULE'),
  ('PySide6.support',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PySide6\\support\\__init__.py',
   'PYMODULE'),
  ('PySide6.support.deprecated',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PySide6\\support\\deprecated.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_colorize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_colorize.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_ios_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_ios_support.py',
   'PYMODULE'),
  ('_markupbase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_markupbase.py',
   'PYMODULE'),
  ('_opcode_metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_opcode_metadata.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydatetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils._win32',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\_win32.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils.tempfile',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\tempfile.py',
   'PYMODULE'),
  ('_pyrepl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\__init__.py',
   'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.commands',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\commands.py',
   'PYMODULE'),
  ('_pyrepl.completing_reader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('_pyrepl.console',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\console.py',
   'PYMODULE'),
  ('_pyrepl.curses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\curses.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.input',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\input.py',
   'PYMODULE'),
  ('_pyrepl.keymap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\keymap.py',
   'PYMODULE'),
  ('_pyrepl.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\main.py',
   'PYMODULE'),
  ('_pyrepl.pager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\pager.py',
   'PYMODULE'),
  ('_pyrepl.reader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\reader.py',
   'PYMODULE'),
  ('_pyrepl.readline',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\readline.py',
   'PYMODULE'),
  ('_pyrepl.simple_interact',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.trace',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\trace.py',
   'PYMODULE'),
  ('_pyrepl.types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\types.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\utils.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\base64.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bdb.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\calendar.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\cmd.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\codeop.py',
   'PYMODULE'),
  ('color_manager',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\color_manager.py',
   'PYMODULE'),
  ('color_utils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\color_utils.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\colorsys.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\contextvars.py',
   'PYMODULE'),
  ('contourpy',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\contourpy\\__init__.py',
   'PYMODULE'),
  ('contourpy._version',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\contourpy\\_version.py',
   'PYMODULE'),
  ('contourpy.array',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\contourpy\\array.py',
   'PYMODULE'),
  ('contourpy.chunk',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\contourpy\\chunk.py',
   'PYMODULE'),
  ('contourpy.convert',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\contourpy\\convert.py',
   'PYMODULE'),
  ('contourpy.dechunk',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\contourpy\\dechunk.py',
   'PYMODULE'),
  ('contourpy.enum_util',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\contourpy\\enum_util.py',
   'PYMODULE'),
  ('contourpy.typecheck',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\contourpy\\typecheck.py',
   'PYMODULE'),
  ('contourpy.types',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\contourpy\\types.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\copy.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('curses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\curses\\__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\curses\\has_key.py',
   'PYMODULE'),
  ('cycler',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\cycler\\__init__.py',
   'PYMODULE'),
  ('data_recovery',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\data_recovery\\__init__.py',
   'PYMODULE'),
  ('data_recovery.data_recovery_handler',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\data_recovery\\data_recovery_handler.py',
   'PYMODULE'),
  ('data_recovery.plot_utils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\data_recovery\\plot_utils.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\datetime.py',
   'PYMODULE'),
  ('dateutil',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\dis.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\doctest.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\__init__.py',
   'PYMODULE'),
  ('email.mime.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\base.py',
   'PYMODULE'),
  ('email.mime.multipart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\multipart.py',
   'PYMODULE'),
  ('email.mime.nonmultipart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\nonmultipart.py',
   'PYMODULE'),
  ('email.mime.text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\text.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fileinput',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\fileinput.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('fpdf',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\fpdf\\__init__.py',
   'PYMODULE'),
  ('fpdf.fonts',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\fpdf\\fonts.py',
   'PYMODULE'),
  ('fpdf.fpdf',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\fpdf\\fpdf.py',
   'PYMODULE'),
  ('fpdf.html',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\fpdf\\html.py',
   'PYMODULE'),
  ('fpdf.php',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\fpdf\\php.py',
   'PYMODULE'),
  ('fpdf.py3k',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\fpdf\\py3k.py',
   'PYMODULE'),
  ('fpdf.template',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\fpdf\\template.py',
   'PYMODULE'),
  ('fpdf.ttfonts',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\fpdf\\ttfonts.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\glob.py',
   'PYMODULE'),
  ('gui',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\gui\\__init__.py',
   'PYMODULE'),
  ('gui.checkableComboBox',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\gui\\checkableComboBox.py',
   'PYMODULE'),
  ('gui.custom_double_spin_box',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\gui\\custom_double_spin_box.py',
   'PYMODULE'),
  ('gui.custom_splash_screen',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\gui\\custom_splash_screen.py',
   'PYMODULE'),
  ('gui.dialog',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\gui\\dialog\\__init__.py',
   'PYMODULE'),
  ('gui.dialog.custom_plot_settings',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\gui\\dialog\\custom_plot_settings.py',
   'PYMODULE'),
  ('gui.dialog.data_visualization',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\gui\\dialog\\data_visualization.py',
   'PYMODULE'),
  ('gui.dialog.existing_data_load_dialog',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\gui\\dialog\\existing_data_load_dialog.py',
   'PYMODULE'),
  ('gui.dialog.photo_preview_dialog',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\gui\\dialog\\photo_preview_dialog.py',
   'PYMODULE'),
  ('gui.dialog.report_preview_dialog',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\gui\\dialog\\report_preview_dialog.py',
   'PYMODULE'),
  ('gui.dialog.temperature_data_config_dialog',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\gui\\dialog\\temperature_data_config_dialog.py',
   'PYMODULE'),
  ('gui.dialog.ui_data_column_selector_dialog',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\gui\\dialog\\ui_data_column_selector_dialog.py',
   'PYMODULE'),
  ('gui.dialog.ui_existing_data_load_dialog',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\gui\\dialog\\ui_existing_data_load_dialog.py',
   'PYMODULE'),
  ('gui.dialog.ui_plot_settings',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\gui\\dialog\\ui_plot_settings.py',
   'PYMODULE'),
  ('gui.dialog.ui_pressure_absolute_dialog',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\gui\\dialog\\ui_pressure_absolute_dialog.py',
   'PYMODULE'),
  ('gui.toggle_switch',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\gui\\toggle_switch.py',
   'PYMODULE'),
  ('gui.ui_VAPR_iDEX_Thruster_Analysis',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\gui\\ui_VAPR_iDEX_Thruster_Analysis.py',
   'PYMODULE'),
  ('gui.widgets',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\gui\\widgets\\__init__.py',
   'PYMODULE'),
  ('gui.widgets.plot_canvas',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\gui\\widgets\\plot_canvas.py',
   'PYMODULE'),
  ('gui.widgets.temperature_selection',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\gui\\widgets\\temperature_selection.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('html.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\html\\parser.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\server.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('joblib',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\__init__.py',
   'PYMODULE'),
  ('joblib._cloudpickle_wrapper',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\_cloudpickle_wrapper.py',
   'PYMODULE'),
  ('joblib._dask',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\_dask.py',
   'PYMODULE'),
  ('joblib._memmapping_reducer',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\_memmapping_reducer.py',
   'PYMODULE'),
  ('joblib._multiprocessing_helpers',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\_multiprocessing_helpers.py',
   'PYMODULE'),
  ('joblib._parallel_backends',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\_parallel_backends.py',
   'PYMODULE'),
  ('joblib._store_backends',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\_store_backends.py',
   'PYMODULE'),
  ('joblib._utils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\_utils.py',
   'PYMODULE'),
  ('joblib.backports',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\backports.py',
   'PYMODULE'),
  ('joblib.compressor',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\compressor.py',
   'PYMODULE'),
  ('joblib.disk',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\disk.py',
   'PYMODULE'),
  ('joblib.executor',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\executor.py',
   'PYMODULE'),
  ('joblib.externals',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\externals\\__init__.py',
   'PYMODULE'),
  ('joblib.externals.cloudpickle',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\externals\\cloudpickle\\__init__.py',
   'PYMODULE'),
  ('joblib.externals.cloudpickle.cloudpickle',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\externals\\cloudpickle\\cloudpickle.py',
   'PYMODULE'),
  ('joblib.externals.loky',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\externals\\loky\\__init__.py',
   'PYMODULE'),
  ('joblib.externals.loky._base',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\externals\\loky\\_base.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\__init__.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend._posix_reduction',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\_posix_reduction.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend._win_reduction',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\_win_reduction.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend.context',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\context.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend.fork_exec',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\fork_exec.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend.popen_loky_posix',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\popen_loky_posix.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend.popen_loky_win32',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\popen_loky_win32.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend.process',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\process.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend.queues',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\queues.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend.reduction',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\reduction.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend.resource_tracker',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\resource_tracker.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend.spawn',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\spawn.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend.synchronize',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\synchronize.py',
   'PYMODULE'),
  ('joblib.externals.loky.backend.utils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\externals\\loky\\backend\\utils.py',
   'PYMODULE'),
  ('joblib.externals.loky.cloudpickle_wrapper',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\externals\\loky\\cloudpickle_wrapper.py',
   'PYMODULE'),
  ('joblib.externals.loky.initializers',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\externals\\loky\\initializers.py',
   'PYMODULE'),
  ('joblib.externals.loky.process_executor',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\externals\\loky\\process_executor.py',
   'PYMODULE'),
  ('joblib.externals.loky.reusable_executor',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\externals\\loky\\reusable_executor.py',
   'PYMODULE'),
  ('joblib.func_inspect',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\func_inspect.py',
   'PYMODULE'),
  ('joblib.hashing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\hashing.py',
   'PYMODULE'),
  ('joblib.logger',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\logger.py',
   'PYMODULE'),
  ('joblib.memory',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\memory.py',
   'PYMODULE'),
  ('joblib.numpy_pickle',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\numpy_pickle.py',
   'PYMODULE'),
  ('joblib.numpy_pickle_compat',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\numpy_pickle_compat.py',
   'PYMODULE'),
  ('joblib.numpy_pickle_utils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\numpy_pickle_utils.py',
   'PYMODULE'),
  ('joblib.parallel',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\parallel.py',
   'PYMODULE'),
  ('joblib.pool',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\joblib\\pool.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('kiwisolver',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\kiwisolver\\__init__.py',
   'PYMODULE'),
  ('kiwisolver.exceptions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\kiwisolver\\exceptions.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\lzma.py',
   'PYMODULE'),
  ('matplotlib',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\__init__.py',
   'PYMODULE'),
  ('matplotlib._afm',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\_afm.py',
   'PYMODULE'),
  ('matplotlib._api',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\_api\\__init__.py',
   'PYMODULE'),
  ('matplotlib._api.deprecation',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\_api\\deprecation.py',
   'PYMODULE'),
  ('matplotlib._blocking_input',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\_blocking_input.py',
   'PYMODULE'),
  ('matplotlib._cm',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\_cm.py',
   'PYMODULE'),
  ('matplotlib._cm_bivar',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\_cm_bivar.py',
   'PYMODULE'),
  ('matplotlib._cm_listed',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\_cm_listed.py',
   'PYMODULE'),
  ('matplotlib._cm_multivar',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\_cm_multivar.py',
   'PYMODULE'),
  ('matplotlib._color_data',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\_color_data.py',
   'PYMODULE'),
  ('matplotlib._constrained_layout',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\_constrained_layout.py',
   'PYMODULE'),
  ('matplotlib._docstring',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\_docstring.py',
   'PYMODULE'),
  ('matplotlib._enums',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\_enums.py',
   'PYMODULE'),
  ('matplotlib._fontconfig_pattern',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\_fontconfig_pattern.py',
   'PYMODULE'),
  ('matplotlib._layoutgrid',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\_layoutgrid.py',
   'PYMODULE'),
  ('matplotlib._mathtext',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\_mathtext.py',
   'PYMODULE'),
  ('matplotlib._mathtext_data',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\_mathtext_data.py',
   'PYMODULE'),
  ('matplotlib._pylab_helpers',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\_pylab_helpers.py',
   'PYMODULE'),
  ('matplotlib._text_helpers',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\_text_helpers.py',
   'PYMODULE'),
  ('matplotlib._tight_bbox',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\_tight_bbox.py',
   'PYMODULE'),
  ('matplotlib._tight_layout',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\_tight_layout.py',
   'PYMODULE'),
  ('matplotlib._version',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\_version.py',
   'PYMODULE'),
  ('matplotlib.artist',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\artist.py',
   'PYMODULE'),
  ('matplotlib.axes',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\axes\\__init__.py',
   'PYMODULE'),
  ('matplotlib.axes._axes',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\axes\\_axes.py',
   'PYMODULE'),
  ('matplotlib.axes._base',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\axes\\_base.py',
   'PYMODULE'),
  ('matplotlib.axes._secondary_axes',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\axes\\_secondary_axes.py',
   'PYMODULE'),
  ('matplotlib.axis',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\axis.py',
   'PYMODULE'),
  ('matplotlib.backend_bases',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\backend_bases.py',
   'PYMODULE'),
  ('matplotlib.backend_managers',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\backend_managers.py',
   'PYMODULE'),
  ('matplotlib.backend_tools',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\backend_tools.py',
   'PYMODULE'),
  ('matplotlib.backends',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\backends\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_agg',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\backends\\backend_agg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_cairo',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\backends\\backend_cairo.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_qt',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\backends\\backend_qt.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_qt5agg',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\backends\\backend_qt5agg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_qt5cairo',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\backends\\backend_qt5cairo.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_qtagg',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\backends\\backend_qtagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_qtcairo',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\backends\\backend_qtcairo.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\backends\\backend_webagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg_core',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\backends\\backend_webagg_core.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_compat',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\backends\\qt_compat.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_editor',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\backends\\qt_editor\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_editor._formlayout',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\backends\\qt_editor\\_formlayout.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_editor.figureoptions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\backends\\qt_editor\\figureoptions.py',
   'PYMODULE'),
  ('matplotlib.backends.registry',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\backends\\registry.py',
   'PYMODULE'),
  ('matplotlib.bezier',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\bezier.py',
   'PYMODULE'),
  ('matplotlib.category',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\category.py',
   'PYMODULE'),
  ('matplotlib.cbook',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\cbook.py',
   'PYMODULE'),
  ('matplotlib.cm',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\cm.py',
   'PYMODULE'),
  ('matplotlib.collections',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\collections.py',
   'PYMODULE'),
  ('matplotlib.colorbar',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\colorbar.py',
   'PYMODULE'),
  ('matplotlib.colorizer',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\colorizer.py',
   'PYMODULE'),
  ('matplotlib.colors',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\colors.py',
   'PYMODULE'),
  ('matplotlib.container',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\container.py',
   'PYMODULE'),
  ('matplotlib.contour',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\contour.py',
   'PYMODULE'),
  ('matplotlib.dates',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\dates.py',
   'PYMODULE'),
  ('matplotlib.dviread',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\dviread.py',
   'PYMODULE'),
  ('matplotlib.figure',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\figure.py',
   'PYMODULE'),
  ('matplotlib.font_manager',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\font_manager.py',
   'PYMODULE'),
  ('matplotlib.gridspec',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\gridspec.py',
   'PYMODULE'),
  ('matplotlib.hatch',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\hatch.py',
   'PYMODULE'),
  ('matplotlib.image',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\image.py',
   'PYMODULE'),
  ('matplotlib.inset',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\inset.py',
   'PYMODULE'),
  ('matplotlib.layout_engine',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\layout_engine.py',
   'PYMODULE'),
  ('matplotlib.legend',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\legend.py',
   'PYMODULE'),
  ('matplotlib.legend_handler',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\legend_handler.py',
   'PYMODULE'),
  ('matplotlib.lines',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\lines.py',
   'PYMODULE'),
  ('matplotlib.markers',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\markers.py',
   'PYMODULE'),
  ('matplotlib.mathtext',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\mathtext.py',
   'PYMODULE'),
  ('matplotlib.mlab',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\mlab.py',
   'PYMODULE'),
  ('matplotlib.offsetbox',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\offsetbox.py',
   'PYMODULE'),
  ('matplotlib.patches',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\patches.py',
   'PYMODULE'),
  ('matplotlib.path',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\path.py',
   'PYMODULE'),
  ('matplotlib.patheffects',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\patheffects.py',
   'PYMODULE'),
  ('matplotlib.projections',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\projections\\__init__.py',
   'PYMODULE'),
  ('matplotlib.projections.geo',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\projections\\geo.py',
   'PYMODULE'),
  ('matplotlib.projections.polar',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\projections\\polar.py',
   'PYMODULE'),
  ('matplotlib.pylab',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\pylab.py',
   'PYMODULE'),
  ('matplotlib.pyplot',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\pyplot.py',
   'PYMODULE'),
  ('matplotlib.quiver',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\quiver.py',
   'PYMODULE'),
  ('matplotlib.rcsetup',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\rcsetup.py',
   'PYMODULE'),
  ('matplotlib.scale',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\scale.py',
   'PYMODULE'),
  ('matplotlib.spines',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\spines.py',
   'PYMODULE'),
  ('matplotlib.stackplot',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\stackplot.py',
   'PYMODULE'),
  ('matplotlib.streamplot',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\streamplot.py',
   'PYMODULE'),
  ('matplotlib.style',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\style\\__init__.py',
   'PYMODULE'),
  ('matplotlib.style.core',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\style\\core.py',
   'PYMODULE'),
  ('matplotlib.table',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\table.py',
   'PYMODULE'),
  ('matplotlib.texmanager',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\texmanager.py',
   'PYMODULE'),
  ('matplotlib.text',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\text.py',
   'PYMODULE'),
  ('matplotlib.textpath',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\textpath.py',
   'PYMODULE'),
  ('matplotlib.ticker',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\ticker.py',
   'PYMODULE'),
  ('matplotlib.transforms',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\transforms.py',
   'PYMODULE'),
  ('matplotlib.tri',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\tri\\__init__.py',
   'PYMODULE'),
  ('matplotlib.tri._triangulation',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_triangulation.py',
   'PYMODULE'),
  ('matplotlib.tri._tricontour',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_tricontour.py',
   'PYMODULE'),
  ('matplotlib.tri._trifinder',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_trifinder.py',
   'PYMODULE'),
  ('matplotlib.tri._triinterpolate',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_triinterpolate.py',
   'PYMODULE'),
  ('matplotlib.tri._tripcolor',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_tripcolor.py',
   'PYMODULE'),
  ('matplotlib.tri._triplot',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_triplot.py',
   'PYMODULE'),
  ('matplotlib.tri._trirefine',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_trirefine.py',
   'PYMODULE'),
  ('matplotlib.tri._tritools',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_tritools.py',
   'PYMODULE'),
  ('matplotlib.typing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\typing.py',
   'PYMODULE'),
  ('matplotlib.units',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\units.py',
   'PYMODULE'),
  ('matplotlib.widgets',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\matplotlib\\widgets.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('mpl_toolkits', '-', 'PYMODULE'),
  ('mpl_toolkits.mplot3d',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.art3d',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\art3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axes3d',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\axes3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axis3d',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\axis3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.proj3d',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\proj3d.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__main__',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\f2py\\__main__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py._src_pyf',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\f2py\\_src_pyf.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\opcode.py',
   'PYMODULE'),
  ('packaging',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.boxplot',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\boxplot.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.converter',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\converter.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.core',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.groupby',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\groupby.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.hist',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\hist.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.misc',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\misc.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.style',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\style.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.timeseries',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\timeseries.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.tools',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\tools.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\__init__.py',
   'PYMODULE'),
  ('pathlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\_abc.py',
   'PYMODULE'),
  ('pathlib._local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\_local.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pdb.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pickle.py',
   'PYMODULE'),
  ('pickletools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pickletools.py',
   'PYMODULE'),
  ('pkg_resources',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\platform.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\plistlib.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pprint.py',
   'PYMODULE'),
  ('psycopg2',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\psycopg2\\__init__.py',
   'PYMODULE'),
  ('psycopg2._ipaddress',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\psycopg2\\_ipaddress.py',
   'PYMODULE'),
  ('psycopg2._json',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\psycopg2\\_json.py',
   'PYMODULE'),
  ('psycopg2._range',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\psycopg2\\_range.py',
   'PYMODULE'),
  ('psycopg2.extensions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\psycopg2\\extensions.py',
   'PYMODULE'),
  ('psycopg2.extras',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\psycopg2\\extras.py',
   'PYMODULE'),
  ('psycopg2.pool',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\psycopg2\\pool.py',
   'PYMODULE'),
  ('psycopg2.sql',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\psycopg2\\sql.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pyparsing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.common',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.core',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.util',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('pytz',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\random.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\runpy.py',
   'PYMODULE'),
  ('scipy',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\__init__.py',
   'PYMODULE'),
  ('scipy.__config__',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\__config__.py',
   'PYMODULE'),
  ('scipy._distributor_init',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_distributor_init.py',
   'PYMODULE'),
  ('scipy._lib',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\__init__.py',
   'PYMODULE'),
  ('scipy._lib._array_api',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\_array_api.py',
   'PYMODULE'),
  ('scipy._lib._bunch',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\_bunch.py',
   'PYMODULE'),
  ('scipy._lib._ccallback',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\_ccallback.py',
   'PYMODULE'),
  ('scipy._lib._disjoint_set',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\_disjoint_set.py',
   'PYMODULE'),
  ('scipy._lib._docscrape',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\_docscrape.py',
   'PYMODULE'),
  ('scipy._lib._elementwise_iterative_method',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\_elementwise_iterative_method.py',
   'PYMODULE'),
  ('scipy._lib._finite_differences',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\_finite_differences.py',
   'PYMODULE'),
  ('scipy._lib._pep440',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\_pep440.py',
   'PYMODULE'),
  ('scipy._lib._testutils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\_testutils.py',
   'PYMODULE'),
  ('scipy._lib._threadsafety',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\_threadsafety.py',
   'PYMODULE'),
  ('scipy._lib._uarray',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\_uarray\\__init__.py',
   'PYMODULE'),
  ('scipy._lib._uarray._backend',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\_uarray\\_backend.py',
   'PYMODULE'),
  ('scipy._lib._util',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\_util.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat._internal',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\_internal.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._aliases',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._fft',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_fft.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._helpers',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_helpers.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._linalg',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_linalg.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._typing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy._aliases',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy._info',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy._typing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask.array',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\array\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask.array._aliases',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\array\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask.array._info',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\array\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy._aliases',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy._info',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy._typing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy.fft',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\fft.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy.linalg',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\linalg.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.torch',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\torch\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.torch._aliases',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\torch\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.torch._info',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\torch\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._funcs',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\_funcs.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._typing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.framework',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\framework.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.main',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\main.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.models',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\models.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.problem',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\problem.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.settings',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\settings.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.subsolvers',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\subsolvers\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.subsolvers.geometry',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\subsolvers\\geometry.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.subsolvers.optim',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\subsolvers\\optim.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\utils\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils.exceptions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\utils\\exceptions.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils.math',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\utils\\math.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils.versions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\utils\\versions.py',
   'PYMODULE'),
  ('scipy._lib.decorator',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\decorator.py',
   'PYMODULE'),
  ('scipy._lib.deprecation',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\deprecation.py',
   'PYMODULE'),
  ('scipy._lib.doccer',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\doccer.py',
   'PYMODULE'),
  ('scipy._lib.uarray',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\_lib\\uarray.py',
   'PYMODULE'),
  ('scipy.cluster',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\cluster\\__init__.py',
   'PYMODULE'),
  ('scipy.cluster.hierarchy',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\cluster\\hierarchy.py',
   'PYMODULE'),
  ('scipy.cluster.vq',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\cluster\\vq.py',
   'PYMODULE'),
  ('scipy.constants',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\constants\\__init__.py',
   'PYMODULE'),
  ('scipy.constants._codata',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\constants\\_codata.py',
   'PYMODULE'),
  ('scipy.constants._constants',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\constants\\_constants.py',
   'PYMODULE'),
  ('scipy.constants.codata',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\constants\\codata.py',
   'PYMODULE'),
  ('scipy.constants.constants',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\constants\\constants.py',
   'PYMODULE'),
  ('scipy.fft',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\fft\\__init__.py',
   'PYMODULE'),
  ('scipy.fft._backend',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\fft\\_backend.py',
   'PYMODULE'),
  ('scipy.fft._basic',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\fft\\_basic.py',
   'PYMODULE'),
  ('scipy.fft._basic_backend',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\fft\\_basic_backend.py',
   'PYMODULE'),
  ('scipy.fft._fftlog',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\fft\\_fftlog.py',
   'PYMODULE'),
  ('scipy.fft._fftlog_backend',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\fft\\_fftlog_backend.py',
   'PYMODULE'),
  ('scipy.fft._helper',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\fft\\_helper.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\__init__.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.basic',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\basic.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.helper',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\helper.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.realtransforms',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\realtransforms.py',
   'PYMODULE'),
  ('scipy.fft._realtransforms',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\fft\\_realtransforms.py',
   'PYMODULE'),
  ('scipy.fft._realtransforms_backend',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\fft\\_realtransforms_backend.py',
   'PYMODULE'),
  ('scipy.integrate',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\__init__.py',
   'PYMODULE'),
  ('scipy.integrate._bvp',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\_bvp.py',
   'PYMODULE'),
  ('scipy.integrate._cubature',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\_cubature.py',
   'PYMODULE'),
  ('scipy.integrate._ivp',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\_ivp\\__init__.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.base',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\_ivp\\base.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.bdf',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\_ivp\\bdf.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.common',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\_ivp\\common.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.dop853_coefficients',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\_ivp\\dop853_coefficients.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.ivp',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\_ivp\\ivp.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.lsoda',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\_ivp\\lsoda.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.radau',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\_ivp\\radau.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.rk',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\_ivp\\rk.py',
   'PYMODULE'),
  ('scipy.integrate._lebedev',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\_lebedev.py',
   'PYMODULE'),
  ('scipy.integrate._ode',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\_ode.py',
   'PYMODULE'),
  ('scipy.integrate._odepack_py',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\_odepack_py.py',
   'PYMODULE'),
  ('scipy.integrate._quad_vec',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\_quad_vec.py',
   'PYMODULE'),
  ('scipy.integrate._quadpack_py',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\_quadpack_py.py',
   'PYMODULE'),
  ('scipy.integrate._quadrature',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\_quadrature.py',
   'PYMODULE'),
  ('scipy.integrate._rules',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\_rules\\__init__.py',
   'PYMODULE'),
  ('scipy.integrate._rules._base',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\_rules\\_base.py',
   'PYMODULE'),
  ('scipy.integrate._rules._gauss_kronrod',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\_rules\\_gauss_kronrod.py',
   'PYMODULE'),
  ('scipy.integrate._rules._gauss_legendre',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\_rules\\_gauss_legendre.py',
   'PYMODULE'),
  ('scipy.integrate._rules._genz_malik',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\_rules\\_genz_malik.py',
   'PYMODULE'),
  ('scipy.integrate._tanhsinh',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\_tanhsinh.py',
   'PYMODULE'),
  ('scipy.integrate.dop',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\dop.py',
   'PYMODULE'),
  ('scipy.integrate.lsoda',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\lsoda.py',
   'PYMODULE'),
  ('scipy.integrate.odepack',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\odepack.py',
   'PYMODULE'),
  ('scipy.integrate.quadpack',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\quadpack.py',
   'PYMODULE'),
  ('scipy.integrate.vode',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\integrate\\vode.py',
   'PYMODULE'),
  ('scipy.interpolate',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\interpolate\\__init__.py',
   'PYMODULE'),
  ('scipy.interpolate._bary_rational',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_bary_rational.py',
   'PYMODULE'),
  ('scipy.interpolate._bsplines',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_bsplines.py',
   'PYMODULE'),
  ('scipy.interpolate._cubic',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_cubic.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack2',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_fitpack2.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_impl',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_fitpack_impl.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_py',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_fitpack_py.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_repro',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_fitpack_repro.py',
   'PYMODULE'),
  ('scipy.interpolate._interpolate',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_interpolate.py',
   'PYMODULE'),
  ('scipy.interpolate._ndbspline',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_ndbspline.py',
   'PYMODULE'),
  ('scipy.interpolate._ndgriddata',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_ndgriddata.py',
   'PYMODULE'),
  ('scipy.interpolate._pade',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_pade.py',
   'PYMODULE'),
  ('scipy.interpolate._polyint',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_polyint.py',
   'PYMODULE'),
  ('scipy.interpolate._rbf',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_rbf.py',
   'PYMODULE'),
  ('scipy.interpolate._rbfinterp',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_rbfinterp.py',
   'PYMODULE'),
  ('scipy.interpolate._rgi',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_rgi.py',
   'PYMODULE'),
  ('scipy.interpolate.fitpack',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\interpolate\\fitpack.py',
   'PYMODULE'),
  ('scipy.interpolate.fitpack2',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\interpolate\\fitpack2.py',
   'PYMODULE'),
  ('scipy.interpolate.interpnd',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\interpolate\\interpnd.py',
   'PYMODULE'),
  ('scipy.interpolate.interpolate',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\interpolate\\interpolate.py',
   'PYMODULE'),
  ('scipy.interpolate.ndgriddata',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\interpolate\\ndgriddata.py',
   'PYMODULE'),
  ('scipy.interpolate.polyint',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\interpolate\\polyint.py',
   'PYMODULE'),
  ('scipy.interpolate.rbf',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\interpolate\\rbf.py',
   'PYMODULE'),
  ('scipy.linalg',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\__init__.py',
   'PYMODULE'),
  ('scipy.linalg._basic',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\_basic.py',
   'PYMODULE'),
  ('scipy.linalg._decomp',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\_decomp.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_cholesky',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\_decomp_cholesky.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_cossin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\_decomp_cossin.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_ldl',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\_decomp_ldl.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_lu',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\_decomp_lu.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_polar',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\_decomp_polar.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_qr',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\_decomp_qr.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_qz',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\_decomp_qz.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_schur',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\_decomp_schur.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_svd',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\_decomp_svd.py',
   'PYMODULE'),
  ('scipy.linalg._expm_frechet',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\_expm_frechet.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\_matfuncs.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs_inv_ssq',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_inv_ssq.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs_sqrtm',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_sqrtm.py',
   'PYMODULE'),
  ('scipy.linalg._misc',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\_misc.py',
   'PYMODULE'),
  ('scipy.linalg._procrustes',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\_procrustes.py',
   'PYMODULE'),
  ('scipy.linalg._sketches',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\_sketches.py',
   'PYMODULE'),
  ('scipy.linalg._solvers',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\_solvers.py',
   'PYMODULE'),
  ('scipy.linalg._special_matrices',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\_special_matrices.py',
   'PYMODULE'),
  ('scipy.linalg.basic',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\basic.py',
   'PYMODULE'),
  ('scipy.linalg.blas',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\blas.py',
   'PYMODULE'),
  ('scipy.linalg.decomp',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\decomp.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_cholesky',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\decomp_cholesky.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_lu',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\decomp_lu.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_qr',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\decomp_qr.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_schur',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\decomp_schur.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_svd',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\decomp_svd.py',
   'PYMODULE'),
  ('scipy.linalg.interpolative',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\interpolative.py',
   'PYMODULE'),
  ('scipy.linalg.lapack',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\lapack.py',
   'PYMODULE'),
  ('scipy.linalg.matfuncs',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\matfuncs.py',
   'PYMODULE'),
  ('scipy.linalg.misc',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\misc.py',
   'PYMODULE'),
  ('scipy.linalg.special_matrices',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\linalg\\special_matrices.py',
   'PYMODULE'),
  ('scipy.ndimage',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\ndimage\\__init__.py',
   'PYMODULE'),
  ('scipy.ndimage._delegators',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\ndimage\\_delegators.py',
   'PYMODULE'),
  ('scipy.ndimage._filters',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\ndimage\\_filters.py',
   'PYMODULE'),
  ('scipy.ndimage._fourier',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\ndimage\\_fourier.py',
   'PYMODULE'),
  ('scipy.ndimage._interpolation',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\ndimage\\_interpolation.py',
   'PYMODULE'),
  ('scipy.ndimage._measurements',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\ndimage\\_measurements.py',
   'PYMODULE'),
  ('scipy.ndimage._morphology',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\ndimage\\_morphology.py',
   'PYMODULE'),
  ('scipy.ndimage._ndimage_api',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\ndimage\\_ndimage_api.py',
   'PYMODULE'),
  ('scipy.ndimage._ni_docstrings',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\ndimage\\_ni_docstrings.py',
   'PYMODULE'),
  ('scipy.ndimage._ni_support',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\ndimage\\_ni_support.py',
   'PYMODULE'),
  ('scipy.ndimage._support_alternative_backends',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\ndimage\\_support_alternative_backends.py',
   'PYMODULE'),
  ('scipy.ndimage.filters',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\ndimage\\filters.py',
   'PYMODULE'),
  ('scipy.ndimage.fourier',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\ndimage\\fourier.py',
   'PYMODULE'),
  ('scipy.ndimage.interpolation',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\ndimage\\interpolation.py',
   'PYMODULE'),
  ('scipy.ndimage.measurements',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\ndimage\\measurements.py',
   'PYMODULE'),
  ('scipy.ndimage.morphology',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\ndimage\\morphology.py',
   'PYMODULE'),
  ('scipy.optimize',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._basinhopping',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_basinhopping.py',
   'PYMODULE'),
  ('scipy.optimize._bracket',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_bracket.py',
   'PYMODULE'),
  ('scipy.optimize._chandrupatla',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_chandrupatla.py',
   'PYMODULE'),
  ('scipy.optimize._cobyla_py',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_cobyla_py.py',
   'PYMODULE'),
  ('scipy.optimize._cobyqa_py',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_cobyqa_py.py',
   'PYMODULE'),
  ('scipy.optimize._constraints',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_constraints.py',
   'PYMODULE'),
  ('scipy.optimize._dcsrch',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_dcsrch.py',
   'PYMODULE'),
  ('scipy.optimize._differentiable_functions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_differentiable_functions.py',
   'PYMODULE'),
  ('scipy.optimize._differentialevolution',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_differentialevolution.py',
   'PYMODULE'),
  ('scipy.optimize._direct_py',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_direct_py.py',
   'PYMODULE'),
  ('scipy.optimize._dual_annealing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_dual_annealing.py',
   'PYMODULE'),
  ('scipy.optimize._hessian_update_strategy',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_hessian_update_strategy.py',
   'PYMODULE'),
  ('scipy.optimize._highspy',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_highspy\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._highspy._highs_wrapper',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_highspy\\_highs_wrapper.py',
   'PYMODULE'),
  ('scipy.optimize._isotonic',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_isotonic.py',
   'PYMODULE'),
  ('scipy.optimize._lbfgsb_py',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_lbfgsb_py.py',
   'PYMODULE'),
  ('scipy.optimize._linesearch',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_linesearch.py',
   'PYMODULE'),
  ('scipy.optimize._linprog',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_linprog.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_doc',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_linprog_doc.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_highs',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_linprog_highs.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_ip',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_linprog_ip.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_rs',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_linprog_rs.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_simplex',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_linprog_simplex.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_util',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_linprog_util.py',
   'PYMODULE'),
  ('scipy.optimize._lsq',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_lsq\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.bvls',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_lsq\\bvls.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.common',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_lsq\\common.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.dogbox',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_lsq\\dogbox.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.least_squares',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_lsq\\least_squares.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.lsq_linear',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_lsq\\lsq_linear.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.trf',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_lsq\\trf.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.trf_linear',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_lsq\\trf_linear.py',
   'PYMODULE'),
  ('scipy.optimize._milp',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_milp.py',
   'PYMODULE'),
  ('scipy.optimize._minimize',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_minimize.py',
   'PYMODULE'),
  ('scipy.optimize._minpack_py',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_minpack_py.py',
   'PYMODULE'),
  ('scipy.optimize._nnls',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_nnls.py',
   'PYMODULE'),
  ('scipy.optimize._nonlin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_nonlin.py',
   'PYMODULE'),
  ('scipy.optimize._numdiff',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_numdiff.py',
   'PYMODULE'),
  ('scipy.optimize._optimize',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_optimize.py',
   'PYMODULE'),
  ('scipy.optimize._qap',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_qap.py',
   'PYMODULE'),
  ('scipy.optimize._remove_redundancy',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_remove_redundancy.py',
   'PYMODULE'),
  ('scipy.optimize._root',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_root.py',
   'PYMODULE'),
  ('scipy.optimize._root_scalar',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_root_scalar.py',
   'PYMODULE'),
  ('scipy.optimize._shgo',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_shgo.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_shgo_lib\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib._complex',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_shgo_lib\\_complex.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib._vertex',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_shgo_lib\\_vertex.py',
   'PYMODULE'),
  ('scipy.optimize._slsqp_py',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_slsqp_py.py',
   'PYMODULE'),
  ('scipy.optimize._spectral',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_spectral.py',
   'PYMODULE'),
  ('scipy.optimize._tnc',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_tnc.py',
   'PYMODULE'),
  ('scipy.optimize._trlib',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_trlib\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_trustregion.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.canonical_constraint',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\canonical_constraint.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.equality_constrained_sqp',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\equality_constrained_sqp.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.minimize_trustregion_constr',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\minimize_trustregion_constr.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.projections',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\projections.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.qp_subproblem',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\qp_subproblem.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.report',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\report.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.tr_interior_point',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\tr_interior_point.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_dogleg',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_trustregion_dogleg.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_exact',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_trustregion_exact.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_krylov',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_trustregion_krylov.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_ncg',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_trustregion_ncg.py',
   'PYMODULE'),
  ('scipy.optimize._zeros_py',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\_zeros_py.py',
   'PYMODULE'),
  ('scipy.optimize.cobyla',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\cobyla.py',
   'PYMODULE'),
  ('scipy.optimize.lbfgsb',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\lbfgsb.py',
   'PYMODULE'),
  ('scipy.optimize.linesearch',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\linesearch.py',
   'PYMODULE'),
  ('scipy.optimize.minpack',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\minpack.py',
   'PYMODULE'),
  ('scipy.optimize.minpack2',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\minpack2.py',
   'PYMODULE'),
  ('scipy.optimize.moduleTNC',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\moduleTNC.py',
   'PYMODULE'),
  ('scipy.optimize.nonlin',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\nonlin.py',
   'PYMODULE'),
  ('scipy.optimize.optimize',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\optimize.py',
   'PYMODULE'),
  ('scipy.optimize.slsqp',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\slsqp.py',
   'PYMODULE'),
  ('scipy.optimize.tnc',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\tnc.py',
   'PYMODULE'),
  ('scipy.optimize.zeros',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\optimize\\zeros.py',
   'PYMODULE'),
  ('scipy.sparse',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse._base',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\_base.py',
   'PYMODULE'),
  ('scipy.sparse._bsr',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\_bsr.py',
   'PYMODULE'),
  ('scipy.sparse._compressed',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\_compressed.py',
   'PYMODULE'),
  ('scipy.sparse._construct',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\_construct.py',
   'PYMODULE'),
  ('scipy.sparse._coo',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\_coo.py',
   'PYMODULE'),
  ('scipy.sparse._csc',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\_csc.py',
   'PYMODULE'),
  ('scipy.sparse._csr',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\_csr.py',
   'PYMODULE'),
  ('scipy.sparse._data',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\_data.py',
   'PYMODULE'),
  ('scipy.sparse._dia',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\_dia.py',
   'PYMODULE'),
  ('scipy.sparse._dok',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\_dok.py',
   'PYMODULE'),
  ('scipy.sparse._extract',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\_extract.py',
   'PYMODULE'),
  ('scipy.sparse._index',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\_index.py',
   'PYMODULE'),
  ('scipy.sparse._lil',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\_lil.py',
   'PYMODULE'),
  ('scipy.sparse._matrix',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\_matrix.py',
   'PYMODULE'),
  ('scipy.sparse._matrix_io',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\_matrix_io.py',
   'PYMODULE'),
  ('scipy.sparse._spfuncs',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\_spfuncs.py',
   'PYMODULE'),
  ('scipy.sparse._sputils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\_sputils.py',
   'PYMODULE'),
  ('scipy.sparse.base',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\base.py',
   'PYMODULE'),
  ('scipy.sparse.bsr',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\bsr.py',
   'PYMODULE'),
  ('scipy.sparse.compressed',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\compressed.py',
   'PYMODULE'),
  ('scipy.sparse.construct',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\construct.py',
   'PYMODULE'),
  ('scipy.sparse.coo',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\coo.py',
   'PYMODULE'),
  ('scipy.sparse.csc',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\csc.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\csgraph\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph._laplacian',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_laplacian.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph._validation',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_validation.py',
   'PYMODULE'),
  ('scipy.sparse.csr',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\csr.py',
   'PYMODULE'),
  ('scipy.sparse.data',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\data.py',
   'PYMODULE'),
  ('scipy.sparse.dia',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\dia.py',
   'PYMODULE'),
  ('scipy.sparse.dok',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\dok.py',
   'PYMODULE'),
  ('scipy.sparse.extract',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\extract.py',
   'PYMODULE'),
  ('scipy.sparse.lil',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\lil.py',
   'PYMODULE'),
  ('scipy.sparse.linalg',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve._add_newdocs',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\_add_newdocs.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve.linsolve',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\linsolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen._svds',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\_svds.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.arpack',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.arpack.arpack',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\arpack.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.lobpcg',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\lobpcg\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.lobpcg.lobpcg',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\lobpcg\\lobpcg.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._expm_multiply',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_expm_multiply.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._interface',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_interface.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve._gcrotmk',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\_gcrotmk.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.iterative',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\iterative.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lgmres',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lgmres.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lsmr',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lsmr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lsqr',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lsqr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.minres',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\minres.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.tfqmr',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\tfqmr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.utils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\utils.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._matfuncs',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_matfuncs.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._norm',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_norm.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._onenormest',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_onenormest.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._propack', '-', 'PYMODULE'),
  ('scipy.sparse.linalg._special_sparse_arrays',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_special_sparse_arrays.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._svdp',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_svdp.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.dsolve',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\dsolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.eigen',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\eigen.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.interface',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\interface.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.isolve',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\isolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.matfuncs',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\matfuncs.py',
   'PYMODULE'),
  ('scipy.sparse.sparsetools',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\sparsetools.py',
   'PYMODULE'),
  ('scipy.sparse.sputils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\sparse\\sputils.py',
   'PYMODULE'),
  ('scipy.spatial',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\spatial\\__init__.py',
   'PYMODULE'),
  ('scipy.spatial._geometric_slerp',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\spatial\\_geometric_slerp.py',
   'PYMODULE'),
  ('scipy.spatial._kdtree',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\spatial\\_kdtree.py',
   'PYMODULE'),
  ('scipy.spatial._plotutils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\spatial\\_plotutils.py',
   'PYMODULE'),
  ('scipy.spatial._procrustes',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\spatial\\_procrustes.py',
   'PYMODULE'),
  ('scipy.spatial._spherical_voronoi',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\spatial\\_spherical_voronoi.py',
   'PYMODULE'),
  ('scipy.spatial.ckdtree',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\spatial\\ckdtree.py',
   'PYMODULE'),
  ('scipy.spatial.distance',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\spatial\\distance.py',
   'PYMODULE'),
  ('scipy.spatial.kdtree',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\spatial\\kdtree.py',
   'PYMODULE'),
  ('scipy.spatial.qhull',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\spatial\\qhull.py',
   'PYMODULE'),
  ('scipy.spatial.transform',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\spatial\\transform\\__init__.py',
   'PYMODULE'),
  ('scipy.spatial.transform._rotation_groups',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\spatial\\transform\\_rotation_groups.py',
   'PYMODULE'),
  ('scipy.spatial.transform._rotation_spline',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\spatial\\transform\\_rotation_spline.py',
   'PYMODULE'),
  ('scipy.spatial.transform.rotation',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\spatial\\transform\\rotation.py',
   'PYMODULE'),
  ('scipy.special',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\special\\__init__.py',
   'PYMODULE'),
  ('scipy.special._basic',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\special\\_basic.py',
   'PYMODULE'),
  ('scipy.special._ellip_harm',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\special\\_ellip_harm.py',
   'PYMODULE'),
  ('scipy.special._input_validation',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\special\\_input_validation.py',
   'PYMODULE'),
  ('scipy.special._lambertw',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\special\\_lambertw.py',
   'PYMODULE'),
  ('scipy.special._logsumexp',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\special\\_logsumexp.py',
   'PYMODULE'),
  ('scipy.special._multiufuncs',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\special\\_multiufuncs.py',
   'PYMODULE'),
  ('scipy.special._orthogonal',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\special\\_orthogonal.py',
   'PYMODULE'),
  ('scipy.special._sf_error',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\special\\_sf_error.py',
   'PYMODULE'),
  ('scipy.special._spfun_stats',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\special\\_spfun_stats.py',
   'PYMODULE'),
  ('scipy.special._spherical_bessel',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\special\\_spherical_bessel.py',
   'PYMODULE'),
  ('scipy.special._support_alternative_backends',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\special\\_support_alternative_backends.py',
   'PYMODULE'),
  ('scipy.special.add_newdocs',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\special\\add_newdocs.py',
   'PYMODULE'),
  ('scipy.special.basic',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\special\\basic.py',
   'PYMODULE'),
  ('scipy.special.orthogonal',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\special\\orthogonal.py',
   'PYMODULE'),
  ('scipy.special.sf_error',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\special\\sf_error.py',
   'PYMODULE'),
  ('scipy.special.specfun',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\special\\specfun.py',
   'PYMODULE'),
  ('scipy.special.spfun_stats',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\special\\spfun_stats.py',
   'PYMODULE'),
  ('scipy.stats',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._axis_nan_policy',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_axis_nan_policy.py',
   'PYMODULE'),
  ('scipy.stats._binned_statistic',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_binned_statistic.py',
   'PYMODULE'),
  ('scipy.stats._binomtest',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_binomtest.py',
   'PYMODULE'),
  ('scipy.stats._bws_test',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_bws_test.py',
   'PYMODULE'),
  ('scipy.stats._censored_data',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_censored_data.py',
   'PYMODULE'),
  ('scipy.stats._common',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_common.py',
   'PYMODULE'),
  ('scipy.stats._constants',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_constants.py',
   'PYMODULE'),
  ('scipy.stats._continuous_distns',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_continuous_distns.py',
   'PYMODULE'),
  ('scipy.stats._correlation',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_correlation.py',
   'PYMODULE'),
  ('scipy.stats._covariance',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_covariance.py',
   'PYMODULE'),
  ('scipy.stats._crosstab',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_crosstab.py',
   'PYMODULE'),
  ('scipy.stats._discrete_distns',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_discrete_distns.py',
   'PYMODULE'),
  ('scipy.stats._distn_infrastructure',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_distn_infrastructure.py',
   'PYMODULE'),
  ('scipy.stats._distr_params',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_distr_params.py',
   'PYMODULE'),
  ('scipy.stats._distribution_infrastructure',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_distribution_infrastructure.py',
   'PYMODULE'),
  ('scipy.stats._entropy',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_entropy.py',
   'PYMODULE'),
  ('scipy.stats._fit',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_fit.py',
   'PYMODULE'),
  ('scipy.stats._hypotests',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_hypotests.py',
   'PYMODULE'),
  ('scipy.stats._kde',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_kde.py',
   'PYMODULE'),
  ('scipy.stats._ksstats',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_ksstats.py',
   'PYMODULE'),
  ('scipy.stats._levy_stable',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_levy_stable\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._mannwhitneyu',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_mannwhitneyu.py',
   'PYMODULE'),
  ('scipy.stats._mgc',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_mgc.py',
   'PYMODULE'),
  ('scipy.stats._morestats',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_morestats.py',
   'PYMODULE'),
  ('scipy.stats._mstats_basic',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_mstats_basic.py',
   'PYMODULE'),
  ('scipy.stats._mstats_extras',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_mstats_extras.py',
   'PYMODULE'),
  ('scipy.stats._multicomp',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_multicomp.py',
   'PYMODULE'),
  ('scipy.stats._multivariate',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_multivariate.py',
   'PYMODULE'),
  ('scipy.stats._new_distributions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_new_distributions.py',
   'PYMODULE'),
  ('scipy.stats._odds_ratio',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_odds_ratio.py',
   'PYMODULE'),
  ('scipy.stats._page_trend_test',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_page_trend_test.py',
   'PYMODULE'),
  ('scipy.stats._probability_distribution',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_probability_distribution.py',
   'PYMODULE'),
  ('scipy.stats._qmc',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_qmc.py',
   'PYMODULE'),
  ('scipy.stats._qmvnt',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_qmvnt.py',
   'PYMODULE'),
  ('scipy.stats._rcont',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_rcont\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._relative_risk',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_relative_risk.py',
   'PYMODULE'),
  ('scipy.stats._resampling',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_resampling.py',
   'PYMODULE'),
  ('scipy.stats._sensitivity_analysis',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_sensitivity_analysis.py',
   'PYMODULE'),
  ('scipy.stats._stats_mstats_common',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_stats_mstats_common.py',
   'PYMODULE'),
  ('scipy.stats._stats_py',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_stats_py.py',
   'PYMODULE'),
  ('scipy.stats._survival',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_survival.py',
   'PYMODULE'),
  ('scipy.stats._tukeylambda_stats',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_tukeylambda_stats.py',
   'PYMODULE'),
  ('scipy.stats._variation',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_variation.py',
   'PYMODULE'),
  ('scipy.stats._warnings_errors',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_warnings_errors.py',
   'PYMODULE'),
  ('scipy.stats._wilcoxon',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\_wilcoxon.py',
   'PYMODULE'),
  ('scipy.stats.biasedurn',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\biasedurn.py',
   'PYMODULE'),
  ('scipy.stats.contingency',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\contingency.py',
   'PYMODULE'),
  ('scipy.stats.distributions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\distributions.py',
   'PYMODULE'),
  ('scipy.stats.kde',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\kde.py',
   'PYMODULE'),
  ('scipy.stats.morestats',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\morestats.py',
   'PYMODULE'),
  ('scipy.stats.mstats',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\mstats.py',
   'PYMODULE'),
  ('scipy.stats.mstats_basic',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\mstats_basic.py',
   'PYMODULE'),
  ('scipy.stats.mstats_extras',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\mstats_extras.py',
   'PYMODULE'),
  ('scipy.stats.mvn',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\mvn.py',
   'PYMODULE'),
  ('scipy.stats.qmc',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\qmc.py',
   'PYMODULE'),
  ('scipy.stats.stats',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\stats\\stats.py',
   'PYMODULE'),
  ('scipy.version',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\scipy\\version.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\selectors.py',
   'PYMODULE'),
  ('setuptools',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shiboken6',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\shiboken6\\__init__.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\signal.py',
   'PYMODULE'),
  ('site',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site.py',
   'PYMODULE'),
  ('six',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('sklearn',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\__init__.py',
   'PYMODULE'),
  ('sklearn.__check_build',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\__check_build\\__init__.py',
   'PYMODULE'),
  ('sklearn._built_with_meson',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\_built_with_meson.py',
   'PYMODULE'),
  ('sklearn._config',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\_config.py',
   'PYMODULE'),
  ('sklearn._distributor_init',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\_distributor_init.py',
   'PYMODULE'),
  ('sklearn._loss',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\_loss\\__init__.py',
   'PYMODULE'),
  ('sklearn._loss.link',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\_loss\\link.py',
   'PYMODULE'),
  ('sklearn._loss.loss',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\_loss\\loss.py',
   'PYMODULE'),
  ('sklearn.base',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\base.py',
   'PYMODULE'),
  ('sklearn.cluster',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\cluster\\__init__.py',
   'PYMODULE'),
  ('sklearn.cluster._affinity_propagation',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_affinity_propagation.py',
   'PYMODULE'),
  ('sklearn.cluster._agglomerative',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_agglomerative.py',
   'PYMODULE'),
  ('sklearn.cluster._bicluster',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_bicluster.py',
   'PYMODULE'),
  ('sklearn.cluster._birch',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_birch.py',
   'PYMODULE'),
  ('sklearn.cluster._bisect_k_means',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_bisect_k_means.py',
   'PYMODULE'),
  ('sklearn.cluster._dbscan',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_dbscan.py',
   'PYMODULE'),
  ('sklearn.cluster._feature_agglomeration',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_feature_agglomeration.py',
   'PYMODULE'),
  ('sklearn.cluster._hdbscan',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\__init__.py',
   'PYMODULE'),
  ('sklearn.cluster._hdbscan.hdbscan',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\hdbscan.py',
   'PYMODULE'),
  ('sklearn.cluster._kmeans',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py',
   'PYMODULE'),
  ('sklearn.cluster._mean_shift',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_mean_shift.py',
   'PYMODULE'),
  ('sklearn.cluster._optics',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_optics.py',
   'PYMODULE'),
  ('sklearn.cluster._spectral',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_spectral.py',
   'PYMODULE'),
  ('sklearn.covariance',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\covariance\\__init__.py',
   'PYMODULE'),
  ('sklearn.covariance._elliptic_envelope',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\covariance\\_elliptic_envelope.py',
   'PYMODULE'),
  ('sklearn.covariance._empirical_covariance',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\covariance\\_empirical_covariance.py',
   'PYMODULE'),
  ('sklearn.covariance._graph_lasso',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\covariance\\_graph_lasso.py',
   'PYMODULE'),
  ('sklearn.covariance._robust_covariance',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\covariance\\_robust_covariance.py',
   'PYMODULE'),
  ('sklearn.covariance._shrunk_covariance',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\covariance\\_shrunk_covariance.py',
   'PYMODULE'),
  ('sklearn.decomposition',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\__init__.py',
   'PYMODULE'),
  ('sklearn.decomposition._base',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_base.py',
   'PYMODULE'),
  ('sklearn.decomposition._dict_learning',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_dict_learning.py',
   'PYMODULE'),
  ('sklearn.decomposition._factor_analysis',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_factor_analysis.py',
   'PYMODULE'),
  ('sklearn.decomposition._fastica',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_fastica.py',
   'PYMODULE'),
  ('sklearn.decomposition._incremental_pca',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_incremental_pca.py',
   'PYMODULE'),
  ('sklearn.decomposition._kernel_pca',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_kernel_pca.py',
   'PYMODULE'),
  ('sklearn.decomposition._lda',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_lda.py',
   'PYMODULE'),
  ('sklearn.decomposition._nmf',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_nmf.py',
   'PYMODULE'),
  ('sklearn.decomposition._pca',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_pca.py',
   'PYMODULE'),
  ('sklearn.decomposition._sparse_pca',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_sparse_pca.py',
   'PYMODULE'),
  ('sklearn.decomposition._truncated_svd',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_truncated_svd.py',
   'PYMODULE'),
  ('sklearn.discriminant_analysis',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\discriminant_analysis.py',
   'PYMODULE'),
  ('sklearn.exceptions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\exceptions.py',
   'PYMODULE'),
  ('sklearn.externals',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\externals\\__init__.py',
   'PYMODULE'),
  ('sklearn.externals._packaging',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\externals\\_packaging\\__init__.py',
   'PYMODULE'),
  ('sklearn.externals._packaging._structures',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\externals\\_packaging\\_structures.py',
   'PYMODULE'),
  ('sklearn.externals._packaging.version',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\externals\\_packaging\\version.py',
   'PYMODULE'),
  ('sklearn.externals._scipy',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\externals\\_scipy\\__init__.py',
   'PYMODULE'),
  ('sklearn.externals._scipy.sparse',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\externals\\_scipy\\sparse\\__init__.py',
   'PYMODULE'),
  ('sklearn.externals._scipy.sparse.csgraph',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\externals\\_scipy\\sparse\\csgraph\\__init__.py',
   'PYMODULE'),
  ('sklearn.externals._scipy.sparse.csgraph._laplacian',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\externals\\_scipy\\sparse\\csgraph\\_laplacian.py',
   'PYMODULE'),
  ('sklearn.gaussian_process',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\gaussian_process\\__init__.py',
   'PYMODULE'),
  ('sklearn.gaussian_process._gpc',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\gaussian_process\\_gpc.py',
   'PYMODULE'),
  ('sklearn.gaussian_process._gpr',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\gaussian_process\\_gpr.py',
   'PYMODULE'),
  ('sklearn.gaussian_process.kernels',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\gaussian_process\\kernels.py',
   'PYMODULE'),
  ('sklearn.isotonic',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\isotonic.py',
   'PYMODULE'),
  ('sklearn.linear_model',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\__init__.py',
   'PYMODULE'),
  ('sklearn.linear_model._base',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_base.py',
   'PYMODULE'),
  ('sklearn.linear_model._bayes',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_bayes.py',
   'PYMODULE'),
  ('sklearn.linear_model._coordinate_descent',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_coordinate_descent.py',
   'PYMODULE'),
  ('sklearn.linear_model._glm',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_glm\\__init__.py',
   'PYMODULE'),
  ('sklearn.linear_model._glm._newton_solver',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_glm\\_newton_solver.py',
   'PYMODULE'),
  ('sklearn.linear_model._glm.glm',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_glm\\glm.py',
   'PYMODULE'),
  ('sklearn.linear_model._huber',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_huber.py',
   'PYMODULE'),
  ('sklearn.linear_model._least_angle',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_least_angle.py',
   'PYMODULE'),
  ('sklearn.linear_model._linear_loss',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_linear_loss.py',
   'PYMODULE'),
  ('sklearn.linear_model._logistic',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_logistic.py',
   'PYMODULE'),
  ('sklearn.linear_model._omp',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_omp.py',
   'PYMODULE'),
  ('sklearn.linear_model._passive_aggressive',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_passive_aggressive.py',
   'PYMODULE'),
  ('sklearn.linear_model._perceptron',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_perceptron.py',
   'PYMODULE'),
  ('sklearn.linear_model._quantile',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_quantile.py',
   'PYMODULE'),
  ('sklearn.linear_model._ransac',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_ransac.py',
   'PYMODULE'),
  ('sklearn.linear_model._ridge',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_ridge.py',
   'PYMODULE'),
  ('sklearn.linear_model._sag',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_sag.py',
   'PYMODULE'),
  ('sklearn.linear_model._stochastic_gradient',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_stochastic_gradient.py',
   'PYMODULE'),
  ('sklearn.linear_model._theil_sen',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_theil_sen.py',
   'PYMODULE'),
  ('sklearn.manifold',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\manifold\\__init__.py',
   'PYMODULE'),
  ('sklearn.manifold._isomap',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\manifold\\_isomap.py',
   'PYMODULE'),
  ('sklearn.manifold._locally_linear',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\manifold\\_locally_linear.py',
   'PYMODULE'),
  ('sklearn.manifold._mds',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\manifold\\_mds.py',
   'PYMODULE'),
  ('sklearn.manifold._spectral_embedding',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\manifold\\_spectral_embedding.py',
   'PYMODULE'),
  ('sklearn.manifold._t_sne',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\manifold\\_t_sne.py',
   'PYMODULE'),
  ('sklearn.metrics',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\metrics\\__init__.py',
   'PYMODULE'),
  ('sklearn.metrics._base',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_base.py',
   'PYMODULE'),
  ('sklearn.metrics._classification',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_classification.py',
   'PYMODULE'),
  ('sklearn.metrics._pairwise_distances_reduction',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\__init__.py',
   'PYMODULE'),
  ('sklearn.metrics._pairwise_distances_reduction._dispatcher',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_dispatcher.py',
   'PYMODULE'),
  ('sklearn.metrics._plot',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_plot\\__init__.py',
   'PYMODULE'),
  ('sklearn.metrics._plot.confusion_matrix',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_plot\\confusion_matrix.py',
   'PYMODULE'),
  ('sklearn.metrics._plot.det_curve',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_plot\\det_curve.py',
   'PYMODULE'),
  ('sklearn.metrics._plot.precision_recall_curve',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_plot\\precision_recall_curve.py',
   'PYMODULE'),
  ('sklearn.metrics._plot.regression',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_plot\\regression.py',
   'PYMODULE'),
  ('sklearn.metrics._plot.roc_curve',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_plot\\roc_curve.py',
   'PYMODULE'),
  ('sklearn.metrics._ranking',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_ranking.py',
   'PYMODULE'),
  ('sklearn.metrics._regression',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_regression.py',
   'PYMODULE'),
  ('sklearn.metrics._scorer',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_scorer.py',
   'PYMODULE'),
  ('sklearn.metrics.cluster',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\metrics\\cluster\\__init__.py',
   'PYMODULE'),
  ('sklearn.metrics.cluster._bicluster',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\metrics\\cluster\\_bicluster.py',
   'PYMODULE'),
  ('sklearn.metrics.cluster._supervised',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\metrics\\cluster\\_supervised.py',
   'PYMODULE'),
  ('sklearn.metrics.cluster._unsupervised',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\metrics\\cluster\\_unsupervised.py',
   'PYMODULE'),
  ('sklearn.metrics.pairwise',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\metrics\\pairwise.py',
   'PYMODULE'),
  ('sklearn.model_selection',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\model_selection\\__init__.py',
   'PYMODULE'),
  ('sklearn.model_selection._classification_threshold',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\model_selection\\_classification_threshold.py',
   'PYMODULE'),
  ('sklearn.model_selection._plot',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\model_selection\\_plot.py',
   'PYMODULE'),
  ('sklearn.model_selection._search',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\model_selection\\_search.py',
   'PYMODULE'),
  ('sklearn.model_selection._search_successive_halving',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\model_selection\\_search_successive_halving.py',
   'PYMODULE'),
  ('sklearn.model_selection._split',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\model_selection\\_split.py',
   'PYMODULE'),
  ('sklearn.model_selection._validation',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\model_selection\\_validation.py',
   'PYMODULE'),
  ('sklearn.multiclass',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\multiclass.py',
   'PYMODULE'),
  ('sklearn.neighbors',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\__init__.py',
   'PYMODULE'),
  ('sklearn.neighbors._base',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_base.py',
   'PYMODULE'),
  ('sklearn.neighbors._classification',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_classification.py',
   'PYMODULE'),
  ('sklearn.neighbors._graph',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_graph.py',
   'PYMODULE'),
  ('sklearn.neighbors._kde',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_kde.py',
   'PYMODULE'),
  ('sklearn.neighbors._lof',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_lof.py',
   'PYMODULE'),
  ('sklearn.neighbors._nca',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_nca.py',
   'PYMODULE'),
  ('sklearn.neighbors._nearest_centroid',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_nearest_centroid.py',
   'PYMODULE'),
  ('sklearn.neighbors._regression',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_regression.py',
   'PYMODULE'),
  ('sklearn.neighbors._unsupervised',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_unsupervised.py',
   'PYMODULE'),
  ('sklearn.preprocessing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\__init__.py',
   'PYMODULE'),
  ('sklearn.preprocessing._data',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_data.py',
   'PYMODULE'),
  ('sklearn.preprocessing._discretization',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_discretization.py',
   'PYMODULE'),
  ('sklearn.preprocessing._encoders',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_encoders.py',
   'PYMODULE'),
  ('sklearn.preprocessing._function_transformer',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_function_transformer.py',
   'PYMODULE'),
  ('sklearn.preprocessing._label',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_label.py',
   'PYMODULE'),
  ('sklearn.preprocessing._polynomial',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_polynomial.py',
   'PYMODULE'),
  ('sklearn.preprocessing._target_encoder',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_target_encoder.py',
   'PYMODULE'),
  ('sklearn.svm',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\svm\\__init__.py',
   'PYMODULE'),
  ('sklearn.svm._base',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\svm\\_base.py',
   'PYMODULE'),
  ('sklearn.svm._bounds',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\svm\\_bounds.py',
   'PYMODULE'),
  ('sklearn.svm._classes',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\svm\\_classes.py',
   'PYMODULE'),
  ('sklearn.tree',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\tree\\__init__.py',
   'PYMODULE'),
  ('sklearn.tree._classes',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\tree\\_classes.py',
   'PYMODULE'),
  ('sklearn.tree._export',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\tree\\_export.py',
   'PYMODULE'),
  ('sklearn.tree._reingold_tilford',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\tree\\_reingold_tilford.py',
   'PYMODULE'),
  ('sklearn.utils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\__init__.py',
   'PYMODULE'),
  ('sklearn.utils._arpack',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\_arpack.py',
   'PYMODULE'),
  ('sklearn.utils._array_api',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\_array_api.py',
   'PYMODULE'),
  ('sklearn.utils._available_if',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\_available_if.py',
   'PYMODULE'),
  ('sklearn.utils._bunch',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\_bunch.py',
   'PYMODULE'),
  ('sklearn.utils._chunking',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\_chunking.py',
   'PYMODULE'),
  ('sklearn.utils._encode',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\_encode.py',
   'PYMODULE'),
  ('sklearn.utils._estimator_html_repr',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\_estimator_html_repr.py',
   'PYMODULE'),
  ('sklearn.utils._indexing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\_indexing.py',
   'PYMODULE'),
  ('sklearn.utils._joblib',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\_joblib.py',
   'PYMODULE'),
  ('sklearn.utils._mask',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\_mask.py',
   'PYMODULE'),
  ('sklearn.utils._metadata_requests',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\_metadata_requests.py',
   'PYMODULE'),
  ('sklearn.utils._missing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\_missing.py',
   'PYMODULE'),
  ('sklearn.utils._optional_dependencies',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\_optional_dependencies.py',
   'PYMODULE'),
  ('sklearn.utils._param_validation',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\_param_validation.py',
   'PYMODULE'),
  ('sklearn.utils._plotting',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\_plotting.py',
   'PYMODULE'),
  ('sklearn.utils._pprint',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\_pprint.py',
   'PYMODULE'),
  ('sklearn.utils._response',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\_response.py',
   'PYMODULE'),
  ('sklearn.utils._set_output',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\_set_output.py',
   'PYMODULE'),
  ('sklearn.utils._show_versions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\_show_versions.py',
   'PYMODULE'),
  ('sklearn.utils._tags',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\_tags.py',
   'PYMODULE'),
  ('sklearn.utils._testing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\_testing.py',
   'PYMODULE'),
  ('sklearn.utils._unique',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\_unique.py',
   'PYMODULE'),
  ('sklearn.utils.class_weight',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\class_weight.py',
   'PYMODULE'),
  ('sklearn.utils.deprecation',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\deprecation.py',
   'PYMODULE'),
  ('sklearn.utils.discovery',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\discovery.py',
   'PYMODULE'),
  ('sklearn.utils.extmath',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\extmath.py',
   'PYMODULE'),
  ('sklearn.utils.fixes',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\fixes.py',
   'PYMODULE'),
  ('sklearn.utils.graph',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\graph.py',
   'PYMODULE'),
  ('sklearn.utils.metadata_routing',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\metadata_routing.py',
   'PYMODULE'),
  ('sklearn.utils.metaestimators',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\metaestimators.py',
   'PYMODULE'),
  ('sklearn.utils.multiclass',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\multiclass.py',
   'PYMODULE'),
  ('sklearn.utils.optimize',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\optimize.py',
   'PYMODULE'),
  ('sklearn.utils.parallel',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\parallel.py',
   'PYMODULE'),
  ('sklearn.utils.random',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\random.py',
   'PYMODULE'),
  ('sklearn.utils.sparsefuncs',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\sparsefuncs.py',
   'PYMODULE'),
  ('sklearn.utils.stats',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\stats.py',
   'PYMODULE'),
  ('sklearn.utils.validation',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\sklearn\\utils\\validation.py',
   'PYMODULE'),
  ('smtplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\smtplib.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\socketserver.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('src',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\__init__.py',
   'PYMODULE'),
  ('src.analysis',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\analysis\\__init__.py',
   'PYMODULE'),
  ('src.analysis.performance',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\analysis\\performance.py',
   'PYMODULE'),
  ('src.analysis.pressure',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\analysis\\pressure.py',
   'PYMODULE'),
  ('src.analysis.temperature',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\analysis\\temperature.py',
   'PYMODULE'),
  ('src.animation',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\animation\\__init__.py',
   'PYMODULE'),
  ('src.animation.animation_dialog',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\animation\\animation_dialog.py',
   'PYMODULE'),
  ('src.animation.collapse_n_expand',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\animation\\collapse_n_expand.py',
   'PYMODULE'),
  ('src.data',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\data\\__init__.py',
   'PYMODULE'),
  ('src.data.loader',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\data\\loader.py',
   'PYMODULE'),
  ('src.database',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\database\\__init__.py',
   'PYMODULE'),
  ('src.database.database_config',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\database\\database_config.py',
   'PYMODULE'),
  ('src.database.database_handler',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\database\\database_handler.py',
   'PYMODULE'),
  ('src.database.database_login',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\database\\database_login.py',
   'PYMODULE'),
  ('src.report_generation',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\report_generation\\__init__.py',
   'PYMODULE'),
  ('src.report_generation.base_pdf',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\report_generation\\base_pdf.py',
   'PYMODULE'),
  ('src.report_generation.data_collector',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\report_generation\\data_collector.py',
   'PYMODULE'),
  ('src.report_generation.image_handler',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\report_generation\\image_handler.py',
   'PYMODULE'),
  ('src.report_generation.layout_config',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\report_generation\\layout_config.py',
   'PYMODULE'),
  ('src.report_generation.report_generator',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\report_generation\\report_generator.py',
   'PYMODULE'),
  ('src.report_generation.setup_fonts',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\report_generation\\setup_fonts.py',
   'PYMODULE'),
  ('src.utils',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\utils\\__init__.py',
   'PYMODULE'),
  ('src.utils.helpers',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\utils\\helpers.py',
   'PYMODULE'),
  ('src.utils.save_report_dialog_box',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\utils\\save_report_dialog_box.py',
   'PYMODULE'),
  ('src.visualization',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\visualization\\__init__.py',
   'PYMODULE'),
  ('src.visualization.plot_manager',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\visualization\\plot_manager.py',
   'PYMODULE'),
  ('src.visualization.styles',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\src\\visualization\\styles.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ssl.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sysconfig\\__init__.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\threading.py',
   'PYMODULE'),
  ('threadpoolctl',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\threadpoolctl.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tomllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tty.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_LATEST-1_BACKUP\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('user_authentication',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\user_authentication\\__init__.py',
   'PYMODULE'),
  ('user_authentication.auth',
   'D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_Pressure_n_table_changes_1\\user_authentication\\auth.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\uuid.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.dom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipimport.py',
   'PYMODULE')])
