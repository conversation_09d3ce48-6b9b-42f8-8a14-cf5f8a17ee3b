"""
Application Configuration
Contains all configuration constants and settings.
"""

from PySide6.QtCore import QSize
from PySide6.QtGui import QColor


class AppConfig:
    """Application configuration constants."""
    
    # Application Info
    APP_NAME = "VAPR-iDEX Thruster Analysis"
    APP_VERSION = "1.0.0"
    APP_ID = "manastuspace.vapridexanalyzer.1.0"
    
    # Window Settings
    SPLASH_SIZE = QSize(400, 250)
    SPLASH_TIMEOUT = 3000  # milliseconds
    
    # UI Colors
    class Colors:
        PRIMARY = QColor(41, 47, 54)
        SECONDARY = QColor(216, 30, 91)
        SUCCESS = QColor(6, 196, 142)
        WARNING = QColor(255, 193, 7)
        ERROR = QColor(220, 53, 69)
        
        # Plot colors palette
        PLOT_PALETTE = [
            '#01a5e3',  # Blue
            '#165152',  # Teal
            '#9d004e',  # Magenta
            '#32c133',  # Green
            '#fb6238',  # Orange
            '#400972',  # Purple
            '#ee0100',  # Red
            '#f7d02e',  # Yellow
            '#1f77b4',  # Muted blue
            '#ff7f0e',  # Safety orange
            '#2ca02c',  # Cooked asparagus green
            '#d62728',  # Brick red
            '#9467bd',  # Muted purple
            '#8c564b',  # Chestnut brown
            '#7f7f7f',  # Middle gray
            '#bcbd22',  # Curry yellow-green
            '#17becf'   # Blue-teal
        ]
    
    # Button Styles
    class Styles:
        DEFAULT_BUTTON = """
            QPushButton{
                background-color: #1e293c;
                border-radius: 17px;
                border: 1px solid #303030;
                padding:5px;
                font-size: 19px;
                font-family: Helvetica;
                font-weight: bold;
            }
            QPushButton:hover{
                background-color:#47a08e;
            }
            QPushButton:pressed{
                background-color:black;
            }
        """
        
        DEFAULT_SUBSECTION_BUTTON = """
            QPushButton{
                background-color: #1e1e1e;
                color: white;
                padding:5px;
                font-size:17px;
                border:1px solid #446699;
                font-family: Arial;
            }
            QPushButton:hover{
                background-color:#47a08e;
            }
            QPushButton:pressed{
                background-color:#5C5C5C;
            }
        """
        
        SELECTED_BUTTON = """
            QPushButton {
                background-color: #B03781;
                border-radius: 10px;
                padding: 5px;
                font-size: 17px;
            }
        """
        
        SPLASH_SCREEN = """
            QWidget#SplashScreen {
                background-color: #171717;
                border-radius: 15px;
            }
        """
    
    # File Paths
    class Paths:
        ASSETS_DIR = "assets"
        FONTS_DIR = "fonts"
        TEMP_DATA_FILE = "temp_data.json"
        LOG_FILE = "vapr_idex.log"
    
    # Database Settings
    class Database:
        SEARCH_DELAY = 300  # milliseconds
        CONNECTION_TIMEOUT = 30  # seconds
    
    # Animation Settings
    class Animation:
        DURATION = 300  # milliseconds
        EASING_CURVE = "OutCubic"
        AMPLITUDE = 1.0
        OVERSHOOT = 1.7
    
    # Data Validation
    class Validation:
        MIN_TEMPERATURE_COLUMNS = 1
        MIN_PRESSURE_COLUMNS = 1
        MAX_PLOT_TITLE_LENGTH = 100
        
    # UI Timeouts
    class Timeouts:
        PLOTS_BUTTON_ENABLE = 2000  # milliseconds
        AUTO_SAVE_INTERVAL = 30000  # milliseconds
        SEARCH_DELAY = 300  # milliseconds
