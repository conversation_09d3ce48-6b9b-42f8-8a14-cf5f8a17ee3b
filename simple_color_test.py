#!/usr/bin/env python3
"""
Simple test script to verify color assignment functionality without GUI dependencies
"""

# Improved bright color palette from the application
COLOR_PALETTE = [
    '#01a5e3',  # Bright blue
    '#32c133',  # Bright green
    '#fb6238',  # Bright orange
    '#ee0100',  # Bright red
    '#f7d02e',  # Bright yellow
    '#ff7f0e',  # Safety orange
    '#2ca02c',  # Bright green variant
    '#d62728',  # Bright red variant
    '#9467bd',  # Bright purple
    '#17becf',  # Bright cyan
    '#1f77b4',  # Medium blue
    '#bcbd22',  # Olive green
    '#8c564b',  # <PERSON>
    '#e377c2',  # Pink
    '#7f7f7f',  # Gray
    '#9d004e',  # Magenta (darker but still visible)
    '#165152'   # Teal (darker but still visible)
]

def test_new_color_assignment():
    """Test the new palette-based color assignment"""
    print("Testing NEW palette-based color assignment...")
    
    test_columns = [
        "Tank Bottom Temperature",
        "Tank Center Temperature", 
        "Tank Lid Temperature",
        "Catalyst Chamber Temperature",
        "Nozzle Convergent Temperature",
        "Nozzle Exit Temperature",
        "Custom Column 1",
        "Custom Column 2",
        "Custom Column 3",
        "Custom Column 4",
        "Custom Column 5"
    ]
    
    color_assignment_counter = 0
    colors_assigned = {}
    
    for column in test_columns:
        # Simulate the new color assignment logic
        color_index = color_assignment_counter % len(COLOR_PALETTE)
        color = COLOR_PALETTE[color_index]
        colors_assigned[column] = color
        color_assignment_counter += 1
        print(f"{column}: {color}")
    
    # Check for duplicates
    color_values = list(colors_assigned.values())
    unique_colors = set(color_values)
    
    print(f"\nTotal columns: {len(test_columns)}")
    print(f"Unique colors assigned: {len(unique_colors)}")
    print(f"Color palette size: {len(COLOR_PALETTE)}")
    
    # Check if we have good color distribution
    if len(unique_colors) >= min(len(test_columns), len(COLOR_PALETTE)):
        print("✅ NEW method test PASSED - Good color distribution")
    else:
        print("❌ NEW method test FAILED - Too many duplicate colors")
        
    return colors_assigned

def test_old_hash_assignment():
    """Test the old hash-based color assignment that was causing problems"""
    print("\nTesting OLD hash-based color assignment (problematic)...")
    
    test_columns = [
        "Tank Bottom Temperature",
        "Tank Center Temperature", 
        "Tank Lid Temperature",
        "Catalyst Chamber Temperature",
        "Nozzle Convergent Temperature",
        "Nozzle Exit Temperature",
        "Custom Column 1",
        "Custom Column 2",
        "Custom Column 3",
        "Custom Column 4",
        "Custom Column 5"
    ]
    
    hash_colors = {}
    for column in test_columns:
        # This is the old problematic method
        hash_color = f"#{hash(column) % 0xFFFFFF:06x}"
        hash_colors[column] = hash_color
        print(f"{column}: {hash_color}")
    
    # Check for dark colors (colors with low hex values)
    dark_colors = []
    for column, color in hash_colors.items():
        # Convert hex to int to check brightness
        hex_val = int(color[1:], 16)
        # If the color value is low, it's dark
        if hex_val < 0x333333:  # Threshold for "dark"
            dark_colors.append((column, color))
    
    print(f"\nDark colors found: {len(dark_colors)}")
    if dark_colors:
        print("⚠️  OLD method produced dark colors:")
        for column, color in dark_colors:
            print(f"  {column}: {color}")
    else:
        print("✅ OLD method produced no dark colors")
    
    return hash_colors

def analyze_color_brightness(colors_dict, method_name):
    """Analyze the brightness of colors in a dictionary"""
    print(f"\nAnalyzing brightness for {method_name}:")
    
    brightness_scores = []
    for column, color in colors_dict.items():
        # Convert hex to RGB and calculate brightness
        hex_val = int(color[1:], 16)
        r = (hex_val >> 16) & 255
        g = (hex_val >> 8) & 255
        b = hex_val & 255
        
        # Calculate perceived brightness (luminance)
        brightness = (0.299 * r + 0.587 * g + 0.114 * b)
        brightness_scores.append(brightness)
        
        if brightness < 50:  # Very dark
            print(f"  VERY DARK: {column}: {color} (brightness: {brightness:.1f})")
        elif brightness < 100:  # Dark
            print(f"  DARK: {column}: {color} (brightness: {brightness:.1f})")
    
    avg_brightness = sum(brightness_scores) / len(brightness_scores)
    min_brightness = min(brightness_scores)
    max_brightness = max(brightness_scores)
    
    print(f"  Average brightness: {avg_brightness:.1f}")
    print(f"  Min brightness: {min_brightness:.1f}")
    print(f"  Max brightness: {max_brightness:.1f}")
    
    return avg_brightness, min_brightness, max_brightness

if __name__ == "__main__":
    print("=" * 70)
    print("COLOR ASSIGNMENT COMPARISON TEST")
    print("=" * 70)
    
    # Test new method
    new_colors = test_new_color_assignment()
    
    # Test old method
    old_colors = test_old_hash_assignment()
    
    # Analyze brightness
    print("\n" + "=" * 70)
    print("BRIGHTNESS ANALYSIS")
    print("=" * 70)
    
    new_avg, new_min, new_max = analyze_color_brightness(new_colors, "NEW palette-based method")
    old_avg, old_min, old_max = analyze_color_brightness(old_colors, "OLD hash-based method")
    
    print("\n" + "=" * 70)
    print("SUMMARY")
    print("=" * 70)
    print(f"NEW method - Average brightness: {new_avg:.1f}, Min: {new_min:.1f}")
    print(f"OLD method - Average brightness: {old_avg:.1f}, Min: {old_min:.1f}")
    
    if new_min > old_min:
        print("✅ NEW method has brighter minimum colors (less likely to be black/dark)")
    else:
        print("⚠️  OLD method had brighter minimum colors")
    
    if new_avg > old_avg:
        print("✅ NEW method has better average brightness")
    else:
        print("⚠️  OLD method had better average brightness")
    
    print("\nThe NEW method should eliminate the black color issue you experienced!")
