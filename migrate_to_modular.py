#!/usr/bin/env python3
"""
Migration Script for VAPR-iDEX Modular Architecture
This script helps migrate from the monolithic gui_main.py to the new modular structure.
"""

import os
import shutil
import sys
from pathlib import Path


def create_directory_structure():
    """Create the new directory structure."""
    directories = [
        'config',
        'ui/dialogs',
        'ui/widgets', 
        'ui/managers',
        'controllers',
        'services',
        'utils'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        
        # Create __init__.py files for Python packages
        init_file = Path(directory) / '__init__.py'
        if not init_file.exists() and '/' in directory:
            init_file.touch()
    
    print("✓ Directory structure created")


def backup_original_file():
    """Backup the original gui_main.py file."""
    if os.path.exists('gui_main.py'):
        backup_name = 'gui_main_backup.py'
        shutil.copy2('gui_main.py', backup_name)
        print(f"✓ Original gui_main.py backed up as {backup_name}")
    else:
        print("⚠ gui_main.py not found in current directory")


def update_imports():
    """Update import statements in existing files."""
    import_updates = {
        'from gui_main import MainWindow': 'from main_window import MainWindow',
        'from gui_main import safe_float': 'from utils.data_utils import safe_float',
        'from gui_main import TemperatureDataConfigDialog': 'from ui.dialogs import TemperatureDataConfigDialog',
        'from gui_main import ExistingDataLoadDialog': 'from ui.dialogs import ExistingDataLoadDialog',
        'from gui_main import PhotoPreviewDialog': 'from ui.dialogs import PhotoPreviewDialog',
        'from gui_main import HoverButton': 'from ui.widgets import HoverButton',
    }
    
    # Files that might need import updates
    files_to_check = [
        'src/**/*.py',
        'gui/**/*.py', 
        'user_authentication/**/*.py',
        '*.py'
    ]
    
    print("✓ Import update mappings prepared")
    print("  Manual update required for import statements in existing files")


def create_migration_checklist():
    """Create a checklist for manual migration steps."""
    checklist = """
# VAPR-iDEX Modular Migration Checklist

## Completed by Script
- [x] Directory structure created
- [x] Original gui_main.py backed up
- [x] New modular files created

## Manual Steps Required

### 1. Update Imports
Update import statements in the following files:
- [ ] Any files importing from gui_main
- [ ] Test files
- [ ] Documentation files

### 2. Move Remaining Functionality
The following functionality from gui_main.py needs to be manually moved:

#### Data Loading Methods (lines ~4600-4900)
- [ ] load_data_from_json_file()
- [ ] save_json_file()
- [ ] load_test_data()
- [ ] update_ui_with_test_data()

#### Plot Generation Methods (lines ~1500-3000)
- [ ] create_temperature_plot()
- [ ] create_pressure_plot()
- [ ] generate_max_temperatures_plot()
- [ ] plot_with_title()

#### Form Handling Methods (lines ~1400-1600)
- [ ] setup_connections() - remaining connections
- [ ] value_changed() methods
- [ ] reset_input_fields()

#### Report Generation Methods (lines ~4200-4400)
- [ ] generate_report()
- [ ] save_to_database()

### 3. Update Configuration
- [ ] Review config/app_config.py and add missing constants
- [ ] Update any hardcoded values to use configuration

### 4. Testing
- [ ] Test application startup
- [ ] Test each major functionality area
- [ ] Test data loading and saving
- [ ] Test plot generation
- [ ] Test report generation
- [ ] Test database operations

### 5. Documentation
- [ ] Update README.md
- [ ] Update API documentation
- [ ] Update user documentation

## Notes
- The new architecture separates concerns into logical modules
- Each controller handles a specific domain (auth, database, plots, etc.)
- Services handle data operations and business logic
- Managers handle UI state and coordination
- Configuration is centralized in config/app_config.py

## Rollback Plan
If issues arise, restore from gui_main_backup.py:
1. Copy gui_main_backup.py to gui_main.py
2. Remove new modular files
3. Update any changed import statements
"""
    
    with open('MIGRATION_CHECKLIST.md', 'w') as f:
        f.write(checklist)
    
    print("✓ Migration checklist created: MIGRATION_CHECKLIST.md")


def validate_environment():
    """Validate the environment before migration."""
    required_files = [
        'gui/__init__.py',
        'src/utils.py',
        'user_authentication/__init__.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("⚠ Warning: The following required files are missing:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        print("  The application may not work correctly without these files.")
    else:
        print("✓ Environment validation passed")


def main():
    """Main migration function."""
    print("VAPR-iDEX Modular Architecture Migration")
    print("=" * 40)
    
    # Validate environment
    validate_environment()
    
    # Create directory structure
    create_directory_structure()
    
    # Backup original file
    backup_original_file()
    
    # Prepare import updates
    update_imports()
    
    # Create migration checklist
    create_migration_checklist()
    
    print("\n" + "=" * 40)
    print("Migration preparation completed!")
    print("\nNext steps:")
    print("1. Review MIGRATION_CHECKLIST.md")
    print("2. Complete manual migration steps")
    print("3. Test the application thoroughly")
    print("4. Update documentation")
    
    print("\nTo start using the new modular structure:")
    print("  python main.py")


if __name__ == "__main__":
    main()
