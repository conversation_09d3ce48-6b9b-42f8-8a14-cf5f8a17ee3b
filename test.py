import sys
from PySide6.QtWidgets import QA<PERSON><PERSON>, QMainWindow, QLineEdit

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()

        # Create a QLineEdit widget
        self.line_edit = QLineEdit(self)

        # Set a mathematical equation as text
        equation = "E = mc^2"
        self.line_edit.setText(equation)

        # Set the central widget of the main window
        self.setCentralWidget(self.line_edit)

# Create the application
app = QApplication(sys.argv)

# Create and show the main window
window = MainWindow()
window.show()

# Run the application's event loop
sys.exit(app.exec())
