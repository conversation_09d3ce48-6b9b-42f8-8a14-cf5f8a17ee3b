#!/usr/bin/env python3
"""
Test script to verify the pressure absolute dialog functionality
"""

import sys
from PySide6.QtWidgets import QApplication, QDialog, QVBoxLayout, QHBoxLayout, QPushButton
from gui.dialog.ui_pressure_absolute_dialog import Ui_Form
from gui.toggle_switch import MultiStateToggleSwitch

def test_pressure_dialog():
    """Test the pressure absolute dialog"""
    app = QApplication(sys.argv)
    
    # Create dialog
    dialog = QDialog()
    dialog.setWindowTitle("Pressure Data Configuration")
    dialog.setModal(True)
    dialog.resize(300, 150)
    
    # Setup UI using the existing Ui_Form
    ui_form = Ui_Form()
    ui_form.setupUi(dialog)
    
    # Create toggle switch and add to the toggle button frame
    pressure_absolute_toggle = MultiStateToggleSwitch(['Yes', 'No'])
    layout = QHBoxLayout(ui_form.toggle_botton_frame)
    layout.addWidget(pressure_absolute_toggle)
    
    # Add OK and Cancel buttons
    button_layout = QHBoxLayout()
    ok_button = QPushButton("OK")
    cancel_button = QPushButton("Cancel")
    
    button_layout.addWidget(ok_button)
    button_layout.addWidget(cancel_button)
    
    # Add button layout to the main layout
    ui_form.verticalLayout.addLayout(button_layout)
    
    # Connect button signals
    ok_button.clicked.connect(dialog.accept)
    cancel_button.clicked.connect(dialog.reject)
    
    # Show dialog and get result
    result = dialog.exec()
    if result == QDialog.Accepted:
        is_absolute = pressure_absolute_toggle.get_current_index() == 0
        print(f"Dialog accepted. Is absolute: {is_absolute}")
        print(f"Selected mode: {pressure_absolute_toggle.get_current_mode()}")
    else:
        print("Dialog cancelled")
    
    return result

if __name__ == "__main__":
    test_pressure_dialog()
