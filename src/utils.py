"""
Utility functions for the application
"""

import os
import sys

def get_resource_path(relative_path):
    """
    Get the absolute path to a resource, works for development and for PyInstaller
    
    Args:
        relative_path: The path relative to the application root
        
    Returns:
        The absolute path to the resource
    """
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        # We are not running in a PyInstaller bundle, use the script's directory
        base_path = os.path.abspath(".")
        
    return os.path.join(base_path, relative_path)
