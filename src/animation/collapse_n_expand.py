from PySide6.QtCore import QObject, QProperty<PERSON>nimation, QEasingCurve, QPoint
from PySide6.QtWidgets import QGraphicsOpacityEffect


class SectionAnimation(QObject):
    def __init__(self, widget, scroll_area=None):
        super().__init__()
        self.widget = widget
        self.scroll_area = scroll_area
        self.animation = QPropertyAnimation(widget, b"maximumHeight")
        self.animation.setEasingCurve(QEasingCurve.InOutQuart)
        self.animation.setDuration(300)

        # Add opacity effect
        self.opacity_effect = QGraphicsOpacityEffect(widget)
        self.widget.setGraphicsEffect(self.opacity_effect)

        # Create opacity animation
        self.opacity_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.opacity_animation.setDuration(300)

        # Connect value changed to update scroll position during animation
        self.animation.valueChanged.connect(self.updateScrollPosition)

    def expand(self):
        """Expand the section smoothly"""
        try:
            # Update stored height
            target_height = self.widget.sizeHint().height()

            # Reset maximum height constraint
            self.widget.setMaximumHeight(16777215)  # QWIDGETSIZE_MAX

            # Set up height animation
            self.animation.setStartValue(0)
            self.animation.setEndValue(target_height)

            # Set up opacity animation
            self.opacity_animation.setStartValue(0)
            self.opacity_animation.setEndValue(1)

            # Show widget and start animations
            self.widget.setMaximumHeight(0)
            self.widget.show()

            # Start both animations
            self.animation.start()
            self.opacity_animation.start()

        except Exception as e:
            print(f"Error in expand animation: {str(e)}")

    def collapse(self):
        """Collapse the section smoothly"""
        try:
            # Set up height animation
            current_height = self.widget.height()
            self.animation.setStartValue(current_height)
            self.animation.setEndValue(0)

            # Set up opacity animation
            self.opacity_animation.setStartValue(1)
            self.opacity_animation.setEndValue(0)

            # Start both animations
            self.animation.start()
            self.opacity_animation.start()

        except Exception as e:
            print(f"Error in collapse animation: {str(e)}")

    def updateScrollPosition(self):
        """Update scroll position during animation to keep widget centered"""
        try:
            if not self.scroll_area or self.widget.isHidden():
                return

            # Get the current widget position relative to the scroll area
            widget_pos = self.widget.mapTo(self.scroll_area.widget(), QPoint(0, 0))
            scroll_area_height = self.scroll_area.height()

            # Get the current animated height
            current_height = self.animation.currentValue()

            # Calculate the target scroll position to center the widget
            target_position = max(0, widget_pos.y() - (scroll_area_height - current_height) // 2)

            # Get current scroll bar
            scroll_bar = self.scroll_area.verticalScrollBar()

            # Get the animation progress (0 to 1)
            start_val = self.animation.startValue()
            end_val = self.animation.endValue()
            if end_val != start_val:
                progress = (current_height - start_val) / (end_val - start_val)

                # Smoothly interpolate to target position
                current_scroll = scroll_bar.value()
                new_position = current_scroll + (target_position - current_scroll) * progress

                # Update scroll position
                scroll_bar.setValue(int(new_position))

        except Exception as e:
            print(f"Error updating scroll position: {str(e)}")