from PySide6.QtWidgets import QVBoxLayout, QDialog, QLabel
from PySide6.QtCore import Qt
from PySide6.QtGui import QMovie


class AnimationDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        # Set window flags for floating effect
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_TranslucentBackground)  # Make dialog background transparent

        # Center over parent window
        if parent:
            parent_geo = parent.geometry()
            self.setGeometry(
                parent_geo.x() + (parent_geo.width() - 400) // 2,
                parent_geo.y() + (parent_geo.height() - 400) // 2,
                400, 400  # Adjust to your GIF size
            )
        else:
            self.setFixedSize(400, 400)

        layout = QVBoxLayout()
        self.animation_label = QLabel()
        self.animation_label.setAlignment(Qt.AlignCenter)

        # Ensure QLabel background is transparent
        self.animation_label.setStyleSheet("background-color: transparent;")

        layout.addWidget(self.animation_label)
        self.setLayout(layout)

        self.loop_count = 0
        self.max_loops = 3
        self.movie = None

    def play_animation(self, file_path):
        self.movie = QMovie(file_path)
        # Ensure movie preserves transparency
        self.movie.setBackgroundColor(Qt.transparent)
        self.animation_label.setMovie(self.movie)
        self.movie.frameChanged.connect(self.check_loop)
        self.movie.start()

        self.exec()

    def check_loop(self):
        if self.movie.currentFrameNumber() == self.movie.frameCount() - 1:
            self.loop_count += 1
            if self.loop_count >= self.max_loops:
                self.accept()
            else:
                self.movie.start()