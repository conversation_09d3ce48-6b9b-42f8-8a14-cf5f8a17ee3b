import os
import subprocess
import sys

from PySide6.QtCore import Qt
from PySide6.QtWidgets import QPushButton, QVBoxLayout, QLabel, QHBoxLayout, QDialog


class ReportPreviewDialog(QDialog):
    """Dialog for previewing PDF report"""

    def __init__(self, pdf_path: str, parent=None):
        super().__init__(parent)
        self.setup_ui(pdf_path)

    def setup_ui(self, pdf_path: str):
        """Setup the dialog UI"""
        # Configure dialog
        self.setWindowTitle("Report Preview")
        self.setModal(True)
        self.setMinimumSize(500, 300)

        # Create main layout
        layout = QVBoxLayout(self)

        # Add info label
        info_label = QLabel("The report has been generated and opened in your default PDF viewer.\n"
                            "Please review the report and click 'Save' if you want to keep it.")
        info_label.setStyleSheet("color: white; font-size: 12px; padding: 10px;")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(info_label)

        # Open PDF in system viewer
        if sys.platform == 'win32': # Windows
            os.startfile(pdf_path)
        elif sys.platform == 'darwin':  # macOS
            subprocess.run(['open', pdf_path])
        else:  # Linux
            subprocess.run(['xdg-open', pdf_path])

        # Add buttons
        button_layout = QHBoxLayout()

        save_button = QPushButton("Save Report")
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #47a08e;
                border-radius: 5px;
                padding: 8px 16px;
                color: white;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #3d8677;
            }
        """)
        save_button.clicked.connect(self.accept)

        cancel_button = QPushButton("Cancel")
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #666666;
                border-radius: 5px;
                padding: 8px 16px;
                color: white;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #555555;
            }
        """)
        cancel_button.clicked.connect(self.reject)

        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)
        button_layout.setContentsMargins(20, 10, 20, 10)

        layout.addLayout(button_layout)