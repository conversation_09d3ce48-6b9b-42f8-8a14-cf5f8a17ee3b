import os
import subprocess
import sys
import time

from PySide6.QtCore import QThr<PERSON>, Signal, QSize
from PySide6.QtWidgets import QMessageBox, QPushButton


def safe_float(value, default='NA'):
    """
    Safely convert a value to float with fallback.

    Args:
        value: Value to convert to float
        default: Default value if conversion fails

    Returns:
        Float value or default if conversion fails
    """
    try:
        if value and str(value).strip():
            return float(value)
        return default
    except (ValueError, TypeError):
        return default

def clean_file_path(file_path: str) -> str:
    """
            Clean and normalize file path by removing quotes and extra spaces.

            Args:
                file_path (str): Raw file path

            Returns:
                str: Cleaned and normalized path
            """
    # Remove quotes and extra spaces
    cleaned = file_path.strip()
    cleaned = cleaned.strip('"').strip("'")
    cleaned = cleaned.strip()

    # Convert to absolute path and normalize
    cleaned = os.path.abspath(os.path.normpath(cleaned))

    return cleaned

def open_pdf(self, pdf_path):
        """Open the PDF file in the default viewer"""
        try:
            if os.path.exists(pdf_path):
                if sys.platform == 'win32':  # Windows
                    os.startfile(pdf_path)
                elif sys.platform == 'darwin':  # macOS
                    subprocess.run(['open', pdf_path])
                else:  # Linux
                    subprocess.run(['xdg-open', pdf_path])
            else:
                QMessageBox.critical(self, "Error", "PDF file not found!")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error opening PDF: {str(e)}")

def get_resource_path(relative_path):
        """Get the absolute path to a resource file for both dev and PyInstaller environments"""
        try:
            # PyInstaller creates a temp folder and stores path in _MEIPASS
            base_path = sys._MEIPASS
        except Exception:
            base_path = os.path.abspath(".")

        return os.path.join(base_path, relative_path)

class InitThread(QThread):
    status_update = Signal(str)
    finished = Signal()

    def run(self):
        # Simulate initialization steps
        steps = [
            "Loading configuration...",
            "Initializing serial connection...",
            "Setting up GUI components...",
            "Preparing system state..."
        ]

        for step in steps:
            self.status_update.emit(step)
            time.sleep(0.8)  # Simulate work being done

        self.finished.emit()

class HoverButton(QPushButton):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.default_size = QSize(24, 24)
        self.hover_size = QSize(30, 30)  # Bigger size for hover state
        self.setIconSize(self.default_size)

    def enterEvent(self, event):
        self.setIconSize(self.hover_size)
        super().enterEvent(event)

    def leaveEvent(self, event):
        self.setIconSize(self.default_size)
        super().leaveEvent(event)