"""
Visualization Package
-------------------
Components for creating and managing plots and visualizations.

Classes:
    - PlotManager: Handles plot creation and customization

Functions:
    - set_plot_style: Sets the default dark theme plotting style

Example:
    from src.visualization import PlotManager, set_plot_style

    # Set global plot style
    set_plot_style()

    # Create plots
    plot_manager = PlotManager()
    plot_manager.create_temperature_plots(data, output_dir)
"""

from .plot_manager import PlotManager
from .styles import set_plot_style

__all__ = ['PlotManager', 'set_plot_style']
