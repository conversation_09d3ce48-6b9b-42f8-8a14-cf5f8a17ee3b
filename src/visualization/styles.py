"""
Defines enhanced plotting styles and themes for consistent visualization.
"""

import matplotlib.pyplot as plt

def set_plot_style():
    """Set the enhanced dark theme plotting style"""
    plt.style.use('seaborn-v0_8-darkgrid')

    # Define enhanced dark theme colors
    background_color = '#1E1E1E'  # Darker background
    text_color = '#E0E0E0'  # Softer white
    grid_color = '#404040'  # Darker grid
    axis_color = '#666666'  # Lighter axis lines

    plt.rcParams.update({
        # Figure
        'figure.facecolor': background_color,
        'figure.edgecolor': background_color,
        'figure.figsize': (12, 7),  # Larger default figure size
        'figure.dpi': 120,  # Higher DPI

        # Axes
        'axes.facecolor': background_color,
        'axes.edgecolor': axis_color,
        'axes.labelcolor': text_color,
        'axes.titlecolor': text_color,
        'axes.grid': True,
        'axes.labelsize': 11,  # Slightly larger labels
        'axes.titlesize': 14,  # Larger title
        'axes.titleweight': 'bold',
        'axes.spines.top': False,  # Remove top spine
        'axes.spines.right': False,  # Remove right spine
        'axes.linewidth': 1.5,  # Thicker axis lines

        # Grid
        'grid.color': grid_color,
        'grid.alpha': 0.3,
        'grid.linewidth': 0.8,  # Thinner grid lines
        'grid.linestyle': '--',  # Dashed grid lines

        # Ticks
        'xtick.color': text_color,
        'ytick.color': text_color,
        'xtick.labelsize': 10,
        'ytick.labelsize': 10,
        'xtick.major.size': 5,
        'ytick.major.size': 5,
        'xtick.minor.size': 3,
        'ytick.minor.size': 3,

        # Legend
        'legend.facecolor': 'none',
        'legend.edgecolor': 'none',
        'legend.fontsize': 10,
        'legend.title_fontsize': 11,
        'legend.framealpha': 0.7,
        'legend.borderpad': 0.5,
        'legend.labelspacing': 0.8,
        'legend.handlelength': 1.5,
        'legend.handleheight': 0.7,
        'legend.handletextpad': 0.5,
        'legend.borderaxespad': 0.5,
        'legend.labelcolor': text_color,

        # Lines
        'lines.linewidth': 2.0,  # Thicker lines
        'lines.markersize': 8,  # Larger markers
        'lines.markeredgewidth': 1.5,  # Thicker marker edges

        # Save figure
        'savefig.dpi': 300,
        'savefig.bbox': 'tight',
        'savefig.facecolor': background_color,
        'savefig.edgecolor': 'none',
        'savefig.pad_inches': 0.2
    })

# Enhanced color schemes for different plot types
COLOR_SCHEMES = {
    'temperature': {
        'main': '#00A8E8',        # Bright blue
        'secondary': '#FF6B6B',   # Coral red
        'highlight': '#4ECDC4',   # Turquoise
        'nozzle': '#FF9F1C',     # Orange
        'chamber': '#A8E6CF',    # Mint
        'tank': '#3EACFA',       # Sky blue
        'background': '#1E1E1E',  # Dark gray
        'text': '#E0E0E0'        # Soft white
    },
    'pressure': {
        'main': '#98C1D9',       # Light blue
        'secondary': '#EE6C4D',  # Orange
        'highlight': '#3D5A80',  # Navy blue
        'chamber': '#E0FBFC',    # Light cyan
        'tank': '#293241',       # Dark blue
        'background': '#1E1E1E',
        'text': '#E0E0E0'
    }
}

# Enhanced plot styles with better defaults
PLOT_STYLES = {
    'default': {
        'figsize': (12, 7),
        'linewidth': 2.0,
        'grid': True,
        'marker': None,
        'alpha': 0.9,
        'title_fontsize': 14,
        'label_fontsize': 11,
        'legend_fontsize': 10,
        'tick_fontsize': 10
    },
    'detailed': {
        'figsize': (15, 9),
        'linewidth': 1.5,
        'grid': True,
        'marker': 'o',
        'markersize': 6,
        'alpha': 1.0,
        'title_fontsize': 16,
        'label_fontsize': 12,
        'legend_fontsize': 11,
        'tick_fontsize': 10
    },
    'comparison': {
        'figsize': (12, 8),
        'linewidth': 2.0,
        'grid': True,
        'alpha': 0.7,
        'title_fontsize': 14,
        'label_fontsize': 11,
        'legend_fontsize': 10,
        'tick_fontsize': 10
    }
}

# Define custom colormaps for temperature ranges
def get_temperature_colormap():
    """Return a custom colormap for temperature visualization"""
    from matplotlib.colors import LinearSegmentedColormap

    colors = [
        '#313695',  # Deep blue (cold)
        '#4575B4',  # Blue
        '#74ADD1',  # Light blue
        '#ABD9E9',  # Very light blue
        '#FDAE61',  # Light orange
        '#F46D43',  # Orange
        '#D73027',  # Red
        '#A50026'   # Deep red (hot)
    ]

    return LinearSegmentedColormap.from_list('custom_temp', colors)