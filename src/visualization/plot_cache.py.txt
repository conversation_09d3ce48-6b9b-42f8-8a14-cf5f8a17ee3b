import os
from PIL import Image
from typing import Optional
import gc

from matplotlib import pyplot as plt


class PlotCache:
    """Cache for processed plots to avoid reprocessing"""

    def __init__(self):
        self._cache = {}
        self._cache_dir = None
        self._max_cache_size = 100

    def get_processed_plot(self, plot_path: str) -> Optional[str]:
        if plot_path in self._cache:
            if os.path.exists(self._cache[plot_path]):
                return self._cache[plot_path]
            else:
                del self._cache[plot_path]

        processed_path = self._process_plot(plot_path)
        if processed_path:
            self._add_to_cache(plot_path, processed_path)
        return processed_path

    def _process_plot(self, plot_path: str) -> Optional[str]:
        try:
            output_path = f"{os.path.splitext(plot_path)[0]}_processed.jpg"

            # Use a context manager to ensure file is closed
            with Image.open(plot_path) as img:
                if img.mode in ('RGBA', 'LA'):
                    img = img.convert('RGB')

                if img.size[0] > 1200 or img.size[1] > 800:
                    img.thumbnail((1200, 800), Image.LANCZOS)

                img.save(output_path, 'JPEG', quality=85, optimize=True)

            # Ensure the file handle is released
            plt.close('all')
            gc.collect()

            return output_path
        except Exception as e:
            print(f"Error processing plot {plot_path}: {str(e)}")
            return None

    def _add_to_cache(self, original_path: str, processed_path: str):
        if len(self._cache) >= self._max_cache_size:
            oldest = next(iter(self._cache))
            old_path = self._cache[oldest]
            if os.path.exists(old_path):
                os.remove(old_path)
            del self._cache[oldest]
        self._cache[original_path] = processed_path

    def clear_cache(self):
        """Clear all cached plots"""
        for path in self._cache.values():
            if os.path.exists(path):
                os.remove(path)
        self._cache.clear()
        gc.collect()