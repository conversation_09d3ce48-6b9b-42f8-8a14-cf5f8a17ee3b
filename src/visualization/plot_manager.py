import os
from typing import List

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from .styles import set_plot_style


class PlotManager:
    """
    Manages creation and customization of plots for thruster analysis.

    Features:
    - Temperature and pressure plotting
    - Custom plot creation
    - Range-specific plotting
    - Plot customization and styling
    """

    def __init__(self):
        self.set_style()

        self.color = {'tank bottom': '#01a5e3',
                      'tank mid': '#165152',
                      'tank flange': '#9d004e',
                      'tank lid': '#32c133',
                      'thruster flange': '#fb6238',
                      'thruster chamber': '#400972',
                      'nozzle convergent': '#ee0100',
                      'nozzle exit': '#f7d02e'}

    @staticmethod
    def set_style():
        """Set the default plotting style"""
        set_plot_style()

    # def create_temperature_plots(self, df: pd.DataFrame, output_dir: str) -> None:
    #     """Create and save default temperature analysis plots"""
    #     try:
    #         os.makedirs(output_dir, exist_ok=True)
    #
    #         # Get temperature columns - use all columns except Sam<PERSON> and time
    #         temp_cols = [col for col in df.columns if col not in ['Sample', 'time']]
    #
    #         # Get time column
    #         if 'time' not in df.columns:
    #             df['time'] = df.index * 0.5  # Assuming 0.5 second intervals
    #
    #         # Ensure all data is numeric
    #         df['time'] = pd.to_numeric(df['time'], errors='coerce')
    #         for col in temp_cols:
    #             df[col] = pd.to_numeric(df[col], errors='coerce')
    #
    #         def create_and_save_plot(x_data, y_data, title, filename):
    #
    #             # Creating figure with transparent background
    #             fig, ax = plt.subplots(figsize=(10, 7))
    #
    #             for col in y_data:
    #                 column = col.lower().strip().split()
    #                 column = column[0] + ' ' + column[1]
    #
    #                 ax.plot(x_data,
    #                         df[col],
    #                         label=col,
    #                         linewidth=2,
    #                         color=self.color[column])
    #
    #             # Set figure and axes backgrounds to transparent
    #             fig.patch.set_alpha(0.0)  # Transparent figure background
    #             ax.patch.set_alpha(0.0)  # Transparent axes background
    #
    #             # Update plot settings
    #             ax.set_title(title,
    #                          color='#09090b',
    #                          fontsize=12,
    #                          fontweight='bold')
    #
    #             ax.set_xlabel('Time (s)',
    #                           color='#09090b',
    #                           fontsize=10,
    #                           fontweight='bold')
    #             ax.set_ylabel('Temperature (°C)',
    #                           color='#09090b',
    #                           fontsize=10,
    #                           fontweight='bold')
    #
    #             legend = ax.legend(bbox_to_anchor=(1, 1),
    #                                    loc='upper left',
    #                                    facecolor='none',  # Transparent background
    #                                    edgecolor='#446699'  # Border color
    #                                    )
    #             # Setting legend text color
    #             for text in legend.get_texts():
    #                 text.set_color('#09090b')
    #
    #             # Style the axis numbers and grid
    #             ax.tick_params(axis='both',
    #                                colors='#09090b',  # White tick labels
    #                                labelsize=9)
    #             # Major grids
    #             ax.grid(which='major', linestyle='-', linewidth='0.75', alpha=0.6,
    #                         color='#666666')  # Darker grid lines
    #
    #             # Minor grids
    #             ax.minorticks_on()
    #             ax.grid(which='minor', linestyle=':', linewidth='0.5', color='#1e1e1e', alpha=0.6)
    #
    #             ax.spines['top'].set_visible(True)
    #             ax.spines['right'].set_visible(True)
    #
    #             # Style the spines (axis lines)
    #             for spine in ax.spines.values():
    #                 spine.set_color('#446699')  # Custom spine color
    #
    #             # fig.tight_layout()
    #
    #             plt.savefig(os.path.join(output_dir, filename))
    #             plt.close(fig)
    #
    #
    #
    #         # All plots with consistent styling
    #         plot_configurations = [
    #             ('All Temperature Distributions', temp_cols, 'all_temperatures.png'),
    #             ('Thruster Temperature Distributions', temp_cols[4:], 'thruster_temperatures.png'),
    #             ('Tank Temperature Distributions',
    #              [col for col in temp_cols if 'tank' in col.lower()],
    #              'tank_temperatures.png'),
    #             ('Chamber Temperature Distribution',
    #              [col for col in temp_cols if 'chamber' in col.lower()],
    #              'chamber_temperature.png')
    #         ]
    #
    #         for title, columns, filename in plot_configurations:
    #             if columns:  # Only create plot if there are columns to plot
    #                 create_and_save_plot(df['time'], columns, title, filename)
    #
    #     except Exception as e:
    #         print(f"Error in create_temperature_plots: {str(e)}")
    #         return None

    def create_temperature_plots(self, df: pd.DataFrame, output_dir: str, plot_types: List[str] = None) -> None:
        try:
            os.makedirs(output_dir, exist_ok=True)

            # Default to all plots if plot_types is None
            if plot_types is None:
                plot_types = ['all', 'thruster', 'tank', 'chamber']

            # Get temperature columns
            temp_cols = [col for col in df.columns if col not in ['Sample', 'time']]
            if 'time' not in df.columns:
                df['time'] = df.index * 0.5

            df['time'] = pd.to_numeric(df['time'], errors='coerce')
            for col in temp_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')

            def create_and_save_plot(x_data, y_data, title, filename):
                fig, ax = plt.subplots(figsize=(10, 5.3))
                for col in y_data:
                    column = col.lower().strip().split()
                    column = column[0] + ' ' + column[1]
                    ax.plot(x_data, df[col], label=col, linewidth=2, color=self.color.get(column, '#000000'))
                fig.patch.set_alpha(0.0)
                ax.patch.set_alpha(0.0)
                ax.set_title(title, color='#09090b', fontsize=12, fontweight='bold')
                ax.set_xlabel('Time (s)', color='#09090b', fontsize=10, fontweight='bold')
                ax.set_ylabel('Temperature (°C)', color='#09090b', fontsize=10, fontweight='bold')
                legend = ax.legend(bbox_to_anchor=(0.5, -0.15),
                                   loc='upper center',
                                   ncol=5,
                                   columnspacing=1,
                                   handletextpad=0.5,
                                   borderaxespad=0,
                                   facecolor='none',  # Transparent background
                                   edgecolor='#446699',  # Border color
                                   bbox_transform=ax.transAxes
                                   )
                for text in legend.get_texts():
                    text.set_color('#09090b')
                ax.tick_params(axis='both', colors='#09090b', labelsize=9)
                ax.grid(which='major', linestyle='-', linewidth='0.75', alpha=0.6, color='#666666')
                ax.minorticks_on()
                ax.grid(which='minor', linestyle=':', linewidth='0.5', color='#1e1e1e', alpha=0.6)
                ax.spines['top'].set_visible(True)
                ax.spines['right'].set_visible(True)
                for spine in ax.spines.values():
                    spine.set_color('#446699')
                plt.savefig(os.path.join(output_dir, filename))
                plt.close(fig)

            # Plot configurations
            plot_configurations = {
                'all': ('All Temperature Distributions', temp_cols, 'all_temperatures.png'),
                'thruster': (
                'Thruster Temperature Distributions', [col for col in temp_cols if 'thruster' in col.lower()],
                'thruster_temperatures.png'),
                'tank': ('Tank Temperature Distributions', [col for col in temp_cols if 'tank' in col.lower()],
                         'tank_temperatures.png'),
                'chamber': ('Chamber Temperature Distribution', [col for col in temp_cols if 'chamber' in col.lower()],
                            'chamber_temperature.png')
            }

            for plot_type in plot_types:
                if plot_type in plot_configurations:
                    title, columns, filename = plot_configurations[plot_type]
                    if columns:
                        create_and_save_plot(df['time'], columns, title, filename)

        except Exception as e:
            print(f"Error in create_temperature_plots: {str(e)}")