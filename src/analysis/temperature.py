import matplotlib
matplotlib.use('Agg')
from matplotlib import pyplot as plt
import logging
import os
import numpy as np
import pandas as pd
from typing import Dict, List, Optional
from ..visualization import PlotManager


class TemperatureAnalyzer:
    """
    Analyzes temperature data from thruster tests.
    """

    def __init__(self):
        self.plot_manager = PlotManager()
        self.analysis_results = {}
        self.max_temperature_data = {
            'value': None,
            'location': None,
            'time': None
        }
        # self.logger = logging.getLogger(__name__)

    # def analyze_temperature_data(self, data: pd.DataFrame, output_dir: str, save_plot: bool = True, default_plot: bool = True) -> Dict:
    #     """
    #     Perform comprehensive temperature data analysis with improved error handling.
    #     """
    #     try:
    #         if data is None or data.empty:
    #             raise ValueError("No valid data provided for analysis")
    #
    #         results = {}
    #
    #         # Get temperature columns
    #         temp_cols = [col for col in data.columns if col not in ['Sample', 'time']]
    #         if not temp_cols:
    #             raise ValueError("No temperature columns found")
    #
    #         # Calculate maximum temperatures and their times
    #         max_temps = {}
    #         overall_max = float('-inf')
    #         max_loc = None
    #         max_time = None
    #
    #         for col in temp_cols:
    #             # Skip columns with all NaN values
    #             if data[col].isna().all():
    #                 continue
    #
    #             max_val = data[col].max()
    #             if pd.notna(max_val):
    #                 max_idx = data[col].idxmax()
    #                 if pd.notna(max_idx):
    #                     max_time_val = data.loc[max_idx, 'time']
    #                     max_temps[col] = {
    #                         'value': max_val,
    #                         'time': max_time_val
    #                     }
    #
    #                     if max_val > overall_max:
    #                         overall_max = max_val
    #                         max_loc = col
    #                         max_time = max_time_val
    #
    #         if not max_temps:
    #             raise ValueError("No valid maximum temperatures found")
    #
    #         # Store results
    #         self.max_temperature_data = {
    #             'value': overall_max,
    #             'location': max_loc,
    #             'time': max_time,
    #             'value_kelvin': overall_max + 273.15
    #         }
    #
    #         results['max_temperatures'] = max_temps
    #
    #         if default_plot:
    #             # Generate temperature analysis table
    #             if output_dir:
    #                 self.generate_temperature_table(data, output_dir)
    #
    #             # Create default plots if requested
    #             if save_plot:
    #                 self.plot_manager.create_temperature_plots(data, output_dir)
    #
    #         return results
    #
    #     except Exception as e:
    #         print(f"Error in temperature analysis: {str(e)}")
    #         import traceback
    #         traceback.print_exc()
    #         return {}

    def analyze_temperature_data(self, data: pd.DataFrame, output_dir: str, save_plot: bool = True,
                                 plot_types: List[str] = None) -> Dict:
        try:
            if data is None or data.empty:
                raise ValueError("No valid data provided for analysis")

            results = {}

            # Get temperature columns
            temp_cols = [col for col in data.columns if col not in ['Sample', 'time']]
            if not temp_cols:
                raise ValueError("No temperature columns found")

            # Calculate maximum temperatures and their times
            max_temps = {}
            overall_max = float('-inf')
            max_loc = None
            max_time = None

            for col in temp_cols:
                if data[col].isna().all():
                    continue
                max_val = data[col].max()
                if pd.notna(max_val):
                    max_idx = data[col].idxmax()
                    if pd.notna(max_idx):
                        max_time_val = data.loc[max_idx, 'time']
                        max_temps[col] = {'value': max_val, 'time': max_time_val}
                        if max_val > overall_max:
                            overall_max = max_val
                            max_loc = col
                            max_time = max_time_val

            if not max_temps:
                raise ValueError("No valid maximum temperatures found")

            self.max_temperature_data = {
                'value': overall_max,
                'location': max_loc,
                'time': max_time,
                'value_kelvin': overall_max + 273.15
            }
            results['max_temperatures'] = max_temps

            # Generate temperature analysis table
            if output_dir:
                self.generate_temperature_table(data, output_dir)

            # Create specified plots if requested
            if save_plot:
                self.plot_manager.create_temperature_plots(data, output_dir, plot_types=plot_types)

            return results

        except Exception as e:
            print(f"Error in temperature analysis: {str(e)}")
            import traceback
            traceback.print_exc()
            return {}

    def _create_thruster_temps_plot(self, data, temp_cols, save_path):
        """Create plot showing thruster temperatures"""
        thruster_cols = [col for col in temp_cols if 'thruster' in col.lower()]
        if thruster_cols:
            fig, ax = plt.subplots(figsize=(15, 8))
            for col in thruster_cols:
                ax.plot(data['time'], data[col], label=col, linewidth=2)
            ax.set_title('Thruster Temperature Distributions')
            ax.set_xlabel('Time (s)')
            ax.set_ylabel('Temperature (°C)')
            ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            ax.grid(True)
            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close(fig)

    def generate_temperature_table(self, data: pd.DataFrame, output_dir: str = None) -> None:
        """
        Generating temperature analysis table using preprocessed data.
        """
        try:
            # Get temperature columns
            temp_cols = [col for col in data.columns if col not in ['time', 'Time', 'Time/Date']]

            rows = []
            columns = []

            for col in temp_cols:

                rows.append('Max' + ' ' + col)
                columns.append('Corresponding' + ' ' + col)

            # Create DataFrame for analysis
            analysis_df = pd.DataFrame(index=rows, columns=columns)

            # For each column, find its maximum temperature and associated time
            for col in temp_cols:
                # Find maximum temperature index for this column
                max_temp_idx = data[col].idxmax()

                # Get the maximum temperature and its time
                max_temp = data.loc[max_temp_idx, col]
                max_time = data.loc[max_temp_idx, 'time']

                # Fill in this column's data
                for row in temp_cols:
                    # Get temperature at this time
                    temp = data.loc[max_temp_idx, row]

                    # Format the value
                    if row == col:  # Diagonal - show max temp and time
                        analysis_df.at['Max' + ' ' + row, 'Corresponding' + ' ' + col] = f"[{max_temp:.2f} @ t = {max_time:.1f}s]"
                    else:  # Other values
                        analysis_df.at['Max' + ' ' + row, 'Corresponding' + ' ' + col] = f"{temp:.2f}"

            # Save results
            if output_dir is None:
                pass
            else:
                csv_path = os.path.join(output_dir, 'temperature_analysis.csv')
                analysis_df.to_csv(csv_path)

            self.analysis_results = {'matrix': analysis_df}

        except Exception as e:
            print(f"Error generating temperature table: {str(e)}")
            raise

    # def create_custom_temperature_plot_gui(self, data: pd.DataFrame, x_col: str, y_cols: List[str],
    #                                        return_fig: bool = False, title: str = None):
    #     """
    #     Create custom temperature plot with improved data handling.
    #
    #     Args:
    #         data (pd.DataFrame): Temperature data
    #         x_col (str): Column name for x-axis
    #         y_cols (List[str]): List of column names for y-axis
    #         return_fig (bool): If True, returns the figure instead of showing it
    #         title (str): Plot title
    #
    #     Returns:
    #         Optional[plt.Figure]: Matplotlib figure if return_fig is True
    #     """
    #     try:
    #         # Create figure and axis
    #         fig, ax = plt.subplots(figsize=(10, 6))
    #
    #         # Verify and prepare x-axis data
    #         if x_col == 'time' and data[x_col].isna().any():
    #             print("Warning: Time column contains NaN values, creating new time values")
    #             x_data = np.arange(len(data)) * 0.5  # Create new time values
    #         else:
    #             x_data = pd.to_numeric(data[x_col], errors='coerce')
    #
    #         # Verify x_data
    #         if x_data.isna().all():
    #             raise ValueError(f"No valid data for x-axis column: {x_col}")
    #
    #         # Plot each y column against x
    #         for y_col_list in y_cols:  # Handle nested list structure
    #             if isinstance(y_col_list, str):
    #                 # If a single string is passed, convert to list
    #                 y_columns = [y_col_list]
    #             else:
    #                 y_columns = y_col_list
    #
    #             for y_col in y_columns:
    #                 # Convert y data to numeric
    #                 y_data = pd.to_numeric(data[y_col], errors='coerce')
    #
    #                 # Verify y_data
    #                 if not y_data.isna().all():
    #                     # Create plot
    #                     ax.plot(x_data, y_data, label=y_col, linewidth=1.5)
    #                 else:
    #                     print(f"Warning: No valid data for y-axis column: {y_col}")
    #
    #         # Customize the plot
    #         ax.set_title(title or 'Temperature Analysis')
    #         ax.set_xlabel('Time (s)' if x_col == 'time' else x_col)
    #         ax.set_ylabel('Temperature (°C)')
    #         ax.grid(True, alpha=0.3)
    #         ax.spines['top'].set_visible(True)
    #         ax.spines['right'].set_visible(True)
    #         ax.spines['bottom'].set_visible(True)
    #         ax.spines['left'].set_visible(True)
    #
    #         # Adjust legend
    #         ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    #         plt.tight_layout()  # Adjust layout to prevent label cutoff
    #
    #         if return_fig:
    #             return fig
    #
    #         plt.show()
    #         plt.close(fig)
    #
    #     except Exception as e:
    #         print(f"Error in custom temperature plot: {str(e)}")
    #         return None