"""
Pressure Analysis Module
----------------------
Handles analysis of pressure data from thruster tests.
"""

import pandas as pd
import numpy as np
from typing import Optional
from matplotlib import pyplot as plt
from ..visualization import PlotManager

class PressureAnalyzer:
    """
    Analyzes pressure data from thruster tests.

    Features:
    - Pressure trend analysis
    - Statistical analysis
    - Range-specific analysis
    - Anomaly detection
    """

    def __init__(self):
        self.plot_manager = PlotManager()
        self.analysis_results = {}

    def plot_pressure_analysis(self, data: pd.DataFrame, x_col: str, y_col: str,
                               x_min: Optional[float] = None, x_max: Optional[float] = None) -> None:
        """
        Create pressure analysis plot with optional range selection.

        Args:
            data: DataFrame containing pressure data
            x_col: Column name for x-axis
            y_col: Column name for y-axis
            x_min: Optional minimum x value for range selection
            x_max: Optional maximum x value for range selection
        """
        try:
            self.plot_manager.plot_pressure_analysis(data, x_col, y_col, x_min, x_max)
        except Exception as e:
            print(f"Error creating pressure plot: {str(e)}")

    def plot_pressure_analysis_gui(self, data, x_col, y_cols, x_min=None, x_max=None, return_fig=True, title=None):
        """Create pressure analysis plot with multiple Y columns"""
        try:
            # Create figure with two subplots
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 10), height_ratios=[1, 1])

            colors = plt.cm.Spectral(np.linspace(0, 1, len(y_cols)))


            # Plot full data for each y column
            for y_col, color in zip(y_cols, colors):
                ax1.plot(data[x_col], data[y_col], color=color, alpha=1, label=f'{y_col} (Full)')

            if x_min is not None and x_max is not None:
                mask = (data[x_col] >= x_min) & (data[x_col] <= x_max)
                range_data = data[mask]

                if not range_data.empty:
                    # Plot each y column in the range
                    for y_col, color in zip(y_cols, colors):
                        ax1.plot(range_data[x_col], range_data[y_col],
                                 color=color, linestyle='-', label=f'{y_col} (Range)')

                        # Calculate and plot average
                        avg = range_data[y_col].mean()
                        ax1.axhline(y=avg, color=color, linestyle='--',
                                    label=f'{y_col} Avg = {avg:.2f}')

                        # Plot in bottom subplot
                        ax2.plot(range_data[x_col], range_data[y_col],
                                 color=color, label=y_col)
                        ax2.axhline(y=avg, color=color, linestyle='--')

                    # Add range boundaries
                    ax1.axvline(x=x_min, color='g', linestyle=':', alpha=0.5)
                    ax1.axvline(x=x_max, color='g', linestyle=':', alpha=0.5)

            ax1.set_title('Full Range View')
            ax2.set_title('Selected Range View' if x_min is not None else 'Full Data View')

            if x_col.lower() == 'time':
                ax1.set_xlabel('Time (s)')
                ax2.set_xlabel('Time (s)')
            else:
                ax1.set_xlabel(x_col)
                ax2.set_xlabel(x_col)

            ax1.set_ylabel('mbar')
            ax2.set_ylabel('mbar')

            ax1.grid(True)
            ax2.grid(True)
            ax1.legend()
            ax2.legend()

            plt.tight_layout()

            if return_fig:
                return fig
            plt.show()
            plt.close(fig)

        except Exception as e:
            print(f"Error in pressure analysis plot: {str(e)}")
            return None

    def get_range_statistics(self, data, x_col, y_col, x_min, x_max):
        """Calculate statistics for a specific range of data."""
        try:
            mask = (data[x_col] >= x_min) & (data[x_col] <= x_max)
            df_filtered = data[mask]

            stats = {
                'mean': float(df_filtered[y_col].mean()),
                'std': float(df_filtered[y_col].std()),
                'min': float(df_filtered[y_col].min()),
                'max': float(df_filtered[y_col].max()),
                'median': float(df_filtered[y_col].median()),
                'range': float(df_filtered[y_col].max() - df_filtered[y_col].min()),
                'data_points': len(df_filtered),
                'duration': float(df_filtered[x_col].max() - df_filtered[x_col].min())
            }

            return stats

        except Exception as e:
            print(f"Error calculating range statistics: {str(e)}")
            return {}




