from typing import Dict
import numpy as np
import pandas as pd


class Performance:
    """
    Calculates various performance parameters of the system
    """

    def __init__(self):
        self.gas_constant = 372.097738 # Default value in J/kgK
        self.gamma = 1.66 # Default Value
        self.g0 = 9.80665 # m/s^2

    def set_properties(self, gas_constant: float, gamma: float):
        """Setting the gas constant value"""
        self.gas_constant = gas_constant
        self.gamma = gamma

    @staticmethod
    def calculate_average_pressure(data: pd.DataFrame, column: str,
                                   range_min: float = None, range_max: float = None) -> float:
        """
        Calculate average pressure within specified range.

        Args:
            data: DataFrame containing pressure data
            column: Column name for pressure data
            range_min: Minimum value for range selection
            range_max: Maximum value for range selection

        Returns:
            float: Average pressure value
        """
        try:
            # Find time column
            time_col = next((col for col in data.columns if 'time'  in col.lower()), None)
            if not time_col:
                # Create time column if it doesn't exist
                data['time'] = data.index * 0.5  # Assuming 0.5 second intervals
                time_col = data['time']

            print(f'{column} contains values {data[column]}')
            print(f'Average Pressure is ')

            if range_min >= 0 and range_max > 0:
                # Select data within range
                mask = (data[time_col] >= range_min) & (data[time_col] <= range_max)
                filtered_data = data.loc[mask, column]

                if filtered_data.empty:
                    raise ValueError(f"No data found in specified range for {column}")

                avg_pressure = filtered_data.mean()

                return avg_pressure
            elif range_min == 0 and range_max == 0:
                # Use all data if no range specified
                avg_pressure = data[column].mean()

                return avg_pressure

        except Exception as e:
            raise ValueError(f"Error calculating average pressure for {column}: {str(e)}")

    def characteristic_velocity(self, chamber_temperature: float):
        """
               Calculate characteristic velocity (c*)

               Args:
                   chamber_temperature: Chamber temperature in Kelvin

               Returns:
                   float: Characteristic velocity in m/s
               """
        return np.sqrt((1/self.gamma) * ((self.gamma + 1)/2)**((self.gamma + 1)/(self.gamma - 1)) *
                       self.gas_constant * chamber_temperature)

    @staticmethod
    def mass_flow_rate(initial_mass: float, unused_mass: float, firing_time: float) -> float:
        """
        Calculate average mass flow rate

        Args:
            initial_mass: Initial propellant mass in g
            unused_mass: Unused propellant mass in g
            firing_time: Total firing time in seconds

        Returns:
            float: Mass flow rate in mg/s
        """
        return (((initial_mass - unused_mass) * 10**-3) / firing_time) * 10**6

    def thrust_coefficient(self, tank_pressure: float, vacuum_pressure: float) -> float:
        """
        Calculate thrust coefficient

        Args:
            tank_pressure: Tank pressure in mBar
            vacuum_pressure: Vacuum chamber pressure in mBar

        Returns:
            float: Thrust coefficient (dimensionless)
        """
        return np.sqrt((2 * self.gamma ** 2 / (self.gamma - 1)) *
                       (2 / (self.gamma + 1))**((self.gamma + 1) / (self.gamma - 1)) *
                       (1 - (vacuum_pressure / tank_pressure)**((self.gamma + 1) / self.gamma)))

    def calculate_performance_parameters(self,
                                      pressure_data: pd.DataFrame,
                                      chamber_temp: float,
                                      initial_mass: float,
                                      unused_mass: float,
                                      burn_time: float,
                                      tank_press_range: tuple = None,
                                      vac_press_range: tuple = None) -> Dict[str, float]:
        """
        Calculate all performance parameters using range-based pressure averages.
        """
        try:
            # Calculate average pressures using specified ranges
            chamber_min, chamber_max = tank_press_range if tank_press_range else (None, None)
            vac_min, vac_max = vac_press_range if vac_press_range else (None, None)

            # Find the actual column names for pressures
            tank_col = next((col for col in pressure_data.columns if 'tank' in col.lower()), None)
            vac_col = next((col for col in pressure_data.columns if 'vac' in col.lower()), None)
            thruster_chamber_col = next((col for col in pressure_data.columns if 'thruster' in col.lower()), None)

            print(f'tank pressure column is \n{tank_col}')
            print(f'vacuum pressure column is \n{vac_col}')
            print(f"thruster chamber column is \n{thruster_chamber_col}")

            if not tank_col or not vac_col:
                raise ValueError("Could not find tank or vacuum pressure columns")

            # Calculate average pressures
            if thruster_chamber_col is not None:
                tank_pressure = self.calculate_average_pressure(
                    pressure_data, thruster_chamber_col, chamber_min, chamber_max)
            else:
                tank_pressure = self.calculate_average_pressure(
                    pressure_data, tank_col, chamber_min, chamber_max)
            print(f'Chamber Pressure is {tank_pressure}')
            vacuum_pressure = self.calculate_average_pressure(
                pressure_data, vac_col, vac_min, vac_max)
            print(f'Vacuum pressure is {vacuum_pressure}')

            # Rest of calculations
            c_star = self.characteristic_velocity(chamber_temp)
            mdot = self.mass_flow_rate(initial_mass, unused_mass, burn_time)    # milli grams per second
            cf = self.thrust_coefficient(tank_pressure, vacuum_pressure)
            thrust = mdot * c_star * cf * 10**(-3)                              # milli Newton
            isp = (thrust / (mdot * self.g0)) * 10**3                           # second
            total_impulse = thrust * burn_time * 10**(-3)                       # Newton Second

            return {
                'characteristic_velocity': c_star,
                'mass_flow_rate': mdot,
                'thrust_coefficient': cf,
                'thrust': thrust,
                'specific_impulse': isp,
                'total_impulse': total_impulse,
                'burn_time': burn_time,
                'chamber_temp': chamber_temp,
                'tank_pressure': tank_pressure,
                'vacuum_pressure': vacuum_pressure
            }

        except Exception as e:
            print(f"Error calculating performance parameters: {str(e)}")
            raise

    @staticmethod
    def format_performance_results(results: Dict[str, float]) -> Dict[str, str]:
        """
        Format the numerical results with appropriate units

        Args:
            results: Dictionary of calculated values

        Returns:
            Dictionary of formatted strings with units
        """
        return {
            'characteristic_velocity': f"{results['characteristic_velocity']:.3f} m/s",
            'mass_flow_rate': f"{results['mass_flow_rate']:.3f} mg/s",
            'thrust_coefficient': f"{results['thrust_coefficient']:.4f}",
            'thrust': f"{results['thrust']:.3f} mN",
            'specific_impulse': f"{results['specific_impulse']:.3f} s",
            'total_impulse': f"{results['total_impulse']:.3f} Ns",
            'burn_time': f"{results['burn_time']:.2f} s"
        }

    def get_time_column_name(self, data: pd.DataFrame) -> str:
        """
        Find the time column name in the pressure data.

        Args:
            data: DataFrame to search

        Returns:
            str: Name of time column
        """
        possible_names = ['time', 'Time', 'TIME', 'Time (s)', 'time (s)']
        for name in possible_names:
            if name in data.columns:
                return name

        # If no standard name found, look for any column with 'time' in it
        time_cols = [col for col in data.columns if 'time' in col.lower()]
        if time_cols:
            return time_cols[0]

        # If still no time column, create one based on index
        data['time'] = data.index * 0.5  # Assuming 0.5 second intervals
        return 'time'

