"""
Image Handler Module for Test Report Generation
--------------------------------------------
Handles image processing, optimization, and caching for report generation.
"""
from datetime import datetime
from PIL import Image
import os
from typing import Dict, List, Tuple, Optional
import logging


class ImageHandler:
    """
        Handles image processing, optimization, and caching for plots and photos.
    """
    def __init__(self):
        """Initialize image handler with caching and logging"""
        self._image_cache = {}
        self._dimension_cache = {}
        self.supported_formats = ['.png', '.jpg', '.jpeg']

        # Configure logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def optimize_image(self, image_path: str, max_size_mb: float = 2.0,
                       max_dimensions: Tuple[int, int] = (1000, 1000)) -> Optional[str]:
        """Optimize image for report inclusion."""
        try:
            if not os.path.exists(image_path):
                self.logger.error(f"Image file not found: {image_path}")
                return None

            file_size = os.path.getsize(image_path) / (1024 * 1024)  # Convert to MB

            # Check cache first
            if image_path in self._image_cache:
                if os.path.exists(self._image_cache[image_path]):
                    return self._image_cache[image_path]
                else:
                    self._image_cache.pop(image_path)

            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'LA'):
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    background.paste(img, mask=img.split()[-1])
                    img = background
                elif img.mode != 'RGB':
                    img = img.convert('RGB')

                # Calculate new size while maintaining aspect ratio
                ratio = min(max_dimensions[0] / img.width,
                            max_dimensions[1] / img.height)
                if ratio < 1:  # Only resize if image is larger than max dimensions
                    new_size = (int(img.width * ratio), int(img.height * ratio))
                    img = img.resize(new_size, Image.Resampling.LANCZOS)

                # Create optimized version
                optimized_path = f"{os.path.splitext(image_path)[0]}_optimized.jpg"
                img.save(optimized_path, 'JPEG', quality=85, optimize=True)

                # Cache and log
                self._image_cache[image_path] = optimized_path
                self.logger.info(f"Optimized {image_path} from {file_size:.2f}MB")
                return optimized_path

        except Exception as e:
            self.logger.error(f"Error optimizing image {image_path}: {str(e)}")
            return None

    def calculate_dimensions(self, image_path: str, max_width: float) -> Tuple[float, float]:
        """
        Calculate image dimensions while maintaining aspect ratio

        Args:
            image_path: Path to image file
            max_width: Maximum allowed width

        Returns:
            Tuple of (width, height)
        """
        try:
            # Check cache first
            cache_key = (image_path, max_width)
            if cache_key in self._dimension_cache:
                return self._dimension_cache[cache_key]

            with Image.open(image_path) as img:
                aspect = img.height / img.width
                width = min(max_width, img.width)
                height = width * aspect

                dimensions = (width, height)
                self._dimension_cache[cache_key] = dimensions
                return dimensions

        except Exception as e:
            self.logger.error(f"Error calculating dimensions for {image_path}: {str(e)}")
            return max_width, max_width  # Safe default

    def batch_process_images(self, paths: List[str]) -> Dict[str, str]:
        """
        Process multiple images in batch

        Args:
            paths: List of image paths to process

        Returns:
            Dictionary mapping original paths to processed paths
        """
        processed = {}
        for path in paths:
            try:
                processed[path] = self.optimize_image(path)
            except Exception as e:
                self.logger.error(f"Error processing image {path}: {str(e)}")
                processed[path] = path
        return processed

    def get_image_info(self, image_path: str) -> Dict[str, any]:
        """
        Get detailed information about an image

        Args:
            image_path: Path to image file

        Returns:
            Dictionary containing image information
        """
        try:
            with Image.open(image_path) as img:
                return {
                    'size': os.path.getsize(image_path),
                    'format': img.format,
                    'mode': img.mode,
                    'dimensions': img.size,
                    'dpi': img.info.get('dpi'),
                }
        except Exception as e:
            self.logger.error(f"Error getting image info for {image_path}: {str(e)}")
            return {}

    def prepare_image_for_report(self, image_path: str, output_dir: str,
                                 max_height: int = 150) -> Optional[str]:
        """
        Prepare image for inclusion in report.

        Args:
            image_path: Path to source image
            output_dir: Directory for processed images
            max_height: Maximum height in report (maintain aspect ratio)

        Returns:
            Path to prepared image or None if failed
        """
        try:
            # Validate image
            is_valid, error = self.validate_image(image_path)
            if not is_valid:
                self.logger.error(f"Invalid image: {error}")
                return None

            # Create unique filename for processed image
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"report_image_{timestamp}.jpg"
            output_path = os.path.join(output_dir, filename)

            # Process image
            with Image.open(image_path) as img:
                # Convert to RGB if needed
                if img.mode in ('RGBA', 'LA'):
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    background.paste(img, mask=img.split()[-1])
                    img = background

                # Calculate dimensions maintaining aspect ratio
                ratio = max_height / img.height
                new_width = int(img.width * ratio)
                img = img.resize((new_width, max_height), Image.Resampling.LANCZOS)

                # Save processed image
                img.save(output_path, 'JPEG', quality=85, optimize=True)

            self.logger.info(f"Prepared image for report: {output_path}")
            return output_path

        except Exception as e:
            self.logger.error(f"Error preparing image for report: {str(e)}")
            return None

    def clear_cache(self, delete_files: bool = False):
        """
        Clear image caches and optionally delete optimized files

        Args:
            delete_files: If True, delete optimized image files
        """
        if delete_files:
            for optimized_path in self._image_cache.values():
                try:
                    if os.path.exists(optimized_path) and optimized_path.endswith('_optimized.jpg'):
                        os.remove(optimized_path)
                except Exception as e:
                    self.logger.error(f"Error deleting optimized file {optimized_path}: {str(e)}")

        self._image_cache.clear()
        self._dimension_cache.clear()

    def cleanup_temporary_images(self):
        """Clean up any temporary optimized images"""
        try:
            for optimized_path in self._image_cache.values():
                if os.path.exists(optimized_path) and '_optimized.' in optimized_path:
                    os.remove(optimized_path)
            self._image_cache.clear()
            self._dimension_cache.clear()
            self.logger.info("Cleaned up temporary images")
        except Exception as e:
            self.logger.error(f"Error cleaning up temporary images: {str(e)}")

    def validate_image(self, image_path: str) -> Tuple[bool, Optional[str]]:
        """
        Validate if file is a supported and valid image.

        Args:
            image_path: Path to image file

        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            if not os.path.exists(image_path):
                return False, "File does not exist"

            ext = os.path.splitext(image_path)[1].lower()
            if ext not in self.supported_formats:
                return False, f"Unsupported format. Supported formats: {', '.join(self.supported_formats)}"

            with Image.open(image_path) as img:
                img.verify()

                # Get image info for logging
                info = {
                    'format': img.format,
                    'mode': img.mode,
                    'dimensions': img.size
                }
                self.logger.info(f"Validated image: {image_path} - {info}")
                return True, None

        except Exception as e:
            return False, str(e)

    def _optimize_plot(self, plot_path: str) -> str:
        """Optimize plot for PDF inclusion"""
        try:
            # If already optimized, return cached path
            if plot_path in self._image_cache:
                return self._image_cache[plot_path]

            with Image.open(plot_path) as img:
                # If image is already small enough, use as-is
                file_size = os.path.getsize(plot_path) / (1024 * 1024)  # Size in MB
                if file_size <= 0.2:  # If 200KB or smaller
                    return plot_path

                # Resize if larger than needed
                max_size = (1500, 1500)  # Maximum dimensions
                if img.size[0] > max_size[0] or img.size[1] > max_size[1]:
                    img.thumbnail(max_size, Image.Resampling.LANCZOS)

                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'LA'):
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    background.paste(img, mask=img.split()[-1])
                    img = background

                # Create optimized version
                optimized_path = f"{os.path.splitext(plot_path)[0]}_optimized.jpg"
                img.save(optimized_path, 'JPEG', quality=85, optimize=True)

                self._image_cache[plot_path] = optimized_path
                self.logger.info(f"Optimized {plot_path} from {file_size:.2f}MB")
                return optimized_path

        except Exception as e:
            self.logger.error(f"Error optimizing plot {plot_path}: {str(e)}")
            return plot_path