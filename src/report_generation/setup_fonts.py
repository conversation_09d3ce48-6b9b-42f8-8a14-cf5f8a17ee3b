import os
import sys

# Global variable to store the fonts directory path
FONTS_DIR = None

def get_fonts_dir():
    """Get the fonts directory path - simple and straightforward approach."""
    global FONTS_DIR

    if FONTS_DIR is not None:
        return FONTS_DIR

    try:
        # Multiple strategies to find the fonts directory
        possible_paths = []

        # For PyInstaller builds, use _MEIPASS
        if hasattr(sys, '_MEIPASS'):
            possible_paths.append(os.path.join(sys._MEIPASS, 'fonts'))

        # Strategy 1: Use __file__ to go up to project root
        if '__file__' in globals():
            base_path = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            possible_paths.append(os.path.join(base_path, 'fonts'))

        # Strategy 2: Use current working directory
        possible_paths.append(os.path.join(os.getcwd(), 'fonts'))

        # Strategy 3: Look for gui_main.py and use its directory
        for root, dirs, files in os.walk(os.getcwd()):
            if 'gui_main.py' in files:
                possible_paths.append(os.path.join(root, 'fonts'))
                break

        # Strategy 4: Check common project structure patterns
        current_dir = os.getcwd()
        while current_dir != os.path.dirname(current_dir):  # Not at root
            if os.path.exists(os.path.join(current_dir, 'gui_main.py')):
                possible_paths.append(os.path.join(current_dir, 'fonts'))
                break
            current_dir = os.path.dirname(current_dir)

        # Test each possible path
        required_fonts = ['DejaVuSansCondensed.ttf', 'DejaVuSansCondensed-Bold.ttf']

        for fonts_dir in possible_paths:
            if os.path.exists(fonts_dir):
                all_fonts_present = all(
                    os.path.exists(os.path.join(fonts_dir, font))
                    for font in required_fonts
                )

                if all_fonts_present:
                    print(f"Found fonts directory: {fonts_dir}")
                    FONTS_DIR = fonts_dir
                    return fonts_dir

        # If no valid fonts directory found, use the first possible path for error reporting
        fallback_path = possible_paths[0] if possible_paths else os.path.join(os.getcwd(), 'fonts')
        print(f"No valid fonts directory found. Using fallback: {fallback_path}")
        FONTS_DIR = fallback_path
        return fallback_path

    except Exception as e:
        print(f"Error determining fonts directory: {str(e)}")
        # Last resort fallback
        fallback_path = os.path.join(os.getcwd(), 'fonts')
        FONTS_DIR = fallback_path
        return fallback_path

def setup_fonts():
    """Setup fonts by verifying they exist in the fonts directory."""
    try:
        fonts_dir = get_fonts_dir()
        required_fonts = [
            'DejaVuSansCondensed.ttf',
            'DejaVuSansCondensed-Bold.ttf'
        ]

        print(f"Checking for fonts in: {fonts_dir}")

        if not os.path.exists(fonts_dir):
            print(f"ERROR: Fonts directory not found: {fonts_dir}")
            return False

        # Check if all required fonts exist
        missing_fonts = []
        for font in required_fonts:
            font_path = os.path.join(fonts_dir, font)
            if not os.path.exists(font_path):
                missing_fonts.append(font)

        if missing_fonts:
            print(f"ERROR: Missing required fonts: {missing_fonts}")
            print(f"Expected location: {fonts_dir}")
            return False

        print(f"SUCCESS: All required fonts found in: {fonts_dir}")
        return True

    except Exception as e:
        print(f"Error checking fonts: {str(e)}")
        return False


if __name__ == "__main__":
    setup_fonts()