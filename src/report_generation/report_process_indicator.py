import os
import shutil
from PySide6.QtCore import QObject, Signal, QThread, Qt
from PySide6.QtWidgets import <PERSON><PERSON><PERSON>og, QVBoxLayout, QLabel, QPushButton
from .report_generator import TestReportGenerator


class ReportGeneratorWorker(QObject):
    progress = Signal(str)
    finished = Signal(str)
    error = Signal(str)

    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.report_generator = None
        self.test_data = None
        self.photo_paths = None
        self.plot_paths = None
        self.temp_dir = None
        self.output_path = None

    def ensure_directory_exists(self, directory):
        """Ensure directory exists and is writable"""
        try:
            os.makedirs(directory, exist_ok=True)
            # Test if directory is writable
            test_file = os.path.join(directory, 'test.txt')
            with open(test_file, 'w') as f:
                f.write('test')
            os.remove(test_file)
            return True
        except Exception as e:
            self.error.emit(f"Error creating/accessing directory {directory}: {str(e)}")
            return False

    def generate_report(self):
        try:
            self.progress.emit("Validating test data...")
            if not self.main_window.test_data.get('test_no'):
                self.error.emit("Test number is required. Please enter test number in Basic Information.")
                return

            self.test_data = self.main_window.test_data

            # Create temporary directory
            self.progress.emit("Setting up temporary directory...")
            import tempfile
            self.temp_dir = tempfile.mkdtemp()
            if not self.ensure_directory_exists(self.temp_dir):
                return

            # Setup output path
            self.output_path = os.path.join(self.temp_dir, f"Test_Report_{self.test_data['test_no']}.pdf")

            # Process photos
            self.progress.emit("Processing photos...")
            self.photo_paths = self.main_window.get_photo_paths()

            # Create plots directory
            self.progress.emit("Setting up plots directory...")
            plots_dir = os.path.join(self.temp_dir, 'plots')
            if not self.ensure_directory_exists(plots_dir):
                return

            # Create type-specific plot directories
            temp_plots_dir = os.path.join(plots_dir, 'temperature')
            pressure_plots_dir = os.path.join(plots_dir, 'pressure')
            if not all(self.ensure_directory_exists(d) for d in [temp_plots_dir, pressure_plots_dir]):
                return

            # Process plots
            self.progress.emit("Processing plots...")
            try:
                report_plots = getattr(self.main_window, 'report_plots', {'default': [], 'custom': []})
                for category in ['default', 'custom']:
                    for plot_info in report_plots.get(category, []):
                        if plot_info and 'path' in plot_info and os.path.exists(plot_info['path']):
                            plot_type = plot_info.get('type', 'temperature')
                            dest_dir = temp_plots_dir if plot_type == 'temperature' else pressure_plots_dir
                            dest_path = os.path.join(dest_dir, os.path.basename(plot_info['path']))
                            shutil.copy2(plot_info['path'], dest_path)
            except Exception as e:
                self.error.emit(f"Error processing plots: {str(e)}")
                return

            # Set up plot paths
            self.plot_paths = {
                'temperature': temp_plots_dir,
                'pressure': pressure_plots_dir
            }

            # Generate PDF
            self.progress.emit("Generating PDF report...")
            try:
                self.report_generator = TestReportGenerator(self.temp_dir)
                self.report_generator.test_data = self.test_data
                preview_path = self.report_generator.generate_pdf(self.plot_paths, self.photo_paths)

                if os.path.exists(preview_path):
                    self.finished.emit(preview_path)
                else:
                    self.error.emit("Failed to generate PDF file")
            except Exception as e:
                self.error.emit(f"Error generating PDF: {str(e)}")
                return

        except Exception as e:
            self.error.emit(f"Error in report generation: {str(e)}")
        finally:
            # Cleanup temporary directory in case of error
            try:
                if hasattr(self, 'temp_dir') and self.temp_dir and os.path.exists(self.temp_dir):
                    shutil.rmtree(self.temp_dir, ignore_errors=True)
            except Exception as e:
                print(f"Error cleaning up temporary directory: {str(e)}")


class ReportProgressDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Generating Report")
        self.setFixedSize(400, 150)

        layout = QVBoxLayout(self)

        # Progress label
        self.progress_label = QLabel("Initializing...")
        self.progress_label.setAlignment(Qt.AlignCenter)
        self.progress_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                padding: 10px;
            }
        """)
        layout.addWidget(self.progress_label)

        # Cancel button
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #994444;
                border-radius: 5px;
                padding: 8px 15px;
                color: white;
            }
            QPushButton:hover {
                background-color: #aa5555;
            }
        """)
        layout.addWidget(self.cancel_button)

    def update_progress(self, message):
        self.progress_label.setText(message)