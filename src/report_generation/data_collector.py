"""
Data Collection Module for Test Report Generation
----------------------------------------------
<PERSON><PERSON> collection of all test-related data through user input.
"""


class TestDataCollector:
    @staticmethod
    def collect_test_info():
        """Collect basic test information"""
        print("\nTest Information Input")
        print("=" * 22)
        return {
            'test_no': input("Test No.: ").strip(),
            'aim': input("Aim of the Test: ").strip(),
            'propellant': input("Propellant Type: ").strip(),
            'catalyst': input("Catalyst Type: ").strip()
        }

    @staticmethod
    def collect_system_specs():
        """Collect system specifications"""
        print("\nSystem Specifications")
        print("=" * 22)
        return {
            'chamber number': input("Chamber Number: ").strip(),
            'chamber material': input("Chamber Material: ").strip(),
            'chamber depth': input("Chamber Depth (mm): ").strip(),
            'chamber diameter': input("Chamber Diameter (mm): ").strip(),
            'chamber nozzle_throat': input("Nozzle Throat Dimension (mm): ").strip(),
            'chamber retainer_orifice': input("Retainer Plate Orifice Diameter (mm): ").strip(),
            'mesh material': input("Mesh Material: ").strip(),
            'mesh size': input("Mesh Size: ").strip()
        }

    @staticmethod
    def collect_propellant_specs():
        """Collect propellant specifications"""
        print("\nPropellant Specifications")
        print("=" * 22)
        return {
            'type': input("Type of Propellant: ").strip(),
            'concentration': input("Concentration (Before testing): ").strip(),
            'stability': input("Stability (Old/New - MIL): ").strip(),
            'weight_before': input("Weight Before Test (g): ").strip(),
            'weight_after': input("Weight After Test (g): ").strip()
        }

    @staticmethod
    def collect_catalyst_specs():
        """Collect catalyst specifications"""
        print("\nCatalyst Specifications")
        print("=" * 22)
        return {
            'type': input("Catalyst Type: ").strip(),
            'composition': input("Grade/Composition: ").strip(),
            'size': input("Size of Catalyst: ").strip(),
            'weight_before': input("Weight Before Test (g): ").strip(),
            'weight_after': input("Weight After Test (g): ").strip(),
            'preheat_temp': input("Preheat Temperature (°C): ").strip()
        }

    @staticmethod
    def collect_component_details():
        """Collect component details"""
        print("\nComponent details:")
        print("=" * 22)
        return {
            'pressure_sensor_type': input("Pressure sensor type: ").strip(),
            'pressure_sensor_range': input("Pressure sensor range: ").strip(),
            'pressure_sensor i/o': input("Pressure sensor input & output: ").strip(),
            'heater_type': input("Heater type: ").strip(),
            'heater_input_power': input("Heater input power: ").strip()
        }

    @staticmethod
    def collect_test_details():
        """Collect test details"""
        print("\nTest details:")
        print("=" * 22)
        return {
            'prop_tank_heater_cut-off_temp': input("Propellant tank heater cut-off temperature: ").strip(),
            'prop_tank_heater_reset_temp': input("Propellant tank heater reset temperature: ").strip(),
        }

    @staticmethod
    def collect_heater_info():
        """Collect heater information"""
        print("\nHeater Specifications")
        print("=" * 22)
        return {
            'heater_type': input("Heater type: ").strip(),
            'heater_input': input("Heater input: ").strip(),
            'heater_cut-off_temperature': input("Heater cut-off temperature: ").strip(),
            'heater_reset_temperature': input("Heater reset temperature: ").strip()
        }

    @staticmethod
    def collect_heater_cycles():
        """Collect heater cycle information"""
        print("\nHeater Cycle Information")
        print("=" * 22)

        cycles = []
        for i in range(4):
            print(f"\nCycle {i + 1}:")
            cycle = {
                'switch_on': input("Heater Switch On Time: ").strip(),
                'switch_off': input("Heater Switch Off Time: ").strip(),
                'max_temp': input("Maximum Temperature in System (°C): ").strip(),
                'max_temp_location': input("Location of Maximum Temperature: ").strip(),
                'tank_bottom_temp': input("Corresponding Tank Bottom Temperature (°C): ").strip()
            }
            cycles.append(cycle)
        return cycles

    @staticmethod
    def collect_note():
        """Collect additional notes"""
        print("\nNote to add")
        print("=" * 22)
        return input("Note: ").strip()
    
    @staticmethod
    def collect_post_test_observ():
        """Collect Post test Observations"""
        print("\nPost Testing Observations")
        print("=" * 22)
        return {
            'chamber_no.': input("Chamber no.: ").strip(),
            'chamber_length': input("Chamber Length: ").strip(),
            'chamber_internal_diameter': input("Chamber internal Diameter: ").strip(),
            'chamber_external_diameter': input("Chamber external Diameter: ").strip(),
            'mesh_condition': input("Mesh Condition: ").strip(),
            'retainer_plate_condition': input("Retainer Plate condition: ").strip(),
            'catalyst_photo_before_firing': input("Catalyst photo before firing: ").strip(),
            'catalyst_photo_after_firing': input("Catalyst photo after firing: ").strip()
        }

    @staticmethod
    def collect_catalyst_post_analysis():
        """Collect Catalyst post-analysis information"""
        print("\nCatalyst post-analysis")
        print("=" * 22)

        analysis = {
            'catalyst_details/specification': input("Catalyst details: ").strip(),
            'catalyst_color_before': input("Catalyst color before: ").strip(),
            'catalyst_color_after': input("Catalyst color after: ").strip(),
            'catalyst_weight_filled': input("Catalyst weight filled: ").strip(),
            'catalyst_weight_recovered': input("Catalyst weight Recovered: ").strip(),
            'catalyst_loss_percentage': input("Catalyst loss percentage: ").strip()
        }

        return analysis

    @staticmethod
    def collect_propellant_post_analysis():
        """Collect Catalyst post-analysis information"""
        print("\nPropellant post-analysis")
        print("=" * 22)

        analysis = {
            'propellant_details/specification': input("Propellant details: ").strip(),
            'propellant_color_before': input("Propellant color before: ").strip(),
            'propellant_color_after': input("Propellant color after: ").strip(),
            'propellant_weight_filled': input("Propellant weight filled: ").strip(),
            'propellant_weight_recovered': input("Propellant weight Recovered: ").strip(),
            'propellant_used_percentage': input("Propellant used percentage: ").strip(),
            'propellant_RI_(before_firing)': input("Propellant RI (before firing): ").strip(),
            'propellant_RI_(after_firing)': input("Propellant RI (after firing): ").strip(),
            'firing_duration': input("Firing duration: ").strip(),
            'approximate_mass_flow_rate': input("Approximate mass flow rate: ").strip(),
        }

        return analysis

    @classmethod
    def collect_all_data(cls):
        """Collect all test data at once"""
        return {
            'basic_info': cls.collect_test_info(),
            'system_specs': cls.collect_system_specs(),
            'propellant_specs': cls.collect_propellant_specs(),
            'catalyst_specs': cls.collect_catalyst_specs(),
            'component_details': cls.collect_component_details(),
            'test_details': cls.collect_test_details(),
            'heater_info': cls.collect_heater_info(),
            'heater_cycles': cls.collect_heater_cycles(),
            'note': cls.collect_note(),
            'post_test_observations': cls.collect_post_test_observ(),
            'catalyst_post_analysis': cls.collect_catalyst_post_analysis(),
            'propellant_post_analysis': cls.collect_propellant_post_analysis(),
        }

    @staticmethod
    def format_input_error(field_name: str, value: str, expected_type: str) -> str:
        """Format error message for invalid input"""
        return f"Invalid input for {field_name}: '{value}'. Expected {expected_type}."