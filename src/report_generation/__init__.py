"""
Report Generation Package
-----------------------
Comprehensive package for generating test reports.
"""

from .report_generator import TestReportGenerator
from .data_collector import TestDataCollector
from .image_handler import <PERSON><PERSON>andler
from .base_pdf import DynamicPDF
import os

# Set FPDF font path using the centralized function
from fpdf import FPDF
from .setup_fonts import get_fonts_dir

# Clear any cached font data and set the correct font path
fonts_dir = get_fonts_dir()
FPDF.FONT_PATH = fonts_dir
print(f"Set FPDF.FONT_PATH to: {fonts_dir}")

# Clear any existing font cache to ensure fresh font loading
if hasattr(FPDF, '_fonts'):
    FPDF._fonts = {}
if hasattr(FPDF, 'fonts'):
    FPDF.fonts = {}

__all__ = [
    'TestReportGenerator',
    'TestDataCollector',
    'ImageHandler',
    'DynamicPDF'
]

__version__ = '1.0.0'