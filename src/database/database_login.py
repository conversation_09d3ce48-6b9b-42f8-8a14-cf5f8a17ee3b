from PySide6.QtWidgets import (QDialog, QVBoxLayout, QLabel, QLineEdit, 
                              QPushButton, QMessageBox, QApplication)
from PySide6.QtCore import Qt
import sys

class DatabaseLoginDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.password = None
        self.setup_ui()
        
    def setup_ui(self):
        self.setWindowTitle("Database Login")
        self.setModal(True)
        self.setMinimumWidth(300)
        
        layout = QVBoxLayout(self)
        
        # Info label
        info_label = QLabel("Enter PostgreSQL Database Password")
        info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(info_label)
        
        # Password field
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("Password")
        self.password_edit.setEchoMode(QLineEdit.Password)
        layout.addWidget(self.password_edit)
        
        # Login button
        login_button = QPushButton("Connect")
        login_button.clicked.connect(self.accept_login)
        layout.addWidget(login_button)
        
        self.setLayout(layout)
        
    def accept_login(self):
        if self.password_edit.text().strip():
            self.password = self.password_edit.text()
            self.accept()
        else:
            QMessageBox.warning(self, "Error", "Please enter the password")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    dialog = DatabaseLoginDialog()
    dialog.exec()
    print(dialog.password)