"""database configuration settings."""
import psycopg2
from PySide6.QtWidgets import QMessageBox

# Server configuration
SERVER_CONFIG = {
    "dbname": "vapr_idex_db",
    "user": "postgres",
    "password": None,  # Will be set at runtime
    "host": "localhost",
    "port": "5432"
}

# Client configuration
CLIENT_CONFIG = {
    "dbname": "vapr_idex_db",
    "user": "postgres",
    "password": None,  # Will be set at runtime
    "host": "************",
    "port": "5432"
}

class DatabaseConfig:
    @staticmethod
    def set_database_password(password: str):
        """Set the database password for both configurations."""
        SERVER_CONFIG["password"] = password
        CLIENT_CONFIG["password"] = password

    @staticmethod
    def get_connection_params(is_server=True):
        """Get database connection parameters based on role."""
        try:
            if is_server:
                return SERVER_CONFIG
            return CLIENT_CONFIG
        except Exception as e:
            print(f"Error getting database configuration: {str(e)}")
            raise

    @staticmethod
    def test_connection(params):
        """Test database connection with given parameters."""
        try:
            conn = psycopg2.connect(**params)
            conn.close()
            return True, "Connection successful"
        except Exception as e:
            return False, f"Connection failed: {str(e)}"