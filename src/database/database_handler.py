import json
import os
import shutil
import tempfile
import traceback

import psycopg2
from psycopg2.extras import <PERSON><PERSON>
from typing import Dict, Optional, List
import pandas as pd

from src.database.database_config import DatabaseConfig


class DatabaseHandler:
    """Handles all database operations for the VAPR-iDEX test data."""

    def __init__(self, is_server=True):
        """Initialize database connection."""
        self.connection_params = DatabaseConfig.get_connection_params(is_server)

        if is_server:
            self.initialize_database()

    def initialize_database(self):
        """Create database and tables if they don't exist."""
        try:
            # First try to connect to postgres database to create our database
            temp_params = self.connection_params.copy()
            temp_params['dbname'] = 'postgres'  # Connect to default postgres database
            conn = psycopg2.connect(**temp_params)
            conn.autocommit = True  # Required for database creation
            cur = conn.cursor()

            # Check if our database exists
            cur.execute("SELECT 1 FROM pg_catalog.pg_database WHERE datname = %s",
                        (self.connection_params['dbname'],))
            exists = cur.fetchone()

            if not exists:
                # Create the database
                cur.execute(f"CREATE DATABASE {self.connection_params['dbname']}")

            # Close connection to postgres database
            cur.close()
            conn.close()

            # Now connect to our database
            conn = psycopg2.connect(**self.connection_params)
            cur = conn.cursor()

            # Creating main test data table to store all the metadata entered by the user and generated in the application
            cur.execute("""
                CREATE TABLE IF NOT EXISTS test_data (
                    test_id SERIAL PRIMARY KEY,
                    test_no INTEGER NOT NULL UNIQUE,
                    test_date DATE,
                    basic_info JSONB,
                    system_specs JSONB,
                    propellant_specs JSONB,
                    catalyst_specs JSONB,
                    component_details JSONB,
                    test_details JSONB,
                    heater_info JSONB,
                    heater_cycles JSONB,
                    note TEXT,
                    post_test_observations JSONB,
                    catalyst_post_analysis JSONB,
                    propellant_post_analysis JSONB,
                    performance_data JSONB,
                    ri_table JSONB,
                    test_authorization JSONB,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Creating temperature data table
            cur.execute("""
                CREATE TABLE IF NOT EXISTS temperature_data (
                    id SERIAL PRIMARY KEY,
                    test_id INTEGER REFERENCES test_data(test_id),
                    time_data FLOAT[],
                    temperature_data JSONB,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Creating pressure data table
            cur.execute("""
                CREATE TABLE IF NOT EXISTS pressure_data (
                    id SERIAL PRIMARY KEY,
                    test_id INTEGER REFERENCES test_data(test_id),
                    time_data FLOAT[],
                    pressure_data JSONB,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Creating Plots table to store plot data
            cur.execute("""
                CREATE TABLE IF NOT EXISTS test_plots (
                    id SERIAL PRIMARY KEY,
                    test_id INTEGER REFERENCES test_data(test_id),
                    plot_type VARCHAR(50),      -- 'temperature' or 'pressure'
                    plot_title VARCHAR(255),
                    plot_data BYTEA,            -- Binary data for the plot
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Creating Photos table to store photo data
            cur.execute("""
                CREATE TABLE IF NOT EXISTS test_photos (
                    id SERIAL PRIMARY KEY,
                    test_id INTEGER REFERENCES test_data(test_id),
                    photo_type VARCHAR(50),      -- 'prop_before', 'prop_after', 'cat_before', 'cat_after'
                    photo_data BYTEA,            -- Binary data for the photo
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Creating PDF Reports table to store the Report as a PDF
            cur.execute("""
                CREATE TABLE IF NOT EXISTS test_reports (
                    id SERIAL PRIMARY KEY,
                    test_id INTEGER REFERENCES test_data(test_id),
                    report_data BYTEA,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Creating Temperature Analysis table to store analysis results
            cur.execute("""
                CREATE TABLE IF NOT EXISTS temperature_analysis (
                    id SERIAL PRIMARY KEY,
                    test_id INTEGER UNIQUE REFERENCES test_data(test_id),
                    analysis_data JSONB,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """)

            # Add new columns if they don't exist
            cur.execute("""
                        DO $$
                        BEGIN
                            IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                                         WHERE table_name='test_data' AND column_name='ri_table') THEN
                                ALTER TABLE test_data ADD COLUMN ri_table JSONB;
                            END IF;

                            IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                                         WHERE table_name='test_data' AND column_name='test_authorization') THEN
                                ALTER TABLE test_data ADD COLUMN test_authorization JSONB;
                            END IF;

                            IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                                        WHERE table_name='test_data' AND column_name='pressure_relations') THEN
                                ALTER TABLE test_data ADD COLUMN pressure_relations JSONB;
                            END IF;
                        END $$;
                    """)

            conn.commit()

        except Exception as e:
            print(f"Error initializing database: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()

    def save_test_data(self, test_data: Dict, plot_paths: Dict = None, photo_paths: Dict = None) -> Optional[int]:
        """Save test data and plot images to database."""
        conn = None
        try:
            conn = psycopg2.connect(**self.connection_params)
            cur = conn.cursor()
            cur.execute("SET datestyle = 'DMY';")

            # Move firing duration into basic_info
            basic_info = test_data.get('basic_info', {})
            firing_duration = test_data.get('firing_duration')
            if firing_duration is not None:
                if isinstance(firing_duration, str) and 'Firing Duration:' in firing_duration:
                    # Extract the numeric value from the string
                    firing_duration = float(firing_duration.split(':')[1].strip().replace('s', ''))
                basic_info['firing_duration'] = firing_duration

            # Check if test_no already exists
            test_no = test_data.get('test_no')
            cur.execute("SELECT test_id FROM test_data WHERE test_no = %s", (test_no,))
            existing = cur.fetchone()

            if existing:
                # Update existing record
                test_id = existing[0]
                query = """
                    UPDATE test_data SET
                        test_date = %s,
                        basic_info = %s,
                        system_specs = %s,
                        propellant_specs = %s,
                        catalyst_specs = %s,
                        component_details = %s,
                        test_details = %s,
                        heater_info = %s,
                        heater_cycles = %s,
                        note = %s,
                        post_test_observations = %s,
                        catalyst_post_analysis = %s,
                        propellant_post_analysis = %s,
                        performance_data = %s,
                        ri_table = %s,
                        test_authorization = %s,
                        pressure_relations = %s
                    WHERE test_id = %s
                """
                values = (
                    test_data.get('test_date'),
                    Json(test_data.get('basic_info', {})),
                    Json(test_data.get('system_specs', {})),
                    Json(test_data.get('propellant_specs', {})),
                    Json(test_data.get('catalyst_specs', {})),
                    Json(test_data.get('component_details', {})),
                    Json(test_data.get('test_details', {})),
                    Json(test_data.get('heater_info', {})),
                    Json(test_data.get('heater_cycles', [])),
                    test_data.get('note', ''),
                    Json(test_data.get('post_test_observations', {})),
                    Json(test_data.get('catalyst_post_analysis', {})),
                    Json(test_data.get('propellant_post_analysis', {})),
                    Json(test_data.get('system_performance', {})),
                    Json(test_data.get('ri_table', {})),
                    Json(test_data.get('test_authorization', {})),
                    Json(test_data.get('pressure_relations')),
                    test_id
                )
                cur.execute(query, values)
            else:
                # Insert new record
                query = """
                    INSERT INTO test_data (
                        test_no, test_date, basic_info, system_specs,
                        propellant_specs, catalyst_specs, component_details,
                        test_details, heater_info, heater_cycles, note,
                        post_test_observations, catalyst_post_analysis,
                        propellant_post_analysis, performance_data,
                        ri_table, test_authorization, pressure_relations
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING test_id
                """
                values = (
                    test_no,
                    test_data.get('test_date'),
                    Json(test_data.get('basic_info', {})),
                    Json(test_data.get('system_specs', {})),
                    Json(test_data.get('propellant_specs', {})),
                    Json(test_data.get('catalyst_specs', {})),
                    Json(test_data.get('component_details', {})),
                    Json(test_data.get('test_details', {})),
                    Json(test_data.get('heater_info', {})),
                    Json(test_data.get('heater_cycles', [])),
                    test_data.get('note', ''),
                    Json(test_data.get('post_test_observations', {})),
                    Json(test_data.get('catalyst_post_analysis', {})),
                    Json(test_data.get('propellant_post_analysis', {})),
                    Json(test_data.get('system_performance', {})),
                    Json(test_data.get('ri_table', {})),
                    Json(test_data.get('test_authorization', {})),
                    Json(test_data.get('pressure_relations', {}))
                )
                cur.execute(query, values)
                test_id = cur.fetchone()[0]

            # After successful test data save, save plots if provided
            if plot_paths and test_id:
                # Delete existing plots for this test
                cur.execute("DELETE FROM test_plots WHERE test_id = %s", (test_id,))

                # Save new plots
                for plot_type, plot_dir in plot_paths.items():
                    if os.path.exists(plot_dir):
                        for plot_file in os.listdir(plot_dir):
                            if plot_file.endswith(('.png', '.jpg', '.jpeg')):
                                plot_path = os.path.join(plot_dir, plot_file)
                                with open(plot_path, 'rb') as f:
                                    plot_data = f.read()
                                    plot_title = os.path.splitext(plot_file)[0]
                                    cur.execute("""
                                        INSERT INTO test_plots (test_id, plot_type, plot_title, plot_data)
                                        VALUES (%s, %s, %s, %s)
                                    """, (test_id, plot_type, plot_title, psycopg2.Binary(plot_data)))

            # Save photos if provided
            if photo_paths and test_id:
                try:
                    # Delete existing photos for this test
                    cur.execute("DELETE FROM test_photos WHERE test_id = %s", (test_id,))

                    # Save new photos
                    for photo_type, photo_info in photo_paths.items():
                        photo_path = photo_info.get('path') if isinstance(photo_info, dict) else photo_info
                        if photo_path and os.path.exists(photo_path):
                            with open(photo_path, 'rb') as f:
                                photo_data = f.read()
                                cur.execute("""
                                    INSERT INTO test_photos (test_id, photo_type, photo_data)
                                    VALUES (%s, %s, %s)
                                """, (test_id, photo_type, psycopg2.Binary(photo_data)))
                    print(f"Successfully saved photos for test_id: {test_id}")
                except Exception as e:
                    print(f"Error saving photos: {str(e)}")
                    traceback.print_exc()

            # Inside save_test_data, after photo saving block
            if 'temperature_analysis' in test_data and test_id:
                self.save_temperature_analysis(test_id, test_data['temperature_analysis'])

            # Ensure performance_data exists
            if 'performance_data' not in test_data:
                test_data['performance_data'] = {}

            # Save filtered temperature data if it exists
            if 'filtered_temp_data' in test_data:
                filtered_data = test_data['filtered_temp_data']
                print(f"Saving filtered_temp_data: {type(filtered_data)}")

                # Make sure it's a dictionary
                if isinstance(filtered_data, dict):
                    print(f"filtered_temp_data keys: {list(filtered_data.keys())}")

                    # Make sure we have both formats for maximum compatibility
                    if 'selected_columns' in filtered_data and 'selected_ranges' in filtered_data:
                        # If we don't have the direct data format, add it
                        if 'time_data' not in filtered_data or 'temperature_data' not in filtered_data:
                            print("Adding direct data format")
                            # We need to reconstruct the filtered data
                            if 'temperature_data' in test_data:
                                # Use the temperature data from test_data
                                temp_data = pd.DataFrame({
                                    'time': test_data['temperature_data']['time']
                                })
                                for col, values in test_data['temperature_data']['temperatures'].items():
                                    temp_data[col] = values

                                # Apply the selection criteria
                                mask = pd.Series(False, index=temp_data.index)
                                for start, end in filtered_data['selected_ranges']:
                                    mask |= ((temp_data['time'] >= start) &
                                             (temp_data['time'] <= end))

                                # Filter data
                                filtered_df = temp_data[mask]

                                # Select only chosen columns plus time
                                columns_to_use = ['time'] + filtered_data['selected_columns']
                                filtered_df = filtered_df[columns_to_use]

                                # Add the direct data format
                                filtered_data['time_data'] = filtered_df['time'].tolist()
                                filtered_data['temperature_data'] = {
                                    col: filtered_df[col].tolist()
                                    for col in filtered_df.columns if col != 'time'
                                }

                # Make sure we're using the correct key name consistently
                test_data['performance_data']['filtered_temp_data'] = filtered_data
                del test_data['filtered_temp_data']  # Remove from top level after moving to performance_data
                print("Saved filtered_temp_data to performance_data")

            # For backward compatibility, also check for filtered_temperature_data
            if 'filtered_temperature_data' in test_data:
                filtered_data = test_data['filtered_temperature_data']
                print(f"Saving filtered_temperature_data: {type(filtered_data)}")

                # Make sure we're using the correct key name consistently
                test_data['performance_data']['filtered_temp_data'] = filtered_data
                del test_data['filtered_temperature_data']
                print("Saved filtered_temperature_data to performance_data as filtered_temp_data")

            conn.commit()
            return test_id

        except Exception as e:
            print(f"Error saving test data: {str(e)}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                conn.close()

    def save_temperature_data(self, test_id: int, temp_data: pd.DataFrame) -> bool:
        """Save temperature data to database."""
        try:
            conn = psycopg2.connect(**self.connection_params)
            cur = conn.cursor()

            # Convert DataFrame to appropriate format
            time_data = temp_data['time'].tolist()
            temp_cols = [col for col in temp_data.columns if col != 'time']
            temperature_data = {col: temp_data[col].tolist() for col in temp_cols}

            query = """
                INSERT INTO temperature_data (test_id, time_data, temperature_data)
                VALUES (%s, %s, %s)
            """

            cur.execute(query, (test_id, time_data, Json(temperature_data)))
            conn.commit()
            return True

        except Exception as e:
            print(f"Error saving temperature data: {str(e)}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                conn.close()

    def save_pressure_data(self, test_id: int, pressure_data: pd.DataFrame) -> bool:
        """Save pressure data to database."""
        try:
            conn = psycopg2.connect(**self.connection_params)
            cur = conn.cursor()

            # Convert DataFrame to appropriate format
            time_col = next((col for col in pressure_data.columns if 'time' in col.lower()), None)
            if time_col:
                time_data = pressure_data[time_col].tolist()
                pressure_cols = [col for col in pressure_data.columns if col != time_col]
                pressure_dict = {col: pressure_data[col].tolist() for col in pressure_cols}

                query = """
                    INSERT INTO pressure_data (test_id, time_data, pressure_data)
                    VALUES (%s, %s, %s)
                """

                cur.execute(query, (test_id, time_data, Json(pressure_dict)))
                conn.commit()
                return True
            return False

        except Exception as e:
            print(f"Error saving pressure data: {str(e)}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                conn.close()

    def save_plot(self, test_id: int, plot_type: str, plot_title: str, plot_path: str) -> bool:
        """Save plot to the database"""
        try:
            conn = psycopg2.connect(**self.connection_params)
            cur = conn.cursor()

            # Reading plot file as binary data
            with open(plot_path, 'rb') as file:
                plot_data = file.read()

            query = """
                INSERT INTO test_plots (test_id, plot_type, plot_title, plot_data)
                VALUES (%s, %s, %s, %s)
            """

            cur.execute(query, (test_id, plot_type, plot_title, psycopg2.Binary(plot_data)))
            conn.commit()
            return True

        except Exception as e:
            print(f'Error saving plot in the Database: {str(e)}')
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                conn.close()

    def save_temperature_analysis(self, test_id: int, analysis_results: pd.DataFrame) -> bool:
        """Save temperature analysis results to the database."""
        try:
            conn = psycopg2.connect(**self.connection_params)
            cur = conn.cursor()

            # Convert DataFrame to JSON-serializable format
            analysis_data = analysis_results.to_dict(orient='index')

            query = """
                INSERT INTO temperature_analysis (test_id, analysis_data)
                VALUES (%s, %s)
                ON CONFLICT (test_id) DO UPDATE
                SET analysis_data = EXCLUDED.analysis_data,
                    created_at = CURRENT_TIMESTAMP
            """

            cur.execute(query, (test_id, Json(analysis_data)))
            conn.commit()
            return True

        except Exception as e:
            print(f"Error saving temperature analysis: {str(e)}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                conn.close()

    def save_report(self, test_id: int, pdf_path: str) -> bool:
        try:
            with open(pdf_path, 'rb') as file:
                pdf_data = file.read()

            conn = psycopg2.connect(**self.connection_params)
            cur = conn.cursor()

            cur.execute("""
                INSERT INTO test_reports (test_id, report_data)
                VALUES (%s, %s)
            """, (test_id, psycopg2.Binary(pdf_data)))

            conn.commit()
            return True
        except Exception as e:
            print(f"Error saving report: {str(e)}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                conn.close()

    def get_test_data(self, test_no: str) -> Optional[Dict]:
        """Retrieve test data by test number."""
        try:
            conn = psycopg2.connect(**self.connection_params)
            cur = conn.cursor()

            query = "SELECT * FROM test_data WHERE test_no = %s"
            cur.execute(query, (test_no,))
            result = cur.fetchone()

            if result:
                columns = [desc[0] for desc in cur.description]
                test_data = dict(zip(columns, result))

                # Process pressure_relations if it exists
                if 'pressure_relations' in test_data and test_data['pressure_relations'] is not None:
                    # If it's already a dict, keep it as is
                    if isinstance(test_data['pressure_relations'], dict):
                        pass
                    # If it's a string, parse it to JSON
                    elif isinstance(test_data['pressure_relations'], str):
                        try:
                            test_data['pressure_relations'] = json.loads(test_data['pressure_relations'])
                        except json.JSONDecodeError:
                            test_data['pressure_relations'] = {}
                else:
                    # Initialize with empty dict if not present
                    test_data['pressure_relations'] = {}

                return test_data
            return None

        except Exception as e:
            print(f"Error retrieving test data: {str(e)}")
            return None
        finally:
            if conn:
                conn.close()

    def get_temperature_data(self, test_id: int) -> Optional[pd.DataFrame]:
        """Retrieve temperature data for a test."""
        try:
            conn = psycopg2.connect(**self.connection_params)
            cur = conn.cursor()

            query = "SELECT time_data, temperature_data FROM temperature_data WHERE test_id = %s"
            cur.execute(query, (test_id,))
            result = cur.fetchone()

            if result:
                time_data, temp_data = result
                df = pd.DataFrame({'time': time_data})
                for col, values in temp_data.items():
                    df[col] = values
                return df
            return None

        except Exception as e:
            print(f"Error retrieving temperature data: {str(e)}")
            return None
        finally:
            if conn:
                conn.close()

    def get_pressure_data(self, test_id: int) -> Optional[pd.DataFrame]:
        """Retrieve pressure data for a test."""
        try:
            conn = psycopg2.connect(**self.connection_params)
            cur = conn.cursor()

            query = "SELECT time_data, pressure_data FROM pressure_data WHERE test_id = %s"
            cur.execute(query, (test_id,))
            result = cur.fetchone()

            if result:
                time_data, pressure_data = result
                df = pd.DataFrame({'time': time_data})
                for col, values in pressure_data.items():
                    df[col] = values
                return df
            return None

        except Exception as e:
            print(f"Error retrieving pressure data: {str(e)}")
            return None
        finally:
            if conn:
                conn.close()

    def get_all_test_numbers(self) -> list:
        """Get all test numbers from database."""
        try:
            conn = psycopg2.connect(**self.connection_params)
            cur = conn.cursor()

            cur.execute("SELECT test_no FROM test_data ORDER BY test_no")
            results = cur.fetchall()
            return [r[0] for r in results]

        except Exception as e:
            print(f"Error retrieving test numbers: {str(e)}")
            return []
        finally:
            if conn:
                conn.close()

    def get_test_plots(self, test_id: int, preview_callback=None) -> List[Dict]:
        """
        Retrieve plots for a test and save them to temporary files.

        Args:
            test_id: The ID of the test
            preview_callback: Optional callback function to update plot preview in GUI
        """
        try:
            conn = psycopg2.connect(**self.connection_params)
            cur = conn.cursor()

            query = """
                SELECT plot_type, plot_title, plot_data
                FROM test_plots
                WHERE test_id = %s
                ORDER BY created_at
            """
            cur.execute(query, (test_id,))
            results = cur.fetchall()

            plots = []
            # Create temporary directory for plots if needed
            self.temp_dir = os.path.join(os.getcwd(), 'temp_plots')
            os.makedirs(self.temp_dir, exist_ok=True)

            for plot_type, plot_title, plot_data in results:
                # Create a temporary file for the plot
                temp_filename = f"{plot_title}.png"
                temp_path = os.path.join(self.temp_dir, temp_filename)

                # Write binary data to file
                with open(temp_path, 'wb') as f:
                    f.write(plot_data)

                # Create a standardized plot info dictionary
                plot_info = {
                    'type': plot_type,
                    'title': plot_title,
                    'path': temp_path
                }
                plots.append(plot_info)

                # If preview callback is provided, update the preview
                if preview_callback:
                    try:
                        preview_callback(plot_info)
                    except Exception as e:
                        print(f"Error updating preview for plot {plot_title}: {str(e)}")



            return plots

        except Exception as e:
            print(f"Error retrieving plots from database: {str(e)}")
            traceback.print_exc()
            return []
        finally:
            if conn:
                conn.close()

    def get_test_photos(self, test_id: int) -> Dict[str, str]:
        """Retrieve photos for a test and save them to temporary files."""
        try:
            conn = psycopg2.connect(**self.connection_params)
            cur = conn.cursor()

            query = """
                SELECT photo_type, photo_data
                FROM test_photos
                WHERE test_id = %s
            """
            cur.execute(query, (test_id,))
            results = cur.fetchall()

            photos = {}
            # Create temporary directory for photos if needed
            temp_dir = os.path.join(os.getcwd(), 'temp_photos')
            os.makedirs(temp_dir, exist_ok=True)

            for photo_type, photo_data in results:
                if photo_data:
                    # Create a temporary file for the photo
                    temp_filename = f"{photo_type}_{test_id}.png"
                    temp_path = os.path.join(temp_dir, temp_filename)

                    # Write binary data to file
                    with open(temp_path, 'wb') as f:
                        f.write(photo_data)

                    photos[photo_type] = temp_path
                    print(f"Successfully retrieved photo {photo_type} for test_id: {test_id}")

            return photos

        except Exception as e:
            print(f"Error retrieving photos from database: {str(e)}")
            traceback.print_exc()
            return {}
        finally:
            if conn:
                conn.close()

    def get_test_report(self, test_id: int) -> Optional[str]:
        try:
            conn = psycopg2.connect(**self.connection_params)
            cur = conn.cursor()

            cur.execute("SELECT report_data FROM test_reports WHERE test_id = %s ORDER BY created_at DESC LIMIT 1",
                        (test_id,))
            result = cur.fetchone()

            if result:
                temp_dir = tempfile.mkdtemp()
                pdf_path = os.path.join(temp_dir, f"test_{test_id}_report.pdf")

                with open(pdf_path, 'wb') as file:
                    file.write(result[0])

                return pdf_path
            return None
        finally:
            if conn:
                conn.close()

    def get_temperature_analysis(self, test_id: int) -> Optional[pd.DataFrame]:
        """Retrieve temperature analysis data for a test."""
        try:
            conn = psycopg2.connect(**self.connection_params)
            cur = conn.cursor()

            query = "SELECT analysis_data FROM temperature_analysis WHERE test_id = %s"
            cur.execute(query, (test_id,))
            result = cur.fetchone()

            if result:
                analysis_data = result[0]
                # Convert JSONB back to DataFrame
                df = pd.DataFrame.from_dict(analysis_data, orient='index')
                return df
            return None

        except Exception as e:
            print(f"Error retrieving temperature analysis: {str(e)}")
            return None
        finally:
            if conn:
                conn.close()

    def get_filtered_temp_data_from_performance(self, test_id: int) -> Optional[pd.DataFrame]:
        """Extract filtered temperature data from system_performance."""
        conn = None
        try:
            # First, get the full temperature data as a fallback
            temp_data = self.get_temperature_data(test_id)
            if temp_data is None:
                print("No temperature data found for this test")
                return None

            # Now try to get the filtered data
            conn = psycopg2.connect(**self.connection_params)
            cur = conn.cursor()

            # Get the entire test data record to examine
            query = "SELECT performance_data FROM test_data WHERE test_id = %s"
            cur.execute(query, (test_id,))
            result = cur.fetchone()

            if result and result[0]:
                perf_data = result[0]

                # Try to get filtered data with either key name
                filtered_data = None
                if 'filtered_temp_data' in perf_data:
                    filtered_data = perf_data['filtered_temp_data']
                elif 'filtered_temperature_data' in perf_data:
                    filtered_data = perf_data['filtered_temperature_data']

                if filtered_data:
                    # Case 1: If filtered_data has time_data and temperature_data, use it directly
                    if isinstance(filtered_data, dict) and 'time_data' in filtered_data and 'temperature_data' in filtered_data:
                        try:
                            df = pd.DataFrame({'time': filtered_data['time_data']})
                            for col, values in filtered_data['temperature_data'].items():
                                df[col] = values
                            return df
                        except Exception as e:
                            print(f"Error creating DataFrame from direct data: {str(e)}")

                    # Case 2: If filtered_data has selected_columns and selected_ranges, reconstruct it
                    elif isinstance(filtered_data, dict) and 'selected_columns' in filtered_data and 'selected_ranges' in filtered_data:
                        try:
                            selected_columns = filtered_data['selected_columns']
                            selected_ranges = filtered_data['selected_ranges']

                            # Create mask for selected ranges
                            mask = pd.Series(False, index=temp_data.index)
                            for start, end in selected_ranges:
                                mask |= ((temp_data['time'] >= start) & (temp_data['time'] <= end))

                            # Filter data
                            filtered_df = temp_data[mask]

                            # Select only chosen columns plus time
                            columns_to_use = ['time'] + selected_columns
                            available_columns = [col for col in columns_to_use if col in filtered_df.columns]
                            filtered_df = filtered_df[available_columns]

                            return filtered_df
                        except Exception as e:
                            print(f"Error reconstructing from selection criteria: {str(e)}")

            # If we get here, we couldn't find or process the filtered temperature data
            # Return the full temperature data as a fallback
            print("Returning full temperature data as fallback")
            return temp_data

        except Exception as e:
            print(f"Error retrieving filtered temperature data: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
        finally:
            if conn:
                conn.close()

    def compare_tests(self, test_no1: str, test_no2: str) -> Dict:
        """Compare two tests and return differences."""
        try:
            # Get test data
            test1 = self.get_test_data(test_no1)
            test2 = self.get_test_data(test_no2)

            if not test1 or not test2:
                print(f"Could not retrieve test data for one or both tests")
                return {}

            differences = {}

            # Compare basic data fields
            for key in test1.keys():
                if key not in ['test_id', 'created_at', 'test_no', 'test_date']:
                    value1 = test1[key]
                    value2 = test2[key]

                    if isinstance(value1, dict) and isinstance(value2, dict):
                        # Compare dictionaries
                        diff = {}
                        all_keys = set(value1.keys()) | set(value2.keys())
                        for k in all_keys:
                            v1 = value1.get(k)
                            v2 = value2.get(k)
                            if v1 != v2:
                                diff[k] = {'test1': v1, 'test2': v2}
                        if diff:
                            differences[key] = diff

                    elif isinstance(value1, list) and isinstance(value2, list):
                        # Compare lists
                        if value1 != value2:
                            differences[key] = {
                                'test1': value1,
                                'test2': value2
                            }

                    elif value1 != value2:
                        # Compare other types
                        differences[key] = {
                            'test1': value1,
                            'test2': value2
                        }

            # Compare plots
            try:
                test1_plots = self.get_test_plots(test1['test_id'])
                test2_plots = self.get_test_plots(test2['test_id'])

                # Create simple plot summaries without using .get()
                plots_summary1 = []
                plots_summary2 = []

                for plot in test1_plots:
                    if isinstance(plot, dict) and 'title' in plot and 'type' in plot:
                        plots_summary1.append({
                            'title': plot['title'],
                            'type': plot['type']
                        })

                for plot in test2_plots:
                    if isinstance(plot, dict) and 'title' in plot and 'type' in plot:
                        plots_summary2.append({
                            'title': plot['title'],
                            'type': plot['type']
                        })

                # Only add to differences if the summaries are different
                if plots_summary1 != plots_summary2:
                    differences['plots'] = {
                        'test1': plots_summary1,
                        'test2': plots_summary2
                    }

            except Exception as e:
                print(f"Error comparing plots: {str(e)}")
                traceback.print_exc()

            return differences

        except Exception as e:
            print(f"Error comparing tests: {str(e)}")
            traceback.print_exc()
            return {}

    def filter_tests(self, params):
        try:
            conn = psycopg2.connect(**self.connection_params)
            cur = conn.cursor()

            query = """
            SELECT test_id, test_no, test_date, basic_info, propellant_specs
            FROM test_data
            """
            conditions = []

            if params.get('propellantConc'):
                value = float(params.get('propellantConc', 0))
                conditions.append(
                    f"CAST(propellant_specs->>'Concentration before testing (%)' AS float) BETWEEN {value - 1} AND {value + 1}")

            if params.get('testNo'):
                conditions.append(f"test_no = {int(params['testNo'])}")

            if params.get('catalystName'):
                catalyst_name = params['catalystName'].replace("'", "''")  # Escape single quotes
                conditions.append(f"basic_info->>'Catalyst' ILIKE '%{catalyst_name}%'")

            if params.get('tankTemp'):
                temp_value = float(params.get('tankTemp', 0))
                conditions.append(f"""
                    EXISTS (
                        SELECT 1 FROM jsonb_array_elements(heater_cycles) AS hc
                        WHERE CAST(hc->>'tank_bottom_temp' AS float) BETWEEN {temp_value - 1} AND {temp_value + 1}
                    )
                """)

            if conditions:
                query += " WHERE " + " AND ".join(conditions)
            query += " ORDER BY test_no"

            cur.execute(query)
            results = cur.fetchall()
            formatted_results = []

            for row in results:
                test_id, test_no, test_date, basic_info, propellant_specs = row
                try:
                    propellant_conc = float(propellant_specs.get('Concentration before testing (%)', 0))
                except (ValueError, TypeError):
                    propellant_conc = 0

                formatted_results.append({
                    'test_id': test_id,
                    'test_no': test_no,
                    'test_date': test_date.strftime('%Y-%m-%d') if test_date else '',
                    'catalyst_name': basic_info.get('Catalyst', ''),
                    'propellant_conc': propellant_conc
                })

            return formatted_results

        except Exception as e:
            print(f"Error in filter_tests: {str(e)}")
            print("Traceback:", traceback.format_exc())
            return []
        finally:
            if conn:
                conn.close()


    def delete_temp_plots_folder(self):
        # Cleanup temp directory
        try:
            shutil.rmtree(self.temp_dir, ignore_errors=True)
        except Exception as e:
            print(f"Error cleaning up temp directory: {str(e)}")

    def load_test_data(self, test_id: int) -> Optional[Dict]:
        """Load test data and associated photos from database."""
        try:
            conn = psycopg2.connect(**self.connection_params)
            cur = conn.cursor()

            # Get test data
            query = """
                SELECT * FROM test_data WHERE test_id = %s
            """
            cur.execute(query, (test_id,))
            result = cur.fetchone()

            if result:
                # Get column names
                column_names = [desc[0] for desc in cur.description]
                test_data = dict(zip(column_names, result))

                # Extract performance data and pressure ranges
                performance_data = test_data.get('performance_data', {})
                if isinstance(performance_data, str):
                    performance_data = json.loads(performance_data)

                # Move pressure ranges to system_performance
                system_performance = {
                    'vacuum_pressure_lower_limit': performance_data.get('vacuum_pressure_lower_limit'),
                    'vacuum_pressure_upper_limit': performance_data.get('vacuum_pressure_upper_limit'),
                    'chamber_pressure_lower_limit': performance_data.get('chamber_pressure_lower_limit'),
                    'chamber_pressure_upper_limit': performance_data.get('chamber_pressure_upper_limit')
                }

                test_data['system_performance'] = system_performance

                if test_data.get('pressure_relations'):
                    # If it's a string, parse it to JSON
                    if isinstance(test_data['pressure_relations'], str):
                        test_data['pressure_relations'] = json.loads(test_data['pressure_relations'])
                else:
                    # Initialize with empty dict if not present
                    test_data['pressure_relations'] = {}

                # Get photos
                photos = self.get_test_photos(test_id)
                if photos:
                    test_data['photos'] = photos

                # Get temperature analysis
                temp_analysis = self.get_temperature_analysis(test_id)
                if temp_analysis is not None:
                    test_data['temperature_analysis'] = temp_analysis

                return test_data
            return None

        except Exception as e:
            print(f"Error loading test data: {str(e)}")
            return None
        finally:
            if conn:
                conn.close()