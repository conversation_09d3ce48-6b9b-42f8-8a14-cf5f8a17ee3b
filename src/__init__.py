"""
Thruster Analysis Package
------------------------
A comprehensive package for analyzing thruster test data, including temperature
and pressure measurements.

Components:
- Data handling (loading and processing)
- Analysis (temperature and pressure)
- Visualization (plotting and reporting)
- Utilities (helper functions)

Example Usage:
    from src.data import DataLoader
    from src.analysis import TemperatureAnalyzer, PressureAnalyzer

    # Initialize components
    loader = DataLoader()
    temp_analyzer = TemperatureAnalyzer()

    # Load and analyze data
    data = loader.load_temperature_data("temp.csv")
    temp_analyzer.analyze_temperature_data(data)
"""

from .data import DataLoader
from .analysis import TemperatureAnalyzer, PressureAnalyzer
from .visualization import PlotManager
from .utils import clean_file_path


__all__ = [
    'DataLoader',
    'TemperatureAnalyzer',
    'PressureAnalyzer',
    'PlotManager',
    'clean_file_path'
]

__version__ = '1.0.0'
__author__ = '<PERSON><PERSON><PERSON>'
