import os
from typing import Dict, Optional
from auth import UserAuth


class AuthTester:
    def __init__(self):
        self.auth = UserAuth()
        self.test_users = {
            "employee": {
                "email": "<EMAIL>",
                "password": "initial_password123",
                "role": "user"
            },
            "manager": {
                "email": "<EMAIL>",
                "password": "manager_password123",
                "role": "manager"
            }
        }
        self.verification_tokens = {}
        self.reset_tokens = {}

    def run_all_tests(self):
        """Run all authentication tests in sequence"""
        try:
            print("\n=== Starting Authentication System Tests ===\n")

            # Test 1: Email Configuration
            self._run_test("Email Configuration Test", self.test_email_config)

            # Test 2: User Creation
            self._run_test("User Creation Tests", self.test_user_creation)

            # Test 3: Email Verification
            self._run_test("Email Verification Tests", self.test_email_verification)

            # Test 4: Login
            self._run_test("Login Tests", self.test_login)

            # Test 5: Password Reset
            self._run_test("Password Reset Tests", self.test_password_reset)

            # Test 6: Admin Functions
            self._run_test("Admin Functions Tests", self.test_admin_functions)

            print("\n=== All Tests Completed ===")

        except Exception as e:
            print(f"\n❌ Test suite failed with error: {str(e)}")
            raise

    def _run_test(self, test_name: str, test_func) -> None:
        """Run a single test with proper formatting"""
        print(f"\n--- {test_name} ---")
        try:
            test_func()
            print(f"✅ {test_name} passed")
        except AssertionError as e:
            print(f"❌ {test_name} failed: {str(e)}")
            raise
        except Exception as e:
            print(f"❌ {test_name} failed with unexpected error: {str(e)}")
            raise

    def test_email_config(self):
        """Test email configuration"""
        print("Testing email configuration...")
        assert UserAuth.test_email_configuration(), "Email configuration test failed"

        # Send test email
        test_email = self.test_users["employee"]["email"]
        success = self.auth._send_email(
            test_email,
            "Test Email",
            "<html><body><h1>Test Email</h1><p>This is a test email.</p></body></html>"
        )
        assert success, "Failed to send test email"

    def test_user_creation(self):
        """Test user creation process"""
        print("Testing user creation...")

        # Test creating users with different roles
        for user_type, user_data in self.test_users.items():
            success, message = self.auth.add_user(
                user_data["email"],
                user_data["password"],
                user_data["role"]
            )
            assert success, f"Failed to create {user_type}: {message}"
            print(f"Created {user_type} user successfully")

            # Store verification token from the response
            token = self._extract_token_from_message(message)
            if token:
                self.verification_tokens[user_type] = token

        # Test invalid email domain
        success, message = self.auth.add_user(
            "<EMAIL>",
            "password123",
            "user"
        )
        assert not success, "Should not allow invalid email domain"

    def test_email_verification(self):
        """Test email verification process"""
        print("Testing email verification...")

        for user_type, token in self.verification_tokens.items():
            # Test verification with valid token
            success, message = self.auth.verify_email(token)
            assert success, f"Failed to verify {user_type}: {message}"
            print(f"Verified {user_type} successfully")

        # Test invalid token
        success, message = self.auth.verify_email("invalid_token")
        assert not success, "Should not verify invalid token"

    def test_login(self):
        """Test login functionality"""
        print("Testing login functionality...")

        # Test successful login
        for user_type, user_data in self.test_users.items():
            username = user_data["email"].split("@")[0]
            user = self.auth.authenticate(username, user_data["password"])
            assert user is not None, f"Failed to login as {user_type}"
            assert user.get("role") == user_data["role"], f"Incorrect role for {user_type}"
            print(f"Login successful for {user_type}")

        # Test invalid credentials
        user = self.auth.authenticate("invalid_user", "invalid_password")
        assert user is None, "Should not authenticate with invalid credentials"

    def test_password_reset(self):
        """Test password reset functionality"""
        print("Testing password reset...")

        for user_type, user_data in self.test_users.items():
            # Initiate password reset
            success, message = self.auth.initiate_password_reset(user_data["email"])
            assert success, f"Failed to initiate password reset for {user_type}"

            # Store reset token
            token = self._extract_token_from_message(message)
            if token:
                self.reset_tokens[user_type] = token

            # Reset password
            new_password = f"new_password_{user_type}_123"
            success, message = self.auth.reset_password(token, new_password)
            assert success, f"Failed to reset password for {user_type}"

            # Verify login with new password
            username = user_data["email"].split("@")[0]
            user = self.auth.authenticate(username, new_password)
            assert user is not None, f"Failed to login with new password for {user_type}"
            print(f"Password reset successful for {user_type}")

    def test_admin_functions(self):
        """Test admin-specific functionality"""
        print("Testing admin functions...")

        # Test admin login
        admin = self.auth.authenticate("admin", "admin123")
        assert admin is not None, "Failed to login as admin"
        assert admin.get("role") == "admin", "Incorrect admin role"

        # Test user management
        # Add implementation based on your admin functions

    def _extract_token_from_message(self, message: str) -> Optional[str]:
        """Extract token from email message"""
        if "Token:" in message:
            return message.split("Token: ")[1].strip()
        return None


if __name__ == "__main__":
    # Clean up any existing test file
    if os.path.exists("user_auth.json"):
        os.rename("user_auth.json", "user_auth.json.backup")

    try:
        # Run tests
        tester = AuthTester()
        tester.run_all_tests()
    finally:
        # Restore original auth file
        if os.path.exists("user_auth.json.backup"):
            if os.path.exists("user_auth.json"):
                os.remove("user_auth.json")
            os.rename("user_auth.json.backup", "user_auth.json")