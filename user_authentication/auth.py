import os
import hashlib
import json
import secrets
import smtplib
import re
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime, timedelta
from typing import Op<PERSON>, Dict, Tuple

from PySide6.QtCore import <PERSON><PERSON>imer
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QLabel, QLineEdit,
                               QPushButton, QGridLayout, QGroupBox, QWidget)


class EmailConfig:
    """Email configuration for office email system"""
    SMTP_SERVER = "smtp.gmail.com"  # Update based on your email provider
    SMTP_PORT = 587
    SENDER_EMAIL = "<EMAIL>"  # Sender email
    SENDER_PASSWORD = "tavarcozmcrwkgai"  # App-specific password for security
    COMPANY_DOMAIN = "manastuspace.com"  # Company's email domain

class UserAuth:
    """Enhanced user authentication with email verification"""

    def __init__(self, auth_file: str = "user_auth.json"):
        self.auth_file = auth_file
        self.verification_tokens = {}
        self.password_reset_tokens = {}
        self._load_users()

    def _load_users(self) -> None:
        """Load user data from file"""
        if os.path.exists(self.auth_file):
            with open(self.auth_file, 'r') as f:
                self.users = json.load(f)
        else:
            # Initialize with admin account
            admin_email = f"manjunath.neelmath@{EmailConfig.COMPANY_DOMAIN}"
            username = self._extract_username_from_email(admin_email)
            self.users = {
                username + "-" + "admin": {
                    "password": self._hash_password("admin123"),
                    "role": "admin",
                    "email": admin_email,
                    "verified": True
                }
            }
            self._save_users()

    def _save_users(self) -> None:
        """Save user data to file"""
        with open(self.auth_file, 'w') as f:
            json.dump(self.users, f)

    def _hash_password(self, password: str) -> str:
        """Hash password using SHA-256"""
        return hashlib.sha256(password.encode()).hexdigest()

    def _generate_token(self) -> str:
        """Generate a secure token for email verification"""
        return secrets.token_urlsafe(32)

    def _send_email(self, to_email: str, subject: str, body: str) -> bool:
        """Send email using office SMTP server"""
        try:
            # Verify email configuration
            if not EmailConfig.SENDER_PASSWORD:
                raise ValueError("Email password not configured. Please set EMAIL_PASSWORD environment variable.")

            msg = MIMEMultipart()
            msg['From'] = EmailConfig.SENDER_EMAIL
            msg['To'] = to_email
            msg['Subject'] = subject
            msg.attach(MIMEText(body, 'html'))

            with smtplib.SMTP(EmailConfig.SMTP_SERVER, EmailConfig.SMTP_PORT) as server:
                server.starttls()
                try:
                    server.login(EmailConfig.SENDER_EMAIL, EmailConfig.SENDER_PASSWORD)
                except smtplib.SMTPAuthenticationError:
                    print("Authentication failed. Please check your email credentials and app password.")
                    return False

                server.send_message(msg)
                print(f"Email sent successfully to {to_email}")
                return True
        except Exception as e:
            print(f"Error sending email: {str(e)}")
            return False

    @staticmethod
    def test_email_configuration():
        """Test email configuration"""
        try:
            server = smtplib.SMTP(EmailConfig.SMTP_SERVER, EmailConfig.SMTP_PORT)
            server.starttls()
            server.login(EmailConfig.SENDER_EMAIL, EmailConfig.SENDER_PASSWORD)
            print("Email configuration test successful!")
            server.quit()
            return True
        except Exception as e:
            print(f"Email configuration test failed: {str(e)}")
            return False

    def _validate_email(self, email: str) -> bool:
        """Validate if email belongs to company domain"""
        pattern = f"^[a-zA-Z0-9_.+-]+@{EmailConfig.COMPANY_DOMAIN}$"
        return bool(re.match(pattern, email))

    def _extract_username_from_email(self, email: str) -> str:
        """Extract username from email address"""
        name = email.split('@')[0]
        name = name.split('.')
        username = None
        for _ in name:
            if username is not None:
                username = username + ' ' + _
            else:
                username = _
        return username.title()

    def authenticate(self, username: str, password: str) -> Optional[Dict]:
        """Authenticate user credentials"""
        if username in self.users:
            user = self.users[username]
            if user["password"] == self._hash_password(password):
                if user.get("verified", False):
                    return user
                return {"error": "Email not verified"}
        return None

    def add_user(self, email: str, password: str, role: str = "user") -> Tuple[bool, str]:
        """Add new user with email verification"""
        if not self._validate_email(email):
            return False, "Invalid email domain"

        username = self._extract_username_from_email(email)
        if username in self.users:
            return False, "Username already exists"

        # Generate verification token
        token = self._generate_token()
        expiry = datetime.now() + timedelta(hours=24)
        self.verification_tokens[token] = {
            "username": username,
            "email": email,
            "password": self._hash_password(password),
            "role": role,
            "expiry": expiry.isoformat()
        }

        # Send verification email with proper URL

        verification_code = token
        email_body = f"""
        <html>
           <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
               <h2>Welcome to VAPR-iDEX</h2>
               <p>Your verification code is:</p>
               <div style="background-color: #f5f5f5; padding: 15px; text-align: center; margin: 20px 0;">
                   <p style="font-size: 24px; font-weight: bold; letter-spacing: 2px; margin: 0;">{verification_code}</p>
               </div>
               <p>Enter this code in the VAPR-iDEX application to complete your registration.</p>
               <p style="color: #666;">This code will expire in 24 hours.</p>
           </body>
        </html>
        """
        if self._send_email(email, "Verify Your Email", email_body):
            return True, "Verification email sent"
        return False, "Error sending verification email"

    def verify_email(self, token: str) -> Tuple[bool, str]:
        """Verify email using token"""
        if token not in self.verification_tokens:
            return False, "Invalid token"

        token_data = self.verification_tokens[token]
        expiry = datetime.fromisoformat(token_data["expiry"])
        if datetime.now() > expiry:
            del self.verification_tokens[token]
            return False, "Token expired"

        # Add verified user
        username = token_data["username"]
        self.users[username] = {
            "password": token_data["password"],
            "role": token_data["role"],
            "email": token_data["email"],
            "verified": True
        }
        self._save_users()

        # Clean up token
        del self.verification_tokens[token]
        return True, "Email verified successfully"

    def initiate_password_reset(self, email: str) -> Tuple[bool, str]:
        """Initiate password reset process"""
        if not self._validate_email(email):
            return False, "Invalid email domain"

        username_draft = self._extract_username_from_email(email)
        username = None
        if username_draft in self.users:
            if self.users[username_draft]["email"] != email:
                return False, "Email not found"
            username = username_draft
        elif username_draft + '-admin' in self.users:
            if self.users[username_draft + '-admin']["email"] != email:
                return False, "Email not found"
            username = username_draft + '-admin'

        # Generate reset token
        token = self._generate_token()
        expiry = datetime.now() + timedelta(hours=0.25)
        self.password_reset_tokens[token] = {
            "username": username,
            "expiry": expiry.isoformat()
        }

        # Send reset email with generated random token
        email_body = f"""
        <html>
            <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2>Password Reset Request</h2>
                <p>Your password reset verification code is:</p>
                <div style="background-color: #f5f5f5; padding: 15px; text-align: center; margin: 20px 0;">
                    <p style="font-size: 24px; font-weight: bold; letter-spacing: 2px; margin: 0;">{token}</p>
                </div>
                <p>Enter this code in the VAPR-iDEX application to reset your password.</p>
                <p style="color: #666;">This code will expire in 15 minutes.</p>
                <p style="color: #666;">If you didn't request this reset, please ignore this email.</p>
            </body>
        </html>
        """
        if self._send_email(email, "VAPR-iDEX Account Password Reset", email_body):
            return True, "Password reset code sent to your email"
        return False, "Error sending reset email"

    def reset_password(self, token: str, new_password: str) -> Tuple[bool, str]:
        """Reset password using token"""
        if token not in self.password_reset_tokens:
            return False, "Invalid token"

        token_data = self.password_reset_tokens[token]
        expiry = datetime.fromisoformat(token_data["expiry"])
        if datetime.now() > expiry:
            del self.password_reset_tokens[token]
            return False, "Token expired"

        # Update password
        username = token_data["username"]
        self.users[username]["password"] = self._hash_password(new_password)
        self._save_users()

        # Clean up token
        del self.password_reset_tokens[token]
        return True, "Password reset successfully"


class LoginDialog(QDialog):
    """Enhanced login dialog with password reset option"""

    def __init__(self, auth: UserAuth, parent=None):
        super().__init__(parent)
        self.auth = auth
        self.user_data = None
        self.setup_ui()

    def setup_ui(self):
        self.setWindowTitle("Login")
        self.setModal(True)
        self.setMinimumWidth(350)

        layout = QVBoxLayout(self)
        layout.setSpacing(10)

        # Username/Email field
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("Username or Email")
        layout.addWidget(self.username_edit)

        # Password field
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("Password")
        self.password_edit.setEchoMode(QLineEdit.Password)
        layout.addWidget(self.password_edit)

        # Login button
        login_button = QPushButton("Login")
        login_button.clicked.connect(self.try_login)
        layout.addWidget(login_button)

        # Forgot password link
        forgot_password = QPushButton("Forgot Password?")
        forgot_password.setStyleSheet("border: none; text-decoration: underline;")
        forgot_password.clicked.connect(self.show_password_reset)
        layout.addWidget(forgot_password)

        # Error label
        self.error_label = QLabel("")
        self.error_label.setStyleSheet("color: red;")
        layout.addWidget(self.error_label)

        self.setLayout(layout)

    def try_login(self):
        username = self.username_edit.text()
        password = self.password_edit.text()

        if not username or not password:
            self.error_label.setText("Please enter both username and password")
            return

        user_data = self.auth.authenticate(username, password)
        if isinstance(user_data, dict) and "error" in user_data:
            self.error_label.setText(user_data["error"])
        elif user_data:
            self.user_data = user_data
            self.accept()
        else:
            self.error_label.setText("Invalid username or password")
            self.password_edit.clear()

    def show_password_reset(self):
        dialog = PasswordResetDialog(self.auth, self)
        dialog.exec()


class PasswordResetDialog(QDialog):
    """Dialog for password reset request with email verification"""

    def __init__(self, auth: UserAuth, parent=None):
        super().__init__(parent)
        self.auth = auth
        self.verification_request = False
        self.setup_ui()

    def setup_ui(self):
        self.setWindowTitle("Reset Password")
        self.setModal(True)
        self.setMinimumWidth(300)

        layout = QVBoxLayout(self)
        layout.setSpacing(10)

        # Email field
        email_layout = QVBoxLayout()
        self.email_label = QLabel("Enter your email address:")
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("<EMAIL>")
        email_layout.addWidget(self.email_label)
        email_layout.addWidget(self.email_edit)
        layout.addLayout(email_layout)

        # Request verification button
        self.request_button = QPushButton("Request Verification Code")
        self.request_button.clicked.connect(self.request_verification)
        self.request_button.setStyleSheet(
            """
            QPushButton{
                background-color: #446699;
                color: white;
                padding: 8px;
                border-radius: 4px;
            }
            
            QPushButton:hover{
                background-color: #557799;
            }
            """
        )
        layout.addWidget(self.request_button)

        # Verification code section (initially hidden)
        self.verification_widget = QWidget()
        verification_layout = QVBoxLayout(self.verification_widget)

        self.code_label = QLabel("Enter verification code:")
        self.code_edit = QLineEdit()
        self.code_edit.setPlaceholderText("Enter code from email")
        verification_layout.addWidget(self.code_label)
        verification_layout.addWidget(self.code_edit)

        # New password fields (initially hidden)
        self.new_password_label = QLabel("Enter new password:")
        self.new_password_edit = QLineEdit()
        self.new_password_edit.setEchoMode(QLineEdit.Password)
        self.new_password_edit.setPlaceholderText("New password")
        verification_layout.addWidget(self.new_password_label)
        verification_layout.addWidget(self.new_password_edit)

        self.confirm_password_label = QLabel("Confirm new password:")
        self.confirm_password_edit = QLineEdit()
        self.confirm_password_edit.setEchoMode(QLineEdit.Password)
        self.confirm_password_edit.setPlaceholderText("Confirm new password")
        verification_layout.addWidget(self.confirm_password_label)
        verification_layout.addWidget(self.confirm_password_edit)

        # Reset password button
        self.reset_button = QPushButton("Reset password")
        self.reset_button.clicked.connect(self.reset_password)
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #449944;
                color: white;
                padding: 8px;
                border-radius: 4px;
            }
            
            QPushButton:hover{
                background-color: #55aa55;
            }
        """)
        verification_layout.addWidget(self.reset_button)

        layout.addWidget(self.verification_widget)
        self.verification_widget.hide()

        # Status label
        self.status_label = QLabel()
        self.status_label.setWordWrap(True)
        self.status_label.setStyleSheet("padding: 10px;")
        layout.addWidget(self.status_label)

        self.setLayout(layout)

    def request_verification(self):
        email = self.email_edit.text().strip()
        if not email:
            self.show_error("Please enter your email address")
            return

        success, message = self.auth.initiate_password_reset(email)
        if success:
            self.show_success("Verification code sent! Check your email.")
            self.verification_widget.show()
            self.email_edit.setEnabled(False)
            self.request_button.setEnabled(False)
            self.verification_request = True

        else:
            self.show_error(message)

    def reset_password(self):
        if not self.verification_request:
            self.show_error("Please request a verification code first")
            return

        code = self.code_edit.text().strip()
        new_password = self.new_password_edit.text()
        confirm_password = self.confirm_password_edit.text()

        if not code:
            self.show_error("Please enter the verification code")
            return

        if not new_password or not confirm_password:
            self.show_error("Please enter and confirm your new password")
            return

        if new_password != confirm_password:
            self.show_error("Password do not match")
            return

        if len(new_password) < 8:
            self.show_error("Password must be at least 8 characters long")
            return

        success, message = self.auth.reset_password(code, new_password)
        if success:
            self.show_success("Password reset successfully!")
            QTimer.singleShot(1500, self.accept) # Close dialog box after 1.5 seconds
        else:
            self.show_error(message)

    def show_error(self, message):
        self.status_label.setStyleSheet("color: #ff4444; padding: 10px;")
        self.status_label.setText(message)

    def show_success(self, message):
        self.status_label.setStyleSheet("color: #44aa44; padding: 10px;")
        self.status_label.setText(message)


class UserManagementDialog(QDialog):
    def __init__(self, auth: UserAuth, parent=None):
        super().__init__(parent)
        self.auth = auth
        self.setup_ui()
        self.verification_sent = False
        self.new_user_email = None

    def setup_ui(self):
        self.setWindowTitle("User Management")
        self.setModal(True)
        self.setMinimumWidth(400)

        layout = QVBoxLayout(self)
        layout.setSpacing(15)

        # User Creation Section
        user_group = QGroupBox("Add New User")
        user_layout = QGridLayout()

        # Email field
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("Office Email Address")
        user_layout.addWidget(QLabel("Email:"), 0, 0)
        user_layout.addWidget(self.email_edit, 0, 1)

        # Role selection
        self.role_edit = QLineEdit()
        self.role_edit.setPlaceholderText("Role (e.g., user, manager)")
        user_layout.addWidget(QLabel("Role:"), 1, 0)
        user_layout.addWidget(self.role_edit, 1, 1)

        user_group.setLayout(user_layout)
        layout.addWidget(user_group)

        # Add user button
        self.add_button = QPushButton("Add User")
        self.add_button.clicked.connect(self.add_user)
        layout.addWidget(self.add_button)

        # Verification Section (initially hidden)
        self.verification_group = QGroupBox("Email Verification")
        verification_layout = QGridLayout()

        self.code_input = QLineEdit()
        self.code_input.setPlaceholderText("Enter verification code")
        verification_layout.addWidget(QLabel("Verification Code:"), 0, 0)
        verification_layout.addWidget(self.code_input, 0, 1)

        self.new_password = QLineEdit()
        self.new_password.setPlaceholderText("Enter new password")
        self.new_password.setEchoMode(QLineEdit.Password)
        verification_layout.addWidget(QLabel("New Password:"), 1, 0)
        verification_layout.addWidget(self.new_password, 1, 1)

        self.confirm_password = QLineEdit()
        self.confirm_password.setPlaceholderText("Confirm new password")
        self.confirm_password.setEchoMode(QLineEdit.Password)
        verification_layout.addWidget(QLabel("Confirm Password:"), 2, 0)
        verification_layout.addWidget(self.confirm_password, 2, 1)

        self.verify_button = QPushButton("Verify & Set Password")
        self.verify_button.clicked.connect(self.verify_and_set_password)
        verification_layout.addWidget(self.verify_button, 3, 0, 1, 2)

        self.verification_group.setLayout(verification_layout)
        self.verification_group.hide()
        layout.addWidget(self.verification_group)

        # Status label
        self.status_label = QLabel("")
        layout.addWidget(self.status_label)

        self.setLayout(layout)

    def add_user(self):
        email = self.email_edit.text()
        role = self.role_edit.text() or "user"

        if not email:
            self.show_status("Please enter an email address", error=True)
            return

        # Generate a temporary password
        temp_password = secrets.token_urlsafe(12)
        success, message = self.auth.add_user(email, temp_password, role)

        if success:
            self.verification_sent = True
            self.new_user_email = email
            self.show_status("Verification code sent! Check email.", error=False)
            self.verification_group.show()
            self.email_edit.setEnabled(False)
            self.role_edit.setEnabled(False)
            self.add_button.setEnabled(False)
        else:
            self.show_status(message, error=True)

    def verify_and_set_password(self):
        if not self.verification_sent:
            return

        code = self.code_input.text().strip()
        new_pass = self.new_password.text()
        confirm_pass = self.confirm_password.text()

        if not code:
            self.show_status("Please enter verification code", error=True)
            return

        if not new_pass or not confirm_pass:
            self.show_status("Please enter and confirm new password", error=True)
            return

        if new_pass != confirm_pass:
            self.show_status("Passwords do not match", error=True)
            return

        # Verify email
        success, message = self.auth.verify_email(code)
        if success:
            username = self.auth._extract_username_from_email(self.new_user_email)

            # Update password
            user = self.auth.users.get(username)
            if user:
                user['password'] = self.auth._hash_password(new_pass)
                self.auth._save_users()
                self.show_status("User verified and password set successfully!", error=False)
                QTimer.singleShot(1500, self.accept)
            else:
                self.show_status("Error updating password", error=True)
        else:
            self.show_status(message, error=True)

    def show_status(self, message, error=False):
        self.status_label.setText(message)
        self.status_label.setStyleSheet(f"color: {'red' if error else 'green'};")

class VerificationDialog(QDialog):
    def __init__(self, auth: UserAuth, parent=None):
        super().__init__(parent)
        self.auth = auth
        self.setup_ui()

    def setup_ui(self):
        self.setWindowTitle("Verify Email")
        self.setModal(True)
        self.setMinimumWidth(400)

        layout = QVBoxLayout(self)

        # Instructions
        instructions = QLabel("Enter the verification code sent to your email:")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)

        # Code input
        self.code_input = QLineEdit()
        self.code_input.setPlaceholderText("Verification Code")
        layout.addWidget(self.code_input)

        # Verify button
        verify_button = QPushButton("Verify")
        verify_button.clicked.connect(self.verify_code)
        verify_button.setStyleSheet("background-color: #446699; color: white; padding: 8px;")
        layout.addWidget(verify_button)

        # Status label
        self.status_label = QLabel()
        self.status_label.setWordWrap(True)
        layout.addWidget(self.status_label)

        self.setLayout(layout)

    def verify_code(self):
        code = self.code_input.text().strip()
        if not code:
            self.status_label.setStyleSheet("color: red;")
            self.status_label.setText("Please enter the verification code")
            return

        success, message = self.auth.verify_email(code)
        if success:
            self.status_label.setStyleSheet("color: green;")
            self.status_label.setText("Email verified successfully!")
            QTimer.singleShot(1500, self.accept)  # Close after 1.5 seconds
        else:
            self.status_label.setStyleSheet("color: red;")
            self.status_label.setText(message)

if __name__ == "__main__":
    print("Testing Gmail Configuration...")
    auth = UserAuth()

    # Test email configuration first
    if UserAuth.test_email_configuration():
        print("\nConfiguration test passed! Sending test email...")

        # Send a test email to yourself
        test_subject = "VAPR-iDEX System Test Email"
        test_body = """
        <html>
            <body>
                <h2>Test Email</h2>
                <p>This is a test email from your VAPR-iDEX system.</p>
                <p>If you received this email, your email configuration is working correctly!</p>
            </body>
        </html>
        """

        # Send to your work email to test both sending and domain validation
        test_recipient = "<EMAIL>"  # Replace with your work email

        if auth._send_email(test_recipient, test_subject, test_body):
            print("Test email sent successfully!")
        else:
            print("Failed to send test email.")
    else:
        print("Email configuration test failed. Please check your settings.")