
# VAPR-iDEX Modular Migration Checklist

## Completed by <PERSON><PERSON><PERSON>
- [x] Directory structure created
- [x] Original gui_main.py backed up
- [x] New modular files created

## Manual Steps Required

### 1. Update Imports
Update import statements in the following files:
- [ ] Any files importing from gui_main
- [ ] Test files
- [ ] Documentation files

### 2. Move Remaining Functionality
The following functionality from gui_main.py needs to be manually moved:

#### Data Loading Methods (lines ~4600-4900)
- [ ] load_data_from_json_file()
- [ ] save_json_file()
- [ ] load_test_data()
- [ ] update_ui_with_test_data()

#### Plot Generation Methods (lines ~1500-3000)
- [ ] create_temperature_plot()
- [ ] create_pressure_plot()
- [ ] generate_max_temperatures_plot()
- [ ] plot_with_title()

#### Form Handling Methods (lines ~1400-1600)
- [ ] setup_connections() - remaining connections
- [ ] value_changed() methods
- [ ] reset_input_fields()

#### Report Generation Methods (lines ~4200-4400)
- [ ] generate_report()
- [ ] save_to_database()

### 3. Update Configuration
- [ ] Review config/app_config.py and add missing constants
- [ ] Update any hardcoded values to use configuration

### 4. Testing
- [ ] Test application startup
- [ ] Test each major functionality area
- [ ] Test data loading and saving
- [ ] Test plot generation
- [ ] Test report generation
- [ ] Test database operations

### 5. Documentation
- [ ] Update README.md
- [ ] Update API documentation
- [ ] Update user documentation

## Notes
- The new architecture separates concerns into logical modules
- Each controller handles a specific domain (auth, database, plots, etc.)
- Services handle data operations and business logic
- Managers handle UI state and coordination
- Configuration is centralized in config/app_config.py

## Rollback Plan
If issues arise, restore from gui_main_backup.py:
1. Copy gui_main_backup.py to gui_main.py
2. Remove new modular files
3. Update any changed import statements
