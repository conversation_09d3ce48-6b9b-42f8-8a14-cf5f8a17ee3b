"""
Icon Service
Handles icon setup and management for the application.
"""

import sys
from PySide6.QtCore import QSize
from PySide6.QtGui import QIcon
from PySide6.QtWidgets import QApplication

from src.utils import get_resource_path


class IconService:
    """Service for managing application icons."""
    
    def setup_all_icons(self, main_window):
        """Setup all icons for the application."""
        try:
            self._setup_application_icon(main_window)
            self._setup_navigation_icons(main_window)
            self._setup_data_icons(main_window)
            self._setup_combo_box_icons(main_window)
        except Exception as e:
            print(f"Error setting up icons: {str(e)}")
    
    def _setup_application_icon(self, main_window):
        """Setup application and window icons."""
        # Set application icon
        app_icon = QIcon()
        app_icon_path = get_resource_path("assets/icon.ico")
        app_icon.addFile(app_icon_path, QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        main_window.setWindowIcon(app_icon)
        QApplication.instance().setWindowIcon(app_icon)

        # Set Windows taskbar icon explicitly
        if hasattr(sys, 'frozen'):
            import ctypes
            from config.app_config import AppConfig
            ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(AppConfig.APP_ID)
    
    def _setup_navigation_icons(self, main_window):
        """Setup navigation button icons."""
        ui = main_window.ui
        
        # Back button icon for Heater cycles
        back_icon = QIcon()
        back_icon_path = get_resource_path("assets/left_arrow.drawio.png")
        back_icon.addFile(back_icon_path, QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        ui.btnCycleBack.setIcon(back_icon)
        ui.btnCycleBack.setIconSize(QSize(70, 70))

        # Next button icon for Heater cycles
        next_icon = QIcon()
        next_icon_path = get_resource_path("assets/right_arrow.drawio.png")
        next_icon.addFile(next_icon_path, QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        ui.btnCycleNext.setIcon(next_icon)
        ui.btnCycleNext.setIconSize(QSize(70, 70))
    
    def _setup_data_icons(self, main_window):
        """Setup data-related icons."""
        ui = main_window.ui
        
        # Temperature load Icon
        temperature_icon = QIcon()
        temperature_icon_path = get_resource_path("assets/temperature_icon.png")
        temperature_icon.addFile(temperature_icon_path, QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        ui.temp_icon.setIcon(temperature_icon)
        ui.temp_icon.setIconSize(QSize(185, 185))
        ui.btnTempDataInd.setIcon(temperature_icon)
        ui.btnTempDataInd.setIconSize(QSize(45, 45))

        # Pressure load Icon
        pressure_icon = QIcon()
        pressure_icon_path = get_resource_path("assets/pressure_icon.png")
        pressure_icon.addFile(pressure_icon_path, QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        ui.pressure_icon.setIcon(pressure_icon)
        ui.pressure_icon.setIconSize(QSize(185, 185))
        ui.btnPressureDataInd.setIcon(pressure_icon)
        ui.btnPressureDataInd.setIconSize(QSize(55, 55))
    
    def _setup_combo_box_icons(self, main_window):
        """Setup combo box dropdown icons."""
        ui = main_window.ui
        
        # Setting down arrow icon for all combo boxes
        down_arrow_path = get_resource_path("assets/down-arrow.png").replace('\\', '/')
        down_arrow_style = f"""
            QComboBox::down-arrow {{
                image: url("{down_arrow_path}");
                width: 24px;
                height: 24px;
            }}
            QComboBox::drop-down {{
                border: none;
                background: transparent;
                width: 20px;
                margin-right: 8px;
            }}
        """

        # Apply style to all combo boxes
        combo_boxes = [
            ui.comboBoxXAxisTemp,
            ui.comboBoxYAxisTemp,
            ui.comboBoxXAxisPressure,
            ui.comboBoxYAxisPressure
        ]

        for combo_box in combo_boxes:
            if hasattr(combo_box, 'styleSheet'):
                current_style = combo_box.styleSheet()
                combo_box.setStyleSheet(current_style + down_arrow_style)
