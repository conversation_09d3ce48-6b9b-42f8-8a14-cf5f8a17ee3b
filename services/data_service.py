"""
Data Service
Handles data operations and management.
"""

import os
import json
import pandas as pd
from typing import Optional, Dict, Any
from PySide6.QtCore import QObject

from src.data import DataLoader
from src.analysis import Temperature<PERSON>nalyzer, PressureAnalyzer
from src.visualization import Plot<PERSON>anager
from src.report_generation import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ator, ImageHandler
from src.report_generation.setup_fonts import setup_fonts
from config.app_config import AppConfig


class DataService(QObject):
    """Service for handling data operations."""
    
    def __init__(self, main_window):
        super().__init__(main_window)
        self.main_window = main_window
        
        # Initialize data structures
        self._initialize_data_structures()
        
        # Initialize core components
        self._initialize_core_components()
    
    def _initialize_data_structures(self):
        """Initialize data storage structures."""
        self.temperature_data = None
        self.pressure_data = None
        self.test_data = {}
        self.filtered_temp_data = None
        self.selected_ranges = []
        self.selected_cols = []
        
        # Plot tracking
        self.report_plots = {
            'default': [],  # For default temperature plots
            'custom': []    # For custom plots
        }
        
        # Color assignments
        self.color = {}
        
        # Store figures for report
        self.current_figures = {}
        
        # Store references in main window
        self.main_window.temperature_data = self.temperature_data
        self.main_window.pressure_data = self.pressure_data
        self.main_window.test_data = self.test_data
        self.main_window.filtered_temp_data = self.filtered_temp_data
        self.main_window.selected_ranges = self.selected_ranges
        self.main_window.selected_cols = self.selected_cols
        self.main_window.report_plots = self.report_plots
        self.main_window.color = self.color
        self.main_window.current_figures = self.current_figures
    
    def _initialize_core_components(self):
        """Initialize backend analysis and processing components."""
        self.data_loader = DataLoader(self.main_window)
        self.temp_analyzer = TemperatureAnalyzer()
        self.pressure_analyzer = PressureAnalyzer()
        self.plot_manager = PlotManager()
        self.image_handler = ImageHandler()

        # Setup fonts first
        if not setup_fonts():
            from PySide6.QtWidgets import QMessageBox
            import sys
            QMessageBox.critical(
                self.main_window, 
                "Error",
                "Failed to setup required fonts. Please check your internet connection and try again."
            )
            sys.exit(1)

        # Initialize report generator
        try:
            self.report_generator = TestReportGenerator()
        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            import sys
            QMessageBox.critical(
                self.main_window, 
                "Error", 
                f"Failed to initialize report generator: {str(e)}"
            )
            sys.exit(1)
        
        # Store references in main window
        self.main_window.data_loader = self.data_loader
        self.main_window.temp_analyzer = self.temp_analyzer
        self.main_window.pressure_analyzer = self.pressure_analyzer
        self.main_window.plot_manager = self.plot_manager
        self.main_window.image_handler = self.image_handler
        self.main_window.report_generator = self.report_generator
    
    def reset_data(self):
        """Reset all data to initial state."""
        # Reset data structures
        self.temperature_data = None
        self.pressure_data = None
        self.filtered_temp_data = None
        self.report_plots = {'default': [], 'custom': []}
        self.selected_ranges = []
        self.selected_cols = []
        
        # Update main window references
        self.main_window.temperature_data = self.temperature_data
        self.main_window.pressure_data = self.pressure_data
        self.main_window.filtered_temp_data = self.filtered_temp_data
        self.main_window.report_plots = self.report_plots
        self.main_window.selected_ranges = self.selected_ranges
        self.main_window.selected_cols = self.selected_cols
        
        # Reset temp_data.json file
        self._reset_temp_data_file()
    
    def _reset_temp_data_file(self):
        """Reset temp_data.json file to blank."""
        user_home = os.path.expanduser("~")
        json_path = os.path.join(user_home, AppConfig.Paths.TEMP_DATA_FILE)
        
        try:
            if os.path.exists(json_path):
                with open(json_path, "w") as temp_json_file:
                    json.dump({}, temp_json_file)
                print(f"{json_path} has been emptied.")
        except Exception as e:
            print(f"Error resetting temp_data.json: {str(e)}")
    
    def load_temperature_data(self, file_path: str) -> bool:
        """Load temperature data from file."""
        try:
            # Use data loader to load temperature data
            self.temperature_data = pd.read_csv(file_path)  # Simplified for example
            self.main_window.temperature_data = self.temperature_data
            return True
        except Exception as e:
            print(f"Error loading temperature data: {str(e)}")
            return False
    
    def load_pressure_data(self, file_path: str) -> bool:
        """Load pressure data from file."""
        try:
            # Use data loader to load pressure data
            self.pressure_data = pd.read_csv(file_path)  # Simplified for example
            self.main_window.pressure_data = self.pressure_data
            return True
        except Exception as e:
            print(f"Error loading pressure data: {str(e)}")
            return False
    
    def has_valid_temperature_data(self) -> bool:
        """Check if valid temperature data exists."""
        if self.temperature_data is None:
            return False
        
        if not isinstance(self.temperature_data, pd.DataFrame):
            return False
        
        if self.temperature_data.empty:
            return False
        
        # Check if it has at least one temperature column (not just time)
        temp_columns = [col for col in self.temperature_data.columns 
                       if col not in ['time', 'Time']]
        return len(temp_columns) >= AppConfig.Validation.MIN_TEMPERATURE_COLUMNS
    
    def has_valid_pressure_data(self) -> bool:
        """Check if valid pressure data exists."""
        if self.pressure_data is None:
            return False
        
        if not isinstance(self.pressure_data, pd.DataFrame):
            return False
        
        if self.pressure_data.empty:
            return False
        
        # Check if it has at least one pressure column (not just time)
        pressure_columns = [col for col in self.pressure_data.columns 
                           if col not in ['time', 'Time']]
        return len(pressure_columns) >= AppConfig.Validation.MIN_PRESSURE_COLUMNS
    
    def assign_color(self, column: str) -> str:
        """Assign a color to a column."""
        if column not in self.color:
            # Get next available color from palette
            color_index = len(self.color) % len(AppConfig.Colors.PLOT_PALETTE)
            self.color[column] = AppConfig.Colors.PLOT_PALETTE[color_index]
        return self.color[column]
