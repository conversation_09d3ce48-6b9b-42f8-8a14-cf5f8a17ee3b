"""
Main Controller
Central controller for coordinating application logic.
"""

import logging
from typing import Dict, Any
from PySide6.QtCore import QObject
from PySide6.QtWidgets import QMessageBox

from config.app_config import AppConfig


class MainController(QObject):
    """Main controller for coordinating application operations."""
    
    def __init__(self, main_window):
        super().__init__(main_window)
        self.main_window = main_window
        self.ui = main_window.ui
        self.logger = logging.getLogger(__name__)
        
        # Section mapping for navigation
        self.section_mapping = {
            "Test Prerequisite": ('test_prereq', 'testPrereqSubSections', 'basicInformation'),
            "Heater Operation": ('heater_op', 'htrOpSubSections', 'heaterInformation'),
            "Post Test Analysis": ('post_test', 'pstTestAnSubSections', 'postTestingObs'),
            "PlotWindow": ('plot_controls', 'plotControlsFrame', 'plots'),
            "Performance": ('performance', 'perforSubSections', 'performance')
        }
    
    def handle_mode_change(self, mode_name: str, mode_index: int):
        """Handle mode changes from toggle switches."""
        self.logger.info(f"Mode changed to: {mode_name} (index: {mode_index})")
        
        if mode_name == "New Test":
            self._handle_new_test_mode()
        elif mode_name == "Existing Test":
            self._handle_existing_test_mode()
        elif mode_name == "Database":
            self._handle_database_mode()
        elif mode_name == "Temperature":
            self._handle_temperature_plot_mode()
        elif mode_name == "Pressure":
            self._handle_pressure_plot_mode()
    
    def _handle_new_test_mode(self):
        """Handle new test mode selection."""
        self.main_window.menuBar().clear()
        self.ui.tabWidget.setCurrentIndex(0)
        
        # Reset data if needed
        if hasattr(self.main_window, 'data_service'):
            self.main_window.data_service.reset_data()
    
    def _handle_existing_test_mode(self):
        """Handle existing test mode selection."""
        self.main_window.menuBar().clear()
        self.ui.tabWidget.setCurrentIndex(0)
        
        # Show existing data dialog
        if hasattr(self.main_window, 'existing_data_dialog'):
            self.main_window.existing_data_dialog.exec()
    
    def _handle_database_mode(self):
        """Handle database mode selection."""
        # Check authentication first
        if hasattr(self.main_window, 'auth_controller'):
            if self.main_window.auth_controller.show_login():
                self.ui.tabWidget.setCurrentIndex(1)
                
                # Setup admin menu if user is admin
                if self.main_window.auth_controller.is_admin():
                    self.main_window.auth_controller.setup_admin_menu(self.main_window.menuBar())
            else:
                # Reset to first mode if authentication failed
                self.ui.tabWidget.setCurrentIndex(0)
    
    def _handle_temperature_plot_mode(self):
        """Handle temperature plot mode."""
        if not self._check_temperature_data():
            return
            
        self._update_content_widget('plots')
        self._show_plot_settings('temperature')
        self.ui.tabWidgetPlotSettings.setCurrentWidget(self.ui.temperature_tab)
        self.ui.lblCurentSection.setText('Temperature Plot')
    
    def _handle_pressure_plot_mode(self):
        """Handle pressure plot mode."""
        if not self._check_pressure_data():
            return
            
        self._update_content_widget('plots')
        self._show_plot_settings('pressure')
        self.ui.tabWidgetPlotSettings.setCurrentWidget(self.ui.pressure_tab)
        self.ui.lblCurentSection.setText('Pressure Plot')
    
    def handle_section_change(self, section_name: str):
        """Handle section button clicks with animations."""
        try:
            self.logger.info(f"Section changed to: {section_name}")
            
            # Update current section label
            self.ui.lblCurentSection.setText(section_name)
            
            # Handle section change based on mapping
            if section_name in self.section_mapping:
                section_key, section_widget_name, content_widget_name = self.section_mapping[section_name]
                
                # Get widgets
                section_widget = getattr(self.ui, section_widget_name, None)
                content_widget = getattr(self.ui, content_widget_name, None)
                
                if section_widget and content_widget:
                    # Handle animation
                    if hasattr(self.main_window, 'ui_manager'):
                        animations = self.main_window.ui_manager.section_animations
                        if section_key in animations:
                            animations[section_key].toggle()
                    
                    # Update content
                    self._update_content_widget(content_widget_name)
                    
                    # Update button states
                    current_page = self.ui.contentStack.currentWidget().objectName()
                    if hasattr(self.main_window, 'ui_manager'):
                        self.main_window.ui_manager.update_button_states(current_page)
                        
        except Exception as e:
            self.logger.error(f"Error handling section change: {str(e)}")
            QMessageBox.critical(
                self.main_window,
                "Error",
                f"Failed to change section: {str(e)}"
            )
    
    def force_enable_plots_button(self):
        """Enable plots button if valid data exists."""
        self.logger.info("Checking data validity for plots button")
        
        # Check data validity
        temp_valid = False
        pressure_valid = False
        
        if hasattr(self.main_window, 'data_service'):
            temp_valid = self.main_window.data_service.has_valid_temperature_data()
            pressure_valid = self.main_window.data_service.has_valid_pressure_data()
        
        # Enable button if we have valid data
        if temp_valid or pressure_valid:
            try:
                self.ui.btnPlots.setEnabled(True)
                self.logger.info("Plots button enabled - valid data found")
                
                # Setup plot controls if available
                if hasattr(self.main_window, 'plot_controller'):
                    self.main_window.plot_controller.setup_plot_controls()
                    
            except Exception as e:
                self.logger.error(f"Error enabling plots button: {str(e)}")
        else:
            self.logger.info("Plots button not enabled - no valid data found")
    
    def _check_temperature_data(self) -> bool:
        """Check if temperature data is available."""
        if hasattr(self.main_window, 'data_service'):
            if not self.main_window.data_service.has_valid_temperature_data():
                QMessageBox.warning(
                    self.main_window,
                    'Load Data',
                    'Please load the Temperature Data first!'
                )
                return False
        return True
    
    def _check_pressure_data(self) -> bool:
        """Check if pressure data is available."""
        if hasattr(self.main_window, 'data_service'):
            if not self.main_window.data_service.has_valid_pressure_data():
                QMessageBox.warning(
                    self.main_window,
                    'Load Data',
                    'Please load the Pressure Data first!'
                )
                return False
        return True
    
    def _update_content_widget(self, widget_name: str):
        """Update content stack to show specified widget."""
        widget = getattr(self.ui, widget_name, None)
        if widget:
            self.ui.contentStack.setCurrentWidget(widget)
    
    def _show_plot_settings(self, plot_type: str):
        """Show plot settings for specified type."""
        # Implementation depends on specific plot settings UI
        pass
    
    # Placeholder methods for functionality that will be implemented
    def handle_temp_matrix_clicked(self):
        """Handle temperature matrix button click."""
        # Delegate to plot controller
        if hasattr(self.main_window, 'plot_controller'):
            self.main_window.plot_controller.handle_temp_matrix_clicked()
    
    def change_page(self, next: bool = True):
        """Change to next or previous page."""
        # Implementation for page navigation
        pass
    
    def calculate_performance(self):
        """Calculate performance metrics."""
        # Implementation for performance calculation
        pass
    
    def generate_report(self):
        """Generate test report."""
        # Implementation for report generation
        pass
    
    def save_to_database(self):
        """Save data to database."""
        # Delegate to database controller
        if hasattr(self.main_window, 'db_controller'):
            # Implementation for saving to database
            pass
    
    def update_table(self):
        """Update data table."""
        # Implementation for table updates
        pass
    
    def select_photo(self, photo_id: str):
        """Handle photo selection."""
        # Implementation for photo selection
        pass
