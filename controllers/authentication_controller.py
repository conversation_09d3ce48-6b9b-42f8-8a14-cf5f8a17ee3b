"""
Authentication Controller
Handles user authentication and authorization logic.
"""
from PySide6.QtGui import QAction
from PySide6.QtWidgets import QDialog, QMessageBox
from PySide6.QtCore import QObject

from user_authentication import Use<PERSON><PERSON><PERSON>, VerificationDialog
from user_authentication.auth import LoginDialog, UserManagementDialog, PasswordResetDialog


class AuthenticationController(QObject):
    """Controller for handling authentication operations."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.auth = UserAuth()
        self.current_user = None
        self.parent_window = parent
    
    def show_login(self) -> bool:
        """Show login dialog and handle authentication."""
        login_dialog = LoginDialog(self.auth, self.parent_window)
        if login_dialog.exec() == QDialog.Accepted and login_dialog.user_data:
            self.current_user = login_dialog.user_data
            return True
        return False
    
    def setup_admin_menu(self, menu_bar):
        """Setup admin-specific menu items."""
        if not self.current_user or self.current_user.get("role") != "admin":
            return
            
        admin_menu = menu_bar.addMenu('Admin')

        # User management action
        manage_users_action = QAction('Manage Users', self.parent_window)
        manage_users_action.triggered.connect(self.show_user_management)
        admin_menu.addAction(manage_users_action)

        # Change password action
        change_password_action = QAction("Change Password", self.parent_window)
        change_password_action.triggered.connect(self.show_admin_password_change)
        admin_menu.addAction(change_password_action)
    
    def show_admin_password_change(self):
        """Show password reset dialog for admin."""
        if self.current_user and self.current_user.get("email"):
            dialog = PasswordResetDialog(self.auth, self.parent_window)
            dialog.email_edit.setText(self.current_user["email"])
            dialog.email_edit.setEnabled(False)  # Lock email field for admin
            dialog.exec()
    
    def show_user_management(self):
        """Show user management dialog."""
        if self.current_user and self.current_user["role"] == "admin":
            dialog = UserManagementDialog(self.auth, self.parent_window)
            if dialog.exec() == QDialog.Accepted:
                # Show verification dialog after adding new user
                self.show_verification_dialog()
    
    def show_verification_dialog(self):
        """Show dialog for verifying user email."""
        dialog = VerificationDialog(self.auth, self.parent_window)
        dialog.exec()
    
    def logout(self):
        """Handle user logout."""
        reply = QMessageBox.question(
            self.parent_window,
            'Logout',
            'Are you sure you want to logout?',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.current_user = None
            if self.parent_window:
                self.parent_window.close()
            # Restart application
            from PySide6.QtWidgets import QApplication
            QApplication.instance().exit()
    
    def is_authenticated(self) -> bool:
        """Check if user is authenticated."""
        return self.current_user is not None
    
    def is_admin(self) -> bool:
        """Check if current user is admin."""
        return (self.current_user is not None and 
                self.current_user.get("role") == "admin")
