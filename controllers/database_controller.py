"""
Database Controller
Handles database operations and data management.
"""

import traceback
from typing import Op<PERSON>, Dict, Any, List
from PySide6.QtCore import QObject, QTimer
from PySide6.QtWidgets import QDialog, QMessageBox, QTableWidgetItem, QInputDialog

from src.database import <PERSON>Handler, DatabaseConfig
from src.database.database_login import DatabaseLoginDialog
from gui.dialog import DataVisualizationWidget
from config.app_config import AppConfig


class DatabaseController(QObject):
    """Controller for database operations."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_handler = None
        self.parent_window = parent
        self._setup_search_timer()
    
    def _setup_search_timer(self):
        """Setup search timer for delayed search."""
        self.search_timer = QTimer(self)
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)
    
    def initialize_connection(self) -> bool:
        """Initialize database connection."""
        login_dialog = DatabaseLoginDialog(self.parent_window)
        if login_dialog.exec() == QDialog.Accepted:
            try:
                # Set the password in configuration
                DatabaseConfig.set_database_password(login_dialog.password)

                # Initialize database handler
                self.db_handler = DatabaseHandler()

                # Test connection
                test_params = DatabaseConfig.get_connection_params(is_server=True)
                success, message = DatabaseConfig.test_connection(test_params)

                if success:
                    QMessageBox.information(
                        self.parent_window, 
                        "Success", 
                        "Connected to database successfully!"
                    )
                    return True
                else:
                    QMessageBox.critical(
                        self.parent_window, 
                        "Connection Error", 
                        f"Failed to connect to database: {message}"
                    )
                    self.db_handler = None
                    return False
                    
            except Exception as e:
                QMessageBox.critical(
                    self.parent_window, 
                    "Error", 
                    f"Failed to initialize database: {str(e)}"
                )
                self.db_handler = None
                return False
        return False
    
    def is_connected(self) -> bool:
        """Check if database is connected."""
        return self.db_handler is not None
    
    def trigger_delayed_search(self):
        """Trigger search after a delay."""
        self.search_timer.start(AppConfig.Database.SEARCH_DELAY)
    
    def perform_search(self):
        """Perform database search with current parameters."""
        if not self.is_connected():
            return
            
        try:
            # Get search parameters from UI
            params = self._get_search_parameters()
            
            # Perform search
            results = self.db_handler.filter_tests(params)
            
            # Update UI table
            if hasattr(self.parent_window, 'ui') and hasattr(self.parent_window.ui, 'results_table_2'):
                if results:
                    self._update_results_table(results)
                else:
                    self.parent_window.ui.results_table_2.setRowCount(0)
                    
        except Exception as e:
            QMessageBox.critical(
                self.parent_window, 
                "Error", 
                f"Error in search: {str(e)}"
            )
    
    def _get_search_parameters(self) -> Dict[str, Any]:
        """Get search parameters from UI."""
        params = {}
        
        if not hasattr(self.parent_window, 'ui'):
            return params
            
        ui = self.parent_window.ui
        
        # Test number
        test_no = ui.test_no.text().strip() if hasattr(ui, 'test_no') else ""
        if test_no:
            params['testNo'] = test_no
        
        # Catalyst name
        catalyst_name = ui.catalyst_name.text().strip() if hasattr(ui, 'catalyst_name') else ""
        if catalyst_name:
            params['catalystName'] = catalyst_name
        
        # Propellant concentration
        if hasattr(ui, 'propellant_ri'):
            prop_conc = ui.propellant_ri.value()
            if prop_conc > 0:
                params['propellantConc'] = prop_conc
        
        # Tank temperature
        if hasattr(ui, 'tank_temp'):
            tank_temp = ui.tank_temp.value()
            if tank_temp > 0:
                params['tankTemp'] = tank_temp
        
        return params
    
    def _update_results_table(self, results: List[Dict]):
        """Update the results table with search results."""
        try:
            table = self.parent_window.ui.results_table_2
            table.setRowCount(len(results))

            for i, result in enumerate(results):
                # Format the numerical values
                prop_conc = f"{result['propellant_conc']:.2f}" if result['propellant_conc'] else ""

                table.setItem(i, 0, QTableWidgetItem(str(result['test_id'])))
                table.setItem(i, 1, QTableWidgetItem(str(result['test_no'])))
                table.setItem(i, 2, QTableWidgetItem(str(result['test_date'])))
                table.setItem(i, 3, QTableWidgetItem(str(result['catalyst_name'])))
                table.setItem(i, 4, QTableWidgetItem(prop_conc))

            table.resizeColumnsToContents()

        except Exception as e:
            QMessageBox.critical(
                self.parent_window, 
                "Error", 
                f"Error updating table: {str(e)}"
            )
            traceback.print_exc()
    
    def load_all_records(self):
        """Load all records into the table."""
        if not self.is_connected():
            return
            
        try:
            results = self.db_handler.filter_tests({})  # Empty params to get all records
            self._update_results_table(results)
        except Exception as e:
            QMessageBox.critical(
                self.parent_window, 
                "Error", 
                f"Error loading records: {str(e)}"
            )
    
    def load_selected_test(self):
        """Load the selected test data into the database viewer."""
        if not hasattr(self.parent_window, 'ui'):
            return
            
        # Check if a row is selected
        selected_rows = self.parent_window.ui.results_table_2.selectedItems()
        if not selected_rows:
            QMessageBox.warning(
                self.parent_window, 
                "Warning", 
                "Please select a test to load."
            )
            return

        # Get the test number from the selected row
        try:
            test_no = self.parent_window.ui.results_table_2.item(selected_rows[0].row(), 1).text()
        except (IndexError, AttributeError):
            QMessageBox.warning(
                self.parent_window, 
                "Error", 
                "Failed to retrieve test number."
            )
            return

        # Setup database viewer layout
        viewer = self.parent_window.ui.databaseViewer
        if viewer.layout() is None:
            from PySide6.QtWidgets import QVBoxLayout
            layout = QVBoxLayout(viewer)
        else:
            layout = viewer.layout()

        # Clear existing widgets
        while layout.count():
            item = layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        # Create and add the new DataVisualizationWidget
        data_visualization = DataVisualizationWidget(test_no, viewer)
        layout.addWidget(data_visualization)
