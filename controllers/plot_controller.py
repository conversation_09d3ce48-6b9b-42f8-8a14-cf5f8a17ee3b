"""
Plot Controller
Handles plotting operations and plot management.
"""

import logging
from typing import Optional, List, Dict, Any
from PySide6.QtCore import QObject
from PySide6.QtWidgets import QMessageBox

from config.app_config import AppConfig


class PlotController(QObject):
    """Controller for plot operations and management."""
    
    def __init__(self, main_window):
        super().__init__(main_window)
        self.main_window = main_window
        self.ui = main_window.ui
        self.logger = logging.getLogger(__name__)
        
        # Plot state tracking
        self.current_plot_type = None
        self.current_figure = None
        self.current_canvas = None
        self.current_toolbar = None
        
        # Plot containers
        self.figure = None
        self.axes = None
        self.canvas = None
        
        # Selected range plot containers
        self.figure_selected = None
        self.axes_selected = None
        self.canvas_selected = None
        
        # Tab widget for pressure plots
        self.tab_widget = None
        self.current_tab_name = ""
        
        self._setup_plot_initialization()
    
    def _setup_plot_initialization(self):
        """Setup initial plot configuration."""
        # Hide pressure plot button frame initially
        if hasattr(self.ui, 'pressurePlotBtnFrame'):
            self.ui.pressurePlotBtnFrame.hide()
        
        # Setup temperature selection widget connections
        if hasattr(self.main_window, 'temp_selection_widget'):
            temp_widget = self.main_window.temp_selection_widget
            
            # Connect selection change signal
            if hasattr(temp_widget, 'dataSelectionChanged'):
                temp_widget.dataSelectionChanged.connect(self.update_temperature_plot)
            
            # Connect order change signal
            if hasattr(temp_widget, 'orderChanged'):
                temp_widget.orderChanged.connect(self.handle_temperature_order_changed)
    
    def setup_plot_controls(self):
        """Setup plot control UI elements."""
        try:
            # Show plot controls frame
            if hasattr(self.ui, 'plotControlsFrame'):
                self.ui.plotControlsFrame.show()
            
            # Setup combo boxes for plot axes
            self._setup_plot_combo_boxes()
            
            self.logger.info("Plot controls setup completed")
            
        except Exception as e:
            self.logger.error(f"Error setting up plot controls: {str(e)}")
    
    def _setup_plot_combo_boxes(self):
        """Setup combo boxes for plot axis selection."""
        # Setup temperature plot combo boxes
        if hasattr(self.main_window, 'data_service') and self.main_window.data_service.temperature_data is not None:
            temp_columns = [col for col in self.main_window.data_service.temperature_data.columns 
                           if col not in ['time', 'Time']]
            
            if hasattr(self.ui, 'comboBoxYAxisTemp'):
                self.ui.comboBoxYAxisTemp.clear()
                self.ui.comboBoxYAxisTemp.addItems(temp_columns)
        
        # Setup pressure plot combo boxes
        if hasattr(self.main_window, 'data_service') and self.main_window.data_service.pressure_data is not None:
            pressure_columns = [col for col in self.main_window.data_service.pressure_data.columns 
                               if col not in ['time', 'Time']]
            
            if hasattr(self.ui, 'comboBoxYAxisPressure'):
                self.ui.comboBoxYAxisPressure.clear()
                self.ui.comboBoxYAxisPressure.addItems(pressure_columns)
    
    def create_temperature_plot(self):
        """Create temperature plot."""
        try:
            self.logger.info("Creating temperature plot")
            
            # Check if temperature data is available
            if not self._check_temperature_data_available():
                return
            
            # Get selected columns
            selected_columns = self._get_selected_temperature_columns()
            if not selected_columns:
                QMessageBox.warning(
                    self.main_window,
                    "No Selection",
                    "Please select at least one temperature column to plot."
                )
                return
            
            # Create the plot
            self._create_plot('temperature', selected_columns)
            
            self.logger.info("Temperature plot created successfully")
            
        except Exception as e:
            self.logger.error(f"Error creating temperature plot: {str(e)}")
            QMessageBox.critical(
                self.main_window,
                "Plot Error",
                f"Failed to create temperature plot: {str(e)}"
            )
    
    def create_pressure_plot(self):
        """Create pressure plot."""
        try:
            self.logger.info("Creating pressure plot")
            
            # Check if pressure data is available
            if not self._check_pressure_data_available():
                return
            
            # Get selected columns
            selected_columns = self._get_selected_pressure_columns()
            if not selected_columns:
                QMessageBox.warning(
                    self.main_window,
                    "No Selection",
                    "Please select at least one pressure column to plot."
                )
                return
            
            # Create the plot
            self._create_plot('pressure', selected_columns)
            
            self.logger.info("Pressure plot created successfully")
            
        except Exception as e:
            self.logger.error(f"Error creating pressure plot: {str(e)}")
            QMessageBox.critical(
                self.main_window,
                "Plot Error",
                f"Failed to create pressure plot: {str(e)}"
            )
    
    def _create_plot(self, plot_type: str, columns: List[str]):
        """Create plot with specified type and columns."""
        # This is a simplified implementation
        # In the actual implementation, this would use matplotlib to create plots
        
        self.current_plot_type = plot_type
        
        # Get data based on plot type
        if plot_type == 'temperature':
            data = self.main_window.data_service.temperature_data
        elif plot_type == 'pressure':
            data = self.main_window.data_service.pressure_data
        else:
            raise ValueError(f"Unknown plot type: {plot_type}")
        
        # Create matplotlib figure and plot
        # This would be implemented with actual matplotlib code
        self.logger.info(f"Creating {plot_type} plot with columns: {columns}")
    
    def update_temperature_plot(self):
        """Update temperature plot with current selection."""
        if self.current_plot_type == 'temperature':
            self.create_temperature_plot()
    
    def handle_temperature_order_changed(self, new_order: List[str]):
        """Handle temperature column reordering."""
        self.logger.info(f"Temperature column order changed: {new_order}")
        self.update_temperature_plot()
    
    def plot_with_title(self):
        """Update plot title."""
        try:
            title = self.ui.lnEditPlotTitle.text() if hasattr(self.ui, 'lnEditPlotTitle') else ""
            
            if not title:
                return
            
            # Update plot title based on current plot
            current_plot = self.ui.lblCurentSection.text()
            
            if current_plot == 'Pressure Plot':
                self._update_pressure_plot_title(title)
            else:
                self._update_temperature_plot_title(title)
                
        except Exception as e:
            self.logger.error(f"Error updating plot title: {str(e)}")
    
    def _update_pressure_plot_title(self, title: str):
        """Update pressure plot title."""
        # Implementation for updating pressure plot title
        pass
    
    def _update_temperature_plot_title(self, title: str):
        """Update temperature plot title."""
        # Implementation for updating temperature plot title
        pass
    
    def handle_plot_inclusion(self):
        """Handle plot inclusion in report."""
        try:
            # Add current plot to report plots
            if hasattr(self.main_window, 'data_service'):
                plot_info = {
                    'type': self.current_plot_type,
                    'title': self.ui.lnEditPlotTitle.text() if hasattr(self.ui, 'lnEditPlotTitle') else "",
                    'path': None  # Would be set when plot is saved
                }
                
                self.main_window.data_service.report_plots['custom'].append(plot_info)
                
                self.logger.info("Plot included in report")
                
        except Exception as e:
            self.logger.error(f"Error including plot in report: {str(e)}")
    
    def generate_max_temperatures_plot(self):
        """Generate maximum temperatures plot."""
        try:
            self.logger.info("Generating maximum temperatures plot")
            
            # Check if temperature analysis data is available
            if not hasattr(self.main_window, 'temp_analyzer'):
                QMessageBox.warning(
                    self.main_window,
                    "No Data",
                    "Temperature analysis data not available."
                )
                return
            
            # Generate the plot
            # Implementation would use the temperature analyzer
            
            self.logger.info("Maximum temperatures plot generated")
            
        except Exception as e:
            self.logger.error(f"Error generating max temperatures plot: {str(e)}")
            QMessageBox.critical(
                self.main_window,
                "Plot Error",
                f"Failed to generate maximum temperatures plot: {str(e)}"
            )
    
    def handle_temp_matrix_clicked(self):
        """Handle temperature matrix button click."""
        try:
            # Switch to temperature matrix view
            if hasattr(self.ui, 'temperaturematrix'):
                self.ui.contentStack.setCurrentWidget(self.ui.temperaturematrix)
                self.ui.lblCurentSection.setText('Temperature Matrix')
                
        except Exception as e:
            self.logger.error(f"Error handling temp matrix click: {str(e)}")
    
    def _check_temperature_data_available(self) -> bool:
        """Check if temperature data is available."""
        if not hasattr(self.main_window, 'data_service'):
            return False
        return self.main_window.data_service.has_valid_temperature_data()
    
    def _check_pressure_data_available(self) -> bool:
        """Check if pressure data is available."""
        if not hasattr(self.main_window, 'data_service'):
            return False
        return self.main_window.data_service.has_valid_pressure_data()
    
    def _get_selected_temperature_columns(self) -> List[str]:
        """Get selected temperature columns from UI."""
        # Implementation would get selected columns from combo box or selection widget
        if hasattr(self.ui, 'comboBoxYAxisTemp'):
            # This is simplified - actual implementation would handle multi-selection
            return [self.ui.comboBoxYAxisTemp.currentText()]
        return []
    
    def _get_selected_pressure_columns(self) -> List[str]:
        """Get selected pressure columns from UI."""
        # Implementation would get selected columns from combo box or selection widget
        if hasattr(self.ui, 'comboBoxYAxisPressure'):
            # This is simplified - actual implementation would handle multi-selection
            return [self.ui.comboBoxYAxisPressure.currentText()]
        return []
