#!/usr/bin/env python3
"""
Script to clear any cached color assignments and verify the color palette
"""

from color_manager import color_manager

def clear_and_verify():
    """Clear color cache and verify the expected color assignments"""
    print("=" * 60)
    print("CLEARING COLOR CACHE AND VERIFYING ASSIGNMENTS")
    print("=" * 60)
    
    # Clear all existing assignments
    color_manager.clear_assignments()
    
    # Your exact column names from the screenshot
    columns = [
        "Tank Bottom (°C)",
        "Tank Flange (°C)", 
        "Thruster ka Flange (°C)",
        "Nozzle ki Convergent (°C)",
        "Nozzle ki Exit (°C)",
        "Tank Mid (°C)",
        "Tank ka Lid (°C)",
        "Thruster ka Chamber (°C)"
    ]
    
    print("\nExpected color assignments after restart:")
    print("-" * 50)
    
    expected_colors = {}
    for i, column in enumerate(columns):
        color = color_manager.get_color(column)
        expected_colors[column] = color
        print(f"{i+1}. {column}")
        print(f"   Color: {color}")
        print()
    
    print("Color palette being used:")
    print("-" * 30)
    for i, color in enumerate(color_manager.color_palette[:len(columns)]):
        print(f"Index {i}: {color}")
    
    print("\n" + "=" * 60)
    print("INSTRUCTIONS FOR FIXING THE DUPLICATE ORANGE ISSUE")
    print("=" * 60)
    
    print("1. COMPLETELY CLOSE your VAPR-iDEX application")
    print("2. Make sure no Python processes are running")
    print("3. Restart the application")
    print("4. Load your data file")
    print()
    print("Expected results after restart:")
    print("- Tank Bottom (°C): #01a5e3 (Bright blue)")
    print("- Tank Flange (°C): #32c133 (Bright green)")  
    print("- Thruster ka Flange (°C): #fb6238 (Bright orange)")
    print("- Nozzle ki Convergent (°C): #ee0100 (Bright red)")
    print("- Nozzle ki Exit (°C): #f7d02e (Bright yellow)")
    print("- Tank Mid (°C): #9467bd (Bright purple)")
    print("- Tank ka Lid (°C): #17becf (Bright cyan)")
    print("- Thruster ka Chamber (°C): #ff7f0e (Safety orange - different from fb6238)")
    print()
    print("If you still see duplicate colors after restart:")
    print("- Check the console output for 'ColorManager: Assigned' messages")
    print("- Each column should get a different index (0, 1, 2, 3, 4, 5, 6, 7)")
    print("- If you see the same index assigned twice, there's a bug")
    
    return expected_colors

if __name__ == "__main__":
    clear_and_verify()
