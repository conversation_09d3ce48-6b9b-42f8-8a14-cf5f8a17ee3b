#!/usr/bin/env python3
"""
Diagnostic script to help identify actual data line colors vs background colors
"""

from color_manager import color_manager

def diagnose_color_sources():
    """Diagnose where different colors are coming from"""
    print("=" * 70)
    print("COLOR SOURCE DIAGNOSTIC")
    print("=" * 70)
    
    # Our defined color palette
    palette_colors = [
        '#01a5e3',  # Bright blue
        '#32c133',  # Bright green
        '#fb6238',  # Bright orange
        '#ee0100',  # Bright red
        '#f7d02e',  # Bright yellow
        '#ff7f0e',  # Safety orange
        '#2ca02c',  # Bright green variant
        '#d62728',  # Bright red variant
        '#9467bd',  # Bright purple
        '#17becf',  # <PERSON> cyan
        '#1f77b4',  # Medium blue
        '#bcbd22',  # Olive green
        '#8c564b',  # Brown
        '#e377c2',  # Pink
        '#7f7f7f',  # Gray
        '#9d004e',  # Ma<PERSON>a (darker but still visible)
        '#165152'   # Teal (darker but still visible)
    ]
    
    # Known background/UI colors
    background_colors = {
        '#fcf1ff': 'Plot widget background (light purple)',
        '#1E1E1E': 'Dark theme background',
        '#E0E0E0': 'Text color',
        '#404040': 'Grid color',
        '#666666': 'Axis color',
        '#000000': 'Black (should not appear in data lines)',
        '#FFFFFF': 'White (should not appear in data lines)',
    }
    
    print("\n1. VALID DATA LINE COLORS (from our palette):")
    print("-" * 50)
    for i, color in enumerate(palette_colors):
        print(f"  {i+1:2d}. {color} - Valid data line color")
    
    print("\n2. BACKGROUND/UI COLORS (NOT for data lines):")
    print("-" * 50)
    for color, description in background_colors.items():
        print(f"  {color} - {description}")
    
    print("\n3. COLOR ANALYSIS:")
    print("-" * 50)
    
    # Test colors you mentioned
    test_colors = ['#fb6238', '#fcf1ff']
    
    for color in test_colors:
        print(f"\nAnalyzing color: {color}")
        
        if color in palette_colors:
            index = palette_colors.index(color) + 1
            print(f"  ✅ VALID: This is color #{index} from our data line palette")
            print(f"  ✅ This should be used for data lines")
        elif color in background_colors:
            print(f"  ❌ BACKGROUND COLOR: {background_colors[color]}")
            print(f"  ❌ This should NOT be used for data lines")
        else:
            print(f"  ⚠️  UNKNOWN: This color is not in our defined palettes")
            print(f"  ⚠️  This might be coming from matplotlib defaults or other sources")
    
    print("\n4. HOW TO IDENTIFY DATA LINE COLORS IN YOUR PLOT:")
    print("-" * 50)
    print("  • Look at the LEGEND - colors shown there are data line colors")
    print("  • Look at the actual LINES in the plot - those are data line colors")
    print("  • Ignore the background color of the plot area")
    print("  • Ignore the background color of the widget/container")
    
    print("\n5. EXPECTED DATA LINE COLORS FOR YOUR COLUMNS:")
    print("-" * 50)
    
    # Clear and get fresh assignments
    color_manager.clear_assignments()
    
    test_columns = [
        "Tank Bottom (°C)",
        "Tank Flange (°C)", 
        "Thruster ka Flange (°C)",
        "Nozzle ki Convergent (°C)",
        "Nozzle ki Exit (°C)",
        "Tank Mid (°C)",
        "Tank ka Lid (°C)",
        "Thruster ka Chamber (°C)"
    ]
    
    print("  After restart, your data lines should have these colors:")
    for column in test_columns:
        color = color_manager.get_color(column)
        print(f"    {column}: {color}")
    
    print("\n6. TROUBLESHOOTING:")
    print("-" * 50)
    print("  If you see colors NOT in the palette above:")
    print("  • Check if it's a background color (like #fcf1ff)")
    print("  • Look for any old cached color assignments")
    print("  • Make sure you've restarted the application completely")
    print("  • Check the console output for color assignment messages")
    
    print("\n" + "=" * 70)
    print("SUMMARY")
    print("=" * 70)
    print("✅ #fb6238 - CORRECT! This is bright orange from our palette")
    print("❌ #fcf1ff - This is a background color, NOT a data line color")
    print()
    print("Focus on the colors in the LEGEND and the actual LINES in your plot.")
    print("The background colors are just for the UI and should be ignored.")

if __name__ == "__main__":
    diagnose_color_sources()
