from PySide6.QtWidgets import QDoubleSpinBox
from PySide6.QtGui import QValidator

class CustomDoubleSpinBox(QDoubleSpinBox):
    def textFromValue(self, value):
        # Convert the value to string with maximum precision
        text = f"{value:.6f}"
        # Remove trailing zeros after decimal point
        text = text.rstrip('0')
        # Remove decimal point if it's the last character
        text = text.rstrip('.')
        return text

    def validate(self, text, pos):
        # Ensure the text follows the format we want
        try:
            if text:
                float(text)
            return QValidator.State.Acceptable, text, pos
        except ValueError:
            return QValidator.State.Invalid, text, pos