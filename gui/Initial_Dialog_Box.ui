<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>dialogBoxWidget</class>
 <widget class="QWidget" name="dialogBoxWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>150</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>400</width>
    <height>150</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>400</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>TestAnalysis</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color:transparent;
border-radius: 5px;</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <widget class="QFrame" name="buttonFrame">
     <property name="styleSheet">
      <string notr="true">QFrame{
background-color:#1e1e1e;
border-radius:10px;
border:1px solid #446699;
}

QPushButton{
border-radius: 30px;
color:black;
font-size:15px;
font-family:Arial;
}

Line{
background-color: #333333;
border:none;
}</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <property name="horizontalSpacing">
       <number>30</number>
      </property>
      <item row="0" column="2">
       <widget class="QPushButton" name="btnExistingTest">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>60</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>60</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton{
	background-color: rgb(239, 208, 255);
}

QPushButton:hover{
	background-color:#bfa6cc;
	border: 2px solid red;
}

QPushButton:pressed{
	background-color:#fbfbda;
	border: 2px solid green;
}</string>
        </property>
        <property name="text">
         <string>Existing Test</string>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QPushButton" name="btnNewTest">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>60</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>60</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton{
	background-color: rgb(207, 149, 255);
}

QPushButton:hover{
	background-color:#9068b2;
	border: 2px solid red;
}

QPushButton:pressed{
	background-color:#fbfbda;
	border: 2px solid green;
}</string>
        </property>
        <property name="text">
         <string>New Test</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="Line" name="line">
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="orientation">
         <enum>Qt::Orientation::Vertical</enum>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
