from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure


class PlotCanvas(FigureCanvas):
    def __init__(self, figure=None):
        if figure is None:
            figure = Figure(figsize=(4, 2), dpi=300)

        self.figure = figure
        super().__init__(self.figure)

        # Set a reasonable minimum size
        self.setMinimumSize(860, 650)

        # Adjust the subplot parameters to give specified padding
        self.figure.subplots_adjust(left=0.15,
                                    right=0.95,
                                    bottom=0.15,
                                    top=0.9,
                                    wspace=0.2,
                                    hspace=0.2)

    def resizeEvent(self, event):
        """Handle resize events to maintain plot appearance"""
        super().resizeEvent(event)
        self.figure.tight_layout()