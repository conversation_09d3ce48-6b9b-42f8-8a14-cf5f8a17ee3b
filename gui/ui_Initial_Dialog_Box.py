# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'Initial_Dialog_BoxGCKkiG.ui'
##
## Created by: Qt User Interface Compiler version 6.8.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, QConicalGradient, QCursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QFrame, QGridLayout, QPushButton,
    QSizePolicy, QWidget)

class Ui_dialogBoxWidget(object):
    def setupUi(self, dialogBoxWidget):
        if not dialogBoxWidget.objectName():
            dialogBoxWidget.setObjectName(u"dialogBoxWidget")
        dialogBoxWidget.resize(400, 150)
        dialogBoxWidget.setMinimumSize(QSize(400, 150))
        dialogBoxWidget.setMaximumSize(QSize(400, 16777215))
        dialogBoxWidget.setStyleSheet(u"QWidget{\n"
"background-color:#1e1e1e;\n"
"border-radius:10px;\n"
"border:1px solid #446699;\n"
"}")
        self.gridLayout = QGridLayout(dialogBoxWidget)
        self.gridLayout.setObjectName(u"gridLayout")
        self.buttonFrame = QFrame(dialogBoxWidget)
        self.buttonFrame.setObjectName(u"buttonFrame")
        self.buttonFrame.setStyleSheet(u"QFrame{\n"
"background-color:#1e1e1e;\n"
"border-radius:10px;\n"
"border:1px solid #446699;\n"
"}\n"
"\n"
"QPushButton{\n"
"background-color: rgb(207, 149, 255);\n"
"border-radius: 30px;\n"
"color:black;\n"
"font-size:15px;\n"
"}\n"
"\n"
"Line{\n"
"background-color: #333333;\n"
"}")
        self.buttonFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.buttonFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.gridLayout_2 = QGridLayout(self.buttonFrame)
        self.gridLayout_2.setObjectName(u"gridLayout_2")
        self.gridLayout_2.setHorizontalSpacing(30)
        self.btnExistingTest = QPushButton(self.buttonFrame)
        self.btnExistingTest.setObjectName(u"btnExistingTest")
        self.btnExistingTest.setMinimumSize(QSize(0, 60))
        self.btnExistingTest.setMaximumSize(QSize(16777215, 60))
        self.btnExistingTest.setStyleSheet(u"background-color: rgb(239, 208, 255);")

        self.gridLayout_2.addWidget(self.btnExistingTest, 0, 2, 1, 1)

        self.btnNewTest = QPushButton(self.buttonFrame)
        self.btnNewTest.setObjectName(u"btnNewTest")
        self.btnNewTest.setMinimumSize(QSize(0, 60))
        self.btnNewTest.setMaximumSize(QSize(16777215, 60))
        self.btnNewTest.setStyleSheet(u"")

        self.gridLayout_2.addWidget(self.btnNewTest, 0, 0, 1, 1)

        self.line = QFrame(self.buttonFrame)
        self.line.setObjectName(u"line")
        self.line.setStyleSheet(u"")
        self.line.setFrameShape(QFrame.Shape.VLine)
        self.line.setFrameShadow(QFrame.Shadow.Sunken)

        self.gridLayout_2.addWidget(self.line, 0, 1, 1, 1)


        self.gridLayout.addWidget(self.buttonFrame, 0, 0, 1, 1)


        self.retranslateUi(dialogBoxWidget)

        QMetaObject.connectSlotsByName(dialogBoxWidget)
    # setupUi

    def retranslateUi(self, dialogBoxWidget):
        dialogBoxWidget.setWindowTitle(QCoreApplication.translate("dialogBoxWidget", u"TestAnalysis", None))
        self.btnExistingTest.setText(QCoreApplication.translate("dialogBoxWidget", u"Existing Test", None))
        self.btnNewTest.setText(QCoreApplication.translate("dialogBoxWidget", u"New Test", None))
    # retranslateUi

