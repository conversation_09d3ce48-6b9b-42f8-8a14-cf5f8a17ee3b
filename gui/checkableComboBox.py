from PySide6.QtWidgets import QComboBox, QStyledItemDelegate
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QStandardItem, QStandardItemModel


class CheckableComboBox(QComboBox):
    itemCheckStateChanged = Signal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.view().pressed.connect(self.handle_item_pressed)
        self.model = QStandardItemModel()
        self.setModel(self.model)
        self.setEditable(True)
        self.lineEdit().setReadOnly(True)
        self.lineEdit().setPlaceholderText("Select items")
        self.setItemDelegate(QStyledItemDelegate())

        # Connect signals
        self.model.dataChanged.connect(self.on_data_changed)

        # Variables to store state
        self.items_checked = []

    def handle_item_pressed(self, index):
        item = self.model.itemFromIndex(index)
        if item.checkState() == Qt.Checked:
            item.setCheckState(Qt.Unchecked)
        else:
            item.setCheckState(Qt.Checked)

    def on_data_changed(self, topLeft, bottomRight, roles):
        """Handle data changes in the model"""
        self.updateText()
        self.itemCheckStateChanged.emit()

    def addItems(self, items):
        """Add checkable items to the combo box"""
        for text in items:
            item = QStandardItem(text)
            item.setCheckState(Qt.CheckState.Unchecked)
            item.setFlags(Qt.ItemFlag.ItemIsUserCheckable | Qt.ItemFlag.ItemIsEnabled)
            self.model.appendRow(item)

    def updateText(self):
        """Update the text shown in the combo box"""
        self.items_checked = []
        for i in range(self.model.rowCount()):
            item = self.model.item(i)
            if item.checkState() == Qt.CheckState.Checked:
                self.items_checked.append(item.text())

        text = ", ".join(self.items_checked) if self.items_checked else "Select items"
        self.setEditText(text)

    def getCheckedItems(self):
        checked_items = []
        for i in range(self.model.rowCount()):
            item = self.model.item(i)
            if item.checkState() == Qt.Checked:
                checked_items.append(item.text())
        return checked_items

    def wheelEvent(self, event):
        # Ignore the wheel event to prevent changing the selection
        event.ignore()