# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'VAPR-iDEX Thruster AnalysisQXZrtY.ui'
##
## Created by: Qt User Interface Compiler version 6.8.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, QConicalGradient, QCursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QAbstractItemView, QAbstractScrollArea, QAbstractSpinBox, QApplication,
    QDateEdit, QDateTimeEdit, QDoubleSpinBox, QFrame,
    QGridLayout, QHBoxLayout, QHeaderView, QLabel,
    QLineEdit, QMainWindow, QPlainTextEdit, QPushButton,
    QScrollArea, QSizePolicy, QSpacerItem, QSpinBox,
    QStackedWidget, QTabWidget, QTableWidget, QTableWidgetItem,
    QTimeEdit, QVBoxLayout, QWidget)

from .checkableComboBox import CheckableComboBox
from .custom_double_spin_box import CustomDoubleSpinBox

class Ui_VaprIdexMainWindow(object):
    def setupUi(self, VaprIdexMainWindow):
        if not VaprIdexMainWindow.objectName():
            VaprIdexMainWindow.setObjectName(u"VaprIdexMainWindow")
        VaprIdexMainWindow.resize(1483, 770)
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(VaprIdexMainWindow.sizePolicy().hasHeightForWidth())
        VaprIdexMainWindow.setSizePolicy(sizePolicy)
        VaprIdexMainWindow.setMinimumSize(QSize(250, 250))
        VaprIdexMainWindow.setStyleSheet(u"background-color: #09090b;\n"
"color: white;")
        self.centralwidget = QWidget(VaprIdexMainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        sizePolicy.setHeightForWidth(self.centralwidget.sizePolicy().hasHeightForWidth())
        self.centralwidget.setSizePolicy(sizePolicy)
        self.centralwidget.setMinimumSize(QSize(0, 0))
        self.centralwidget.setMaximumSize(QSize(16777215, 16777215))
        font = QFont()
        font.setStyleStrategy(QFont.PreferAntialias)
        self.centralwidget.setFont(font)
        self.verticalLayout_85 = QVBoxLayout(self.centralwidget)
        self.verticalLayout_85.setObjectName(u"verticalLayout_85")
        self.topFrame = QFrame(self.centralwidget)
        self.topFrame.setObjectName(u"topFrame")
        self.topFrame.setMinimumSize(QSize(0, 50))
        self.topFrame.setMaximumSize(QSize(16777215, 50))
        self.topFrame.setStyleSheet(u"")
        self.topFrame.setFrameShape(QFrame.Shape.NoFrame)
        self.topFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.gridLayout_39 = QGridLayout(self.topFrame)
        self.gridLayout_39.setSpacing(0)
        self.gridLayout_39.setObjectName(u"gridLayout_39")
        self.gridLayout_39.setContentsMargins(5, 0, 5, 0)
        self.topBarFrame = QFrame(self.topFrame)
        self.topBarFrame.setObjectName(u"topBarFrame")
        self.topBarFrame.setMinimumSize(QSize(500, 45))
        self.topBarFrame.setMaximumSize(QSize(16777215, 45))
        self.topBarFrame.setStyleSheet(u"QFrame{\n"
"	background-color:#18181b;\n"
"	border-radius:10px;\n"
"}\n"
"\n"
"QLabel{\n"
"	background-color: #374151;\n"
"	border-radius:15px;\n"
"	padding:5px;\n"
"	font-size:14px;\n"
"	color:#94a3b8;\n"
"}")
        self.topBarFrame.setFrameShape(QFrame.Shape.NoFrame)
        self.topBarFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_2 = QHBoxLayout(self.topBarFrame)
        self.horizontalLayout_2.setSpacing(10)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.horizontalLayout_2.setContentsMargins(9, 0, 9, 0)
        self.mainHeader = QFrame(self.topBarFrame)
        self.mainHeader.setObjectName(u"mainHeader")
        self.mainHeader.setMinimumSize(QSize(250, 30))
        self.mainHeader.setMaximumSize(QSize(265, 30))
        self.mainHeader.setStyleSheet(u"background-color: rgb(92, 92, 92);\n"
"border-radius:15px;\n"
"")
        self.mainHeader.setFrameShape(QFrame.Shape.StyledPanel)
        self.mainHeader.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_3 = QHBoxLayout(self.mainHeader)
        self.horizontalLayout_3.setSpacing(0)
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.horizontalLayout_3.setContentsMargins(5, 0, 0, 0)
        self.projectName = QLabel(self.mainHeader)
        self.projectName.setObjectName(u"projectName")
        self.projectName.setMinimumSize(QSize(0, 24))
        self.projectName.setMaximumSize(QSize(90, 24))
        font1 = QFont()
        font1.setBold(True)
        self.projectName.setFont(font1)
        self.projectName.setStyleSheet(u"background-color: #474747;\n"
"border-radius: 10px;\n"
"padding:5px;\n"
"color:white;\n"
"")

        self.horizontalLayout_3.addWidget(self.projectName)

        self.lblCurentSection = QLabel(self.mainHeader)
        self.lblCurentSection.setObjectName(u"lblCurentSection")
        self.lblCurentSection.setStyleSheet(u"color:white;\n"
"font-size:14px;")
        self.lblCurentSection.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_3.addWidget(self.lblCurentSection)


        self.horizontalLayout_2.addWidget(self.mainHeader)

        self.lblAim = QLabel(self.topBarFrame)
        self.lblAim.setObjectName(u"lblAim")
        self.lblAim.setMinimumSize(QSize(500, 30))
        self.lblAim.setMaximumSize(QSize(16777215, 30))
        font2 = QFont()
        font2.setFamilies([u"Arial"])
        font2.setBold(True)
        self.lblAim.setFont(font2)
        self.lblAim.setStyleSheet(u"")
        self.lblAim.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_2.addWidget(self.lblAim)

        self.lblPropellant = QLabel(self.topBarFrame)
        self.lblPropellant.setObjectName(u"lblPropellant")
        self.lblPropellant.setMinimumSize(QSize(150, 30))
        self.lblPropellant.setMaximumSize(QSize(200, 30))
        self.lblPropellant.setFont(font2)
        self.lblPropellant.setStyleSheet(u"")
        self.lblPropellant.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_2.addWidget(self.lblPropellant)

        self.lblCatalyst = QLabel(self.topBarFrame)
        self.lblCatalyst.setObjectName(u"lblCatalyst")
        self.lblCatalyst.setMinimumSize(QSize(180, 30))
        self.lblCatalyst.setMaximumSize(QSize(250, 30))
        self.lblCatalyst.setFont(font2)
        self.lblCatalyst.setStyleSheet(u"")
        self.lblCatalyst.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_2.addWidget(self.lblCatalyst)

        self.testNoFrame = QFrame(self.topBarFrame)
        self.testNoFrame.setObjectName(u"testNoFrame")
        self.testNoFrame.setMinimumSize(QSize(0, 30))
        self.testNoFrame.setMaximumSize(QSize(150, 30))
        self.testNoFrame.setStyleSheet(u"background-color: #374151;\n"
"border-radius:15px;\n"
"")
        self.testNoFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.testNoFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout = QHBoxLayout(self.testNoFrame)
        self.horizontalLayout.setSpacing(3)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalLayout.setContentsMargins(10, 0, 0, 0)
        self.label = QLabel(self.testNoFrame)
        self.label.setObjectName(u"label")
        self.label.setMinimumSize(QSize(68, 0))
        self.label.setMaximumSize(QSize(60, 16777215))
        self.label.setFont(font2)
        self.label.setStyleSheet(u"background-color: #374151;\n"
"	border-radius:15px;\n"
"	padding:5px;")
        self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout.addWidget(self.label)

        self.lblTestNumber = QLabel(self.testNoFrame)
        self.lblTestNumber.setObjectName(u"lblTestNumber")
        self.lblTestNumber.setMinimumSize(QSize(63, 0))
        self.lblTestNumber.setMaximumSize(QSize(16777215, 16777215))
        self.lblTestNumber.setFont(font2)
        self.lblTestNumber.setStyleSheet(u"")
        self.lblTestNumber.setAlignment(Qt.AlignmentFlag.AlignLeading|Qt.AlignmentFlag.AlignLeft|Qt.AlignmentFlag.AlignVCenter)

        self.horizontalLayout.addWidget(self.lblTestNumber)


        self.horizontalLayout_2.addWidget(self.testNoFrame)


        self.gridLayout_39.addWidget(self.topBarFrame, 0, 0, 1, 1)


        self.verticalLayout_85.addWidget(self.topFrame)

        self.tabWidget = QTabWidget(self.centralwidget)
        self.tabWidget.setObjectName(u"tabWidget")
        self.tabWidget.setFocusPolicy(Qt.FocusPolicy.NoFocus)
        self.tabWidget.setStyleSheet(u"border:none;\n"
"background-color:  #09090b;")
        self.mainTab = QWidget()
        self.mainTab.setObjectName(u"mainTab")
        self.gridLayout_28 = QGridLayout(self.mainTab)
        self.gridLayout_28.setObjectName(u"gridLayout_28")
        self.gridLayout_28.setContentsMargins(0, 0, 0, 0)
        self.centralWidgetScrollArea = QScrollArea(self.mainTab)
        self.centralWidgetScrollArea.setObjectName(u"centralWidgetScrollArea")
        self.centralWidgetScrollArea.setStyleSheet(u"QScrollArea {\n"
"    border: none; /* Remove border */\n"
"}\n"
"\n"
"QScrollArea > QWidget > QScrollBar:vertical {\n"
"    width: 0px;\n"
"}\n"
"\n"
"QScrollArea > QWidget > QScrollBar:horizontal {\n"
"    height: 0px;\n"
"}")
        self.centralWidgetScrollArea.setSizeAdjustPolicy(QAbstractScrollArea.SizeAdjustPolicy.AdjustToContentsOnFirstShow)
        self.centralWidgetScrollArea.setWidgetResizable(True)
        self.centralWidgetScrollArea.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.centralContentScrollArea = QWidget()
        self.centralContentScrollArea.setObjectName(u"centralContentScrollArea")
        self.centralContentScrollArea.setGeometry(QRect(0, 0, 1463, 658))
        sizePolicy1 = QSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Expanding)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.centralContentScrollArea.sizePolicy().hasHeightForWidth())
        self.centralContentScrollArea.setSizePolicy(sizePolicy1)
        self.centralContentScrollArea.setStyleSheet(u"")
        self.gridLayout_24 = QGridLayout(self.centralContentScrollArea)
        self.gridLayout_24.setSpacing(0)
        self.gridLayout_24.setObjectName(u"gridLayout_24")
        self.gridLayout_24.setContentsMargins(0, 0, 0, 0)
        self.centralFrame = QFrame(self.centralContentScrollArea)
        self.centralFrame.setObjectName(u"centralFrame")
        sizePolicy.setHeightForWidth(self.centralFrame.sizePolicy().hasHeightForWidth())
        self.centralFrame.setSizePolicy(sizePolicy)
        self.centralFrame.setFrameShape(QFrame.Shape.NoFrame)
        self.centralFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.gridLayout_35 = QGridLayout(self.centralFrame)
        self.gridLayout_35.setSpacing(0)
        self.gridLayout_35.setObjectName(u"gridLayout_35")
        self.gridLayout_35.setContentsMargins(0, 0, 0, 0)
        self.mainContentPanel = QFrame(self.centralFrame)
        self.mainContentPanel.setObjectName(u"mainContentPanel")
        self.mainContentPanel.setStyleSheet(u"")
        self.mainContentPanel.setFrameShape(QFrame.Shape.NoFrame)
        self.mainContentPanel.setFrameShadow(QFrame.Shadow.Plain)
        self.verticalLayout_3 = QVBoxLayout(self.mainContentPanel)
        self.verticalLayout_3.setSpacing(0)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.verticalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.contentStack = QStackedWidget(self.mainContentPanel)
        self.contentStack.setObjectName(u"contentStack")
        sizePolicy.setHeightForWidth(self.contentStack.sizePolicy().hasHeightForWidth())
        self.contentStack.setSizePolicy(sizePolicy)
        self.contentStack.setLayoutDirection(Qt.LayoutDirection.LeftToRight)
        self.contentStack.setStyleSheet(u"QWidget{\n"
"background-color: #171717;\n"
"border-radius:10px;\n"
"margin:5px;\n"
"}")
        self.plotDataLoad = QWidget()
        self.plotDataLoad.setObjectName(u"plotDataLoad")
        self.gridLayout_7 = QGridLayout(self.plotDataLoad)
        self.gridLayout_7.setObjectName(u"gridLayout_7")
        self.gridLayout_7.setContentsMargins(0, 0, 0, 0)
        self.contentFrameLoadData = QFrame(self.plotDataLoad)
        self.contentFrameLoadData.setObjectName(u"contentFrameLoadData")
        self.contentFrameLoadData.setMinimumSize(QSize(780, 400))
        self.contentFrameLoadData.setMaximumSize(QSize(1200, 400))
        self.contentFrameLoadData.setStyleSheet(u"background-color:transparent;\n"
"border-radius:30px;")
        self.contentFrameLoadData.setFrameShape(QFrame.Shape.NoFrame)
        self.contentFrameLoadData.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_64 = QVBoxLayout(self.contentFrameLoadData)
        self.verticalLayout_64.setObjectName(u"verticalLayout_64")
        self.verticalLayout_64.setContentsMargins(5, 5, 5, -1)
        self.frame_51 = QFrame(self.contentFrameLoadData)
        self.frame_51.setObjectName(u"frame_51")
        self.frame_51.setMinimumSize(QSize(0, 50))
        self.frame_51.setMaximumSize(QSize(16777215, 50))
        self.frame_51.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_51.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_18 = QHBoxLayout(self.frame_51)
        self.horizontalLayout_18.setSpacing(0)
        self.horizontalLayout_18.setObjectName(u"horizontalLayout_18")
        self.horizontalLayout_18.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_42 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_18.addItem(self.horizontalSpacer_42)

        self.subSecLoadData = QLabel(self.frame_51)
        self.subSecLoadData.setObjectName(u"subSecLoadData")
        self.subSecLoadData.setMinimumSize(QSize(180, 45))
        self.subSecLoadData.setMaximumSize(QSize(16777215, 16777215))
        font3 = QFont()
        font3.setFamilies([u"Arial"])
        font3.setPointSize(12)
        font3.setBold(True)
        self.subSecLoadData.setFont(font3)
        self.subSecLoadData.setStyleSheet(u"background-color: #252525;\n"
"border-radius: 17px;\n"
"padding:5px;\n"
"border: 2px solid red;\n"
"")
        self.subSecLoadData.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_18.addWidget(self.subSecLoadData)

        self.horizontalSpacer_55 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_18.addItem(self.horizontalSpacer_55)


        self.verticalLayout_64.addWidget(self.frame_51)

        self.subFrameLoadData = QFrame(self.contentFrameLoadData)
        self.subFrameLoadData.setObjectName(u"subFrameLoadData")
        self.subFrameLoadData.setMinimumSize(QSize(0, 80))
        self.subFrameLoadData.setStyleSheet(u"font-size:16px;\n"
"font-family: Arial;")
        self.subFrameLoadData.setFrameShape(QFrame.Shape.StyledPanel)
        self.subFrameLoadData.setFrameShadow(QFrame.Shadow.Raised)
        self.gridLayout_33 = QGridLayout(self.subFrameLoadData)
        self.gridLayout_33.setObjectName(u"gridLayout_33")
        self.gridLayout_33.setHorizontalSpacing(40)
        self.gridLayout_33.setContentsMargins(15, 15, 15, 15)
        self.widget_2 = QWidget(self.subFrameLoadData)
        self.widget_2.setObjectName(u"widget_2")
        self.widget_2.setStyleSheet(u"QWidget{\n"
"background-color:rgba(43, 58, 103, 0.7);\n"
"}")
        self.verticalLayout_72 = QVBoxLayout(self.widget_2)
        self.verticalLayout_72.setObjectName(u"verticalLayout_72")
        self.frame_63 = QFrame(self.widget_2)
        self.frame_63.setObjectName(u"frame_63")
        self.frame_63.setStyleSheet(u"background-color:transparent;")
        self.frame_63.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_63.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_50 = QHBoxLayout(self.frame_63)
        self.horizontalLayout_50.setSpacing(0)
        self.horizontalLayout_50.setObjectName(u"horizontalLayout_50")
        self.horizontalLayout_50.setContentsMargins(0, 0, 0, 0)
        self.pressure_icon = QPushButton(self.frame_63)
        self.pressure_icon.setObjectName(u"pressure_icon")
        self.pressure_icon.setMinimumSize(QSize(185, 185))
        self.pressure_icon.setMaximumSize(QSize(185, 185))
        self.pressure_icon.setStyleSheet(u"")
        icon = QIcon()
        icon.addFile(u"../../tempDevelop2/assets/pressure_icon.png", QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        self.pressure_icon.setIcon(icon)
        self.pressure_icon.setIconSize(QSize(200, 200))

        self.horizontalLayout_50.addWidget(self.pressure_icon)


        self.verticalLayout_72.addWidget(self.frame_63)

        self.frame_64 = QFrame(self.widget_2)
        self.frame_64.setObjectName(u"frame_64")
        self.frame_64.setStyleSheet(u"border-radius:8px;\n"
"background-color:transparent;")
        self.frame_64.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_64.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_51 = QHBoxLayout(self.frame_64)
        self.horizontalLayout_51.setSpacing(0)
        self.horizontalLayout_51.setObjectName(u"horizontalLayout_51")
        self.horizontalLayout_51.setContentsMargins(0, 0, 0, 0)
        self.btnPressureDataLoad = QPushButton(self.frame_64)
        self.btnPressureDataLoad.setObjectName(u"btnPressureDataLoad")
        self.btnPressureDataLoad.setMinimumSize(QSize(250, 50))
        self.btnPressureDataLoad.setMaximumSize(QSize(250, 50))
        self.btnPressureDataLoad.setStyleSheet(u"\n"
"QPushButton{\n"
"	background-color:rgba(29, 78, 216, 0.3);\n"
"	\n"
"	padding:5px;\n"
"\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	background-color:#7b92d4;\n"
"}\n"
"\n"
"QPushButton:pressed{\n"
"	background-color:black;\n"
"}")

        self.horizontalLayout_51.addWidget(self.btnPressureDataLoad)


        self.verticalLayout_72.addWidget(self.frame_64)


        self.gridLayout_33.addWidget(self.widget_2, 0, 1, 1, 1)

        self.widget = QWidget(self.subFrameLoadData)
        self.widget.setObjectName(u"widget")
        self.widget.setStyleSheet(u"QWidget{\n"
"background-color:rgb(12, 57, 36);\n"
"}")
        self.verticalLayout_71 = QVBoxLayout(self.widget)
        self.verticalLayout_71.setObjectName(u"verticalLayout_71")
        self.frame_58 = QFrame(self.widget)
        self.frame_58.setObjectName(u"frame_58")
        self.frame_58.setStyleSheet(u"background-color:transparent;")
        self.frame_58.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_58.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_48 = QHBoxLayout(self.frame_58)
        self.horizontalLayout_48.setSpacing(0)
        self.horizontalLayout_48.setObjectName(u"horizontalLayout_48")
        self.horizontalLayout_48.setContentsMargins(0, 0, 0, 0)
        self.temp_icon = QPushButton(self.frame_58)
        self.temp_icon.setObjectName(u"temp_icon")
        self.temp_icon.setMinimumSize(QSize(185, 185))
        self.temp_icon.setMaximumSize(QSize(185, 185))
        self.temp_icon.setStyleSheet(u"")
        icon1 = QIcon()
        icon1.addFile(u"../../tempDevelop2/assets/temperature_icon.png", QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        self.temp_icon.setIcon(icon1)
        self.temp_icon.setIconSize(QSize(180, 180))

        self.horizontalLayout_48.addWidget(self.temp_icon)


        self.verticalLayout_71.addWidget(self.frame_58)

        self.frame_62 = QFrame(self.widget)
        self.frame_62.setObjectName(u"frame_62")
        self.frame_62.setStyleSheet(u"border-radius:8px;\n"
"background-color:transparent;")
        self.frame_62.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_62.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_49 = QHBoxLayout(self.frame_62)
        self.horizontalLayout_49.setSpacing(0)
        self.horizontalLayout_49.setObjectName(u"horizontalLayout_49")
        self.horizontalLayout_49.setContentsMargins(0, 0, 0, 0)
        self.btnTempDataLoad = QPushButton(self.frame_62)
        self.btnTempDataLoad.setObjectName(u"btnTempDataLoad")
        self.btnTempDataLoad.setMinimumSize(QSize(250, 50))
        self.btnTempDataLoad.setMaximumSize(QSize(250, 50))
        self.btnTempDataLoad.setStyleSheet(u"QPushButton{\n"
"	background-color: rgba(4, 120, 87, 0.3);\n"
"	\n"
"	padding:5px;\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	background-color:#47a08e;\n"
"}\n"
"\n"
"QPushButton:pressed{\n"
"	background-color:black;\n"
"}")

        self.horizontalLayout_49.addWidget(self.btnTempDataLoad)


        self.verticalLayout_71.addWidget(self.frame_62)


        self.gridLayout_33.addWidget(self.widget, 0, 0, 1, 1)


        self.verticalLayout_64.addWidget(self.subFrameLoadData)


        self.gridLayout_7.addWidget(self.contentFrameLoadData, 0, 0, 1, 1)

        self.contentStack.addWidget(self.plotDataLoad)
        self.basicInformation = QWidget()
        self.basicInformation.setObjectName(u"basicInformation")
        self.gridLayout_2 = QGridLayout(self.basicInformation)
        self.gridLayout_2.setObjectName(u"gridLayout_2")
        self.gridLayout_2.setContentsMargins(0, 0, 0, 9)
        self.contentFrameBasicInfo = QFrame(self.basicInformation)
        self.contentFrameBasicInfo.setObjectName(u"contentFrameBasicInfo")
        self.contentFrameBasicInfo.setMinimumSize(QSize(1000, 600))
        self.contentFrameBasicInfo.setMaximumSize(QSize(1200, 600))
        self.contentFrameBasicInfo.setStyleSheet(u"")
        self.contentFrameBasicInfo.setFrameShape(QFrame.Shape.NoFrame)
        self.contentFrameBasicInfo.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_4 = QVBoxLayout(self.contentFrameBasicInfo)
        self.verticalLayout_4.setObjectName(u"verticalLayout_4")
        self.verticalLayout_4.setContentsMargins(9, 5, 5, 9)
        self.frame_5 = QFrame(self.contentFrameBasicInfo)
        self.frame_5.setObjectName(u"frame_5")
        self.frame_5.setMinimumSize(QSize(0, 50))
        self.frame_5.setMaximumSize(QSize(16777215, 50))
        font4 = QFont()
        font4.setPointSize(11)
        self.frame_5.setFont(font4)
        self.frame_5.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_5.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_4 = QHBoxLayout(self.frame_5)
        self.horizontalLayout_4.setSpacing(0)
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.horizontalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_4.addItem(self.horizontalSpacer)

        self.subSecBasicInfo = QLabel(self.frame_5)
        self.subSecBasicInfo.setObjectName(u"subSecBasicInfo")
        self.subSecBasicInfo.setMinimumSize(QSize(210, 45))
        self.subSecBasicInfo.setMaximumSize(QSize(16777215, 16777215))
        self.subSecBasicInfo.setFont(font3)
        self.subSecBasicInfo.setStyleSheet(u"background-color: #252525;\n"
"border-radius: 17px;\n"
"padding:5px;\n"
"border: 2px solid red;\n"
"")
        self.subSecBasicInfo.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_4.addWidget(self.subSecBasicInfo)

        self.horizontalSpacer_34 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_4.addItem(self.horizontalSpacer_34)


        self.verticalLayout_4.addWidget(self.frame_5)

        self.subFrameBasicInfo = QFrame(self.contentFrameBasicInfo)
        self.subFrameBasicInfo.setObjectName(u"subFrameBasicInfo")
        self.subFrameBasicInfo.setStyleSheet(u"QLabel{\n"
"	background-color: #333333;\n"
"	border-radius:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QLineEdit, QDateEdit, QDoubleSpinBox, QSpinBox{\n"
"	background-color: rgb(84, 103, 163);\n"
"	border:1px solid #9CAECA;\n"
"	border-radius: 5px;\n"
"	padding:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"")
        self.subFrameBasicInfo.setFrameShape(QFrame.Shape.StyledPanel)
        self.subFrameBasicInfo.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_45 = QHBoxLayout(self.subFrameBasicInfo)
        self.horizontalLayout_45.setObjectName(u"horizontalLayout_45")
        self.horizontalSpacer_5 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_45.addItem(self.horizontalSpacer_5)

        self.frame_45 = QFrame(self.subFrameBasicInfo)
        self.frame_45.setObjectName(u"frame_45")
        self.frame_45.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_45.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_58 = QVBoxLayout(self.frame_45)
        self.verticalLayout_58.setSpacing(20)
        self.verticalLayout_58.setObjectName(u"verticalLayout_58")
        self.subLblAim = QLabel(self.frame_45)
        self.subLblAim.setObjectName(u"subLblAim")
        self.subLblAim.setMinimumSize(QSize(450, 55))
        self.subLblAim.setMaximumSize(QSize(16777215, 55))
        self.subLblAim.setStyleSheet(u"")
        self.subLblAim.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_58.addWidget(self.subLblAim)

        self.subLblProp = QLabel(self.frame_45)
        self.subLblProp.setObjectName(u"subLblProp")
        self.subLblProp.setMinimumSize(QSize(450, 55))
        self.subLblProp.setMaximumSize(QSize(16777215, 55))
        self.subLblProp.setStyleSheet(u"")
        self.subLblProp.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_58.addWidget(self.subLblProp)

        self.subLblPropRI = QLabel(self.frame_45)
        self.subLblPropRI.setObjectName(u"subLblPropRI")
        self.subLblPropRI.setMinimumSize(QSize(450, 55))
        self.subLblPropRI.setMaximumSize(QSize(16777215, 55))
        self.subLblPropRI.setStyleSheet(u"")
        self.subLblPropRI.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_58.addWidget(self.subLblPropRI)

        self.subLblCat = QLabel(self.frame_45)
        self.subLblCat.setObjectName(u"subLblCat")
        self.subLblCat.setMinimumSize(QSize(450, 55))
        self.subLblCat.setMaximumSize(QSize(16777215, 55))
        self.subLblCat.setStyleSheet(u"")
        self.subLblCat.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_58.addWidget(self.subLblCat)

        self.subLblTestNo = QLabel(self.frame_45)
        self.subLblTestNo.setObjectName(u"subLblTestNo")
        self.subLblTestNo.setMinimumSize(QSize(450, 55))
        self.subLblTestNo.setMaximumSize(QSize(16777215, 55))
        self.subLblTestNo.setStyleSheet(u"")
        self.subLblTestNo.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_58.addWidget(self.subLblTestNo)

        self.subLblTestDate = QLabel(self.frame_45)
        self.subLblTestDate.setObjectName(u"subLblTestDate")
        self.subLblTestDate.setMinimumSize(QSize(450, 55))
        self.subLblTestDate.setMaximumSize(QSize(16777215, 55))
        self.subLblTestDate.setStyleSheet(u"")
        self.subLblTestDate.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_58.addWidget(self.subLblTestDate)


        self.horizontalLayout_45.addWidget(self.frame_45)

        self.frame_46 = QFrame(self.subFrameBasicInfo)
        self.frame_46.setObjectName(u"frame_46")
        self.frame_46.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_46.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_59 = QVBoxLayout(self.frame_46)
        self.verticalLayout_59.setSpacing(20)
        self.verticalLayout_59.setObjectName(u"verticalLayout_59")
        self.subLnEdtAim = QLineEdit(self.frame_46)
        self.subLnEdtAim.setObjectName(u"subLnEdtAim")
        self.subLnEdtAim.setMinimumSize(QSize(450, 55))
        self.subLnEdtAim.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtAim.setStyleSheet(u"")

        self.verticalLayout_59.addWidget(self.subLnEdtAim)

        self.subLnEdtProp = QLineEdit(self.frame_46)
        self.subLnEdtProp.setObjectName(u"subLnEdtProp")
        self.subLnEdtProp.setMinimumSize(QSize(450, 55))
        self.subLnEdtProp.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtProp.setStyleSheet(u"background-color: rgb(84, 103, 163);\n"
"border:1px solid #9CAECA;\n"
"border-radius: 5px;\n"
"padding:5px;")

        self.verticalLayout_59.addWidget(self.subLnEdtProp)

        self.subLnEdtPropRI = CustomDoubleSpinBox(self.frame_46)
        self.subLnEdtPropRI.setObjectName(u"subLnEdtPropRI")
        self.subLnEdtPropRI.setMinimumSize(QSize(450, 55))
        self.subLnEdtPropRI.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtPropRI.setStyleSheet(u"")
        self.subLnEdtPropRI.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtPropRI.setDecimals(6)
        self.subLnEdtPropRI.setMaximum(10000000.000000000000000)
        self.subLnEdtPropRI.setSingleStep(0.000000000000000)

        self.verticalLayout_59.addWidget(self.subLnEdtPropRI)

        self.subLnEdtCat = QLineEdit(self.frame_46)
        self.subLnEdtCat.setObjectName(u"subLnEdtCat")
        self.subLnEdtCat.setMinimumSize(QSize(450, 55))
        self.subLnEdtCat.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtCat.setStyleSheet(u"background-color: rgb(84, 103, 163);\n"
"border:1px solid #9CAECA;\n"
"border-radius: 5px;\n"
"padding:5px;")

        self.verticalLayout_59.addWidget(self.subLnEdtCat)

        self.subLnEdtTestNo = QSpinBox(self.frame_46)
        self.subLnEdtTestNo.setObjectName(u"subLnEdtTestNo")
        self.subLnEdtTestNo.setMinimumSize(QSize(450, 55))
        self.subLnEdtTestNo.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtTestNo.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtTestNo.setMaximum(10000000)
        self.subLnEdtTestNo.setSingleStep(0)

        self.verticalLayout_59.addWidget(self.subLnEdtTestNo)

        self.subLnEdtTestDate = QDateEdit(self.frame_46)
        self.subLnEdtTestDate.setObjectName(u"subLnEdtTestDate")
        self.subLnEdtTestDate.setMinimumSize(QSize(450, 55))
        self.subLnEdtTestDate.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtTestDate.setStyleSheet(u"\n"
"            QDateEdit::drop-down {\n"
"                subcontrol-origin: padding;\n"
"                subcontrol-position: top right;\n"
"                width: 1px;\n"
"              \n"
"            }\n"
"            QDateEdit::down-arrow {\n"
"                image: url(down_arrow.png);  /* Replace with your own arrow image */\n"
"                width: 10px;\n"
"                height: 10px;\n"
"            }\n"
"            QCalendarWidget QToolButton {\n"
"                height: 60px;\n"
"                width: 130px;\n"
"                color: white;\n"
"                font-size: 14px;\n"
"                icon-size: 20px, 20px;\n"
"                background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
"                                                  stop:0 #4f4f4f, stop:1 #292929);\n"
"            }\n"
"            QCalendarWidget QMenu {\n"
"                width: 150px;\n"
"                left: 20px;\n"
"                color: white;\n"
"                font-size: 14px;\n"
"          "
                        "      background-color: #4f4f4f;\n"
"            }\n"
"\n"
"            QCalendarWidget QSpinBox {\n"
"                width: 60px;\n"
"                font-size: 14px;\n"
"                color: white;\n"
"                background-color: #292929;\n"
"                selection-background-color: #3d3d3d;\n"
"                selection-color: white;\n"
"            }\n"
"            QCalendarWidget QWidget#qt_calendar_navigationbar {\n"
"                background-color: transparent;\n"
"            }\n"
"            QCalendarWidget QAbstractItemView:enabled {\n"
"                font-size: 14px;\n"
"                color: white;\n"
"                background-color: #333333;\n"
"                selection-background-color: #446699;\n"
"                selection-color: white;\n"
"            }\n"
"            QCalendarWidget QAbstractItemView:disabled {\n"
"                color: #666666;\n"
"            }\n"
"\n"
"QCalendarWidget QWidget {\n"
"    background-color: #111827;\n"
"}\n"
"\n"
"/* Header row (days of"
                        " week)*/\n"
"QCalendarWidget QHeaderView{\n"
"	background-color: #333333;\n"
"	color:white;\n"
"}\n"
"\n"
"QCalendarWidget QHeaderView::section{\n"
"	color: white;\n"
"	background-color: #333333;\n"
"	padding: 2px;\n"
"	border: none;\n"
"}\n"
"\n"
"")
        self.subLnEdtTestDate.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtTestDate.setCalendarPopup(True)
        self.subLnEdtTestDate.setDate(QDate(2024, 1, 1))

        self.verticalLayout_59.addWidget(self.subLnEdtTestDate)


        self.horizontalLayout_45.addWidget(self.frame_46)

        self.horizontalSpacer_6 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_45.addItem(self.horizontalSpacer_6)


        self.verticalLayout_4.addWidget(self.subFrameBasicInfo)


        self.gridLayout_2.addWidget(self.contentFrameBasicInfo, 1, 0, 1, 1)

        self.contentStack.addWidget(self.basicInformation)
        self.systemSpecification = QWidget()
        self.systemSpecification.setObjectName(u"systemSpecification")
        self.gridLayout_4 = QGridLayout(self.systemSpecification)
        self.gridLayout_4.setObjectName(u"gridLayout_4")
        self.gridLayout_4.setContentsMargins(0, 0, 0, 0)
        self.contentFrameSystmSpeci = QFrame(self.systemSpecification)
        self.contentFrameSystmSpeci.setObjectName(u"contentFrameSystmSpeci")
        self.contentFrameSystmSpeci.setMinimumSize(QSize(1000, 600))
        self.contentFrameSystmSpeci.setMaximumSize(QSize(1200, 600))
        self.contentFrameSystmSpeci.setStyleSheet(u"")
        self.contentFrameSystmSpeci.setFrameShape(QFrame.Shape.NoFrame)
        self.contentFrameSystmSpeci.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_5 = QVBoxLayout(self.contentFrameSystmSpeci)
        self.verticalLayout_5.setObjectName(u"verticalLayout_5")
        self.verticalLayout_5.setContentsMargins(9, 5, -1, -1)
        self.frame_7 = QFrame(self.contentFrameSystmSpeci)
        self.frame_7.setObjectName(u"frame_7")
        self.frame_7.setMinimumSize(QSize(0, 50))
        self.frame_7.setMaximumSize(QSize(16777215, 50))
        self.frame_7.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_7.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_5 = QHBoxLayout(self.frame_7)
        self.horizontalLayout_5.setSpacing(0)
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.horizontalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_5.addItem(self.horizontalSpacer_2)

        self.subSecSystmSpeci = QLabel(self.frame_7)
        self.subSecSystmSpeci.setObjectName(u"subSecSystmSpeci")
        self.subSecSystmSpeci.setMinimumSize(QSize(225, 45))
        self.subSecSystmSpeci.setMaximumSize(QSize(16777215, 16777215))
        self.subSecSystmSpeci.setFont(font3)
        self.subSecSystmSpeci.setStyleSheet(u"background-color: #252525;\n"
"border-radius: 17px;\n"
"padding:5px;\n"
"border: 2px solid red;\n"
"")
        self.subSecSystmSpeci.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_5.addWidget(self.subSecSystmSpeci)

        self.horizontalSpacer_43 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_5.addItem(self.horizontalSpacer_43)


        self.verticalLayout_5.addWidget(self.frame_7)

        self.scrollArea_5 = QScrollArea(self.contentFrameSystmSpeci)
        self.scrollArea_5.setObjectName(u"scrollArea_5")
        self.scrollArea_5.setStyleSheet(u"QLabel{\n"
"	background-color: #333333;\n"
"	border-radius:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QLineEdit, QSpinBox, QDoubleSpinBox{\n"
"	background-color: rgb(84, 103, 163);\n"
"	border:1px solid #9CAECA;\n"
"	border-radius: 5px;\n"
"	padding:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"/* Scrollbar styling */\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    width: 12px;\n"
"    margin: 0px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 4px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}")
        self.scrollArea_5.setWidgetResizable(True)
        self.scrollAreaWidgetContents_3 = QWidget()
        self.scrollAreaWidgetContents_3.setObjectName(u"scrollAreaWidgetContents_3")
        self.scrollAreaWidgetContents_3.setGeometry(QRect(0, 0, 1074, 626))
        sizePolicy.setHeightForWidth(self.scrollAreaWidgetContents_3.sizePolicy().hasHeightForWidth())
        self.scrollAreaWidgetContents_3.setSizePolicy(sizePolicy)
        self.gridLayout_12 = QGridLayout(self.scrollAreaWidgetContents_3)
        self.gridLayout_12.setObjectName(u"gridLayout_12")
        self.horizontalSpacer_3 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_12.addItem(self.horizontalSpacer_3, 0, 0, 1, 1)

        self.horizontalSpacer_4 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_12.addItem(self.horizontalSpacer_4, 0, 3, 1, 1)

        self.frame_44 = QFrame(self.scrollAreaWidgetContents_3)
        self.frame_44.setObjectName(u"frame_44")
        self.frame_44.setMinimumSize(QSize(0, 0))
        self.frame_44.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_44.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_57 = QVBoxLayout(self.frame_44)
        self.verticalLayout_57.setSpacing(20)
        self.verticalLayout_57.setObjectName(u"verticalLayout_57")
        self.subLblChmbrNo_2 = QLabel(self.frame_44)
        self.subLblChmbrNo_2.setObjectName(u"subLblChmbrNo_2")
        self.subLblChmbrNo_2.setMinimumSize(QSize(450, 55))
        self.subLblChmbrNo_2.setMaximumSize(QSize(16777215, 60))
        self.subLblChmbrNo_2.setStyleSheet(u"")
        self.subLblChmbrNo_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_57.addWidget(self.subLblChmbrNo_2)

        self.subLblChmbrMat = QLabel(self.frame_44)
        self.subLblChmbrMat.setObjectName(u"subLblChmbrMat")
        self.subLblChmbrMat.setMinimumSize(QSize(450, 55))
        self.subLblChmbrMat.setMaximumSize(QSize(16777215, 60))
        self.subLblChmbrMat.setStyleSheet(u"")
        self.subLblChmbrMat.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_57.addWidget(self.subLblChmbrMat)

        self.subLblChmbrDept = QLabel(self.frame_44)
        self.subLblChmbrDept.setObjectName(u"subLblChmbrDept")
        self.subLblChmbrDept.setMinimumSize(QSize(450, 55))
        self.subLblChmbrDept.setMaximumSize(QSize(16777215, 60))
        self.subLblChmbrDept.setStyleSheet(u"")
        self.subLblChmbrDept.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_57.addWidget(self.subLblChmbrDept)

        self.subLblChmbrDia = QLabel(self.frame_44)
        self.subLblChmbrDia.setObjectName(u"subLblChmbrDia")
        self.subLblChmbrDia.setMinimumSize(QSize(450, 55))
        self.subLblChmbrDia.setMaximumSize(QSize(16777215, 60))
        self.subLblChmbrDia.setStyleSheet(u"")
        self.subLblChmbrDia.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_57.addWidget(self.subLblChmbrDia)

        self.subLblNozlThrtDime = QLabel(self.frame_44)
        self.subLblNozlThrtDime.setObjectName(u"subLblNozlThrtDime")
        self.subLblNozlThrtDime.setMinimumSize(QSize(450, 55))
        self.subLblNozlThrtDime.setMaximumSize(QSize(16777215, 60))
        self.subLblNozlThrtDime.setStyleSheet(u"")
        self.subLblNozlThrtDime.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_57.addWidget(self.subLblNozlThrtDime)

        self.subLblRetainerPltOrfcDia = QLabel(self.frame_44)
        self.subLblRetainerPltOrfcDia.setObjectName(u"subLblRetainerPltOrfcDia")
        self.subLblRetainerPltOrfcDia.setMinimumSize(QSize(450, 55))
        self.subLblRetainerPltOrfcDia.setMaximumSize(QSize(16777215, 60))
        self.subLblRetainerPltOrfcDia.setStyleSheet(u"")
        self.subLblRetainerPltOrfcDia.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_57.addWidget(self.subLblRetainerPltOrfcDia)

        self.subLblMeshMat = QLabel(self.frame_44)
        self.subLblMeshMat.setObjectName(u"subLblMeshMat")
        self.subLblMeshMat.setMinimumSize(QSize(450, 55))
        self.subLblMeshMat.setMaximumSize(QSize(16777215, 60))
        self.subLblMeshMat.setStyleSheet(u"")
        self.subLblMeshMat.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_57.addWidget(self.subLblMeshMat)

        self.subLblMeshSize = QLabel(self.frame_44)
        self.subLblMeshSize.setObjectName(u"subLblMeshSize")
        self.subLblMeshSize.setMinimumSize(QSize(450, 55))
        self.subLblMeshSize.setMaximumSize(QSize(16777215, 60))
        self.subLblMeshSize.setStyleSheet(u"")
        self.subLblMeshSize.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_57.addWidget(self.subLblMeshSize)


        self.gridLayout_12.addWidget(self.frame_44, 0, 1, 1, 1)

        self.frame_43 = QFrame(self.scrollAreaWidgetContents_3)
        self.frame_43.setObjectName(u"frame_43")
        self.frame_43.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_43.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_56 = QVBoxLayout(self.frame_43)
        self.verticalLayout_56.setSpacing(20)
        self.verticalLayout_56.setObjectName(u"verticalLayout_56")
        self.subLnEdtChmbrNo = QLineEdit(self.frame_43)
        self.subLnEdtChmbrNo.setObjectName(u"subLnEdtChmbrNo")
        self.subLnEdtChmbrNo.setMinimumSize(QSize(450, 55))
        self.subLnEdtChmbrNo.setMaximumSize(QSize(16777215, 60))
        self.subLnEdtChmbrNo.setStyleSheet(u"")

        self.verticalLayout_56.addWidget(self.subLnEdtChmbrNo)

        self.subLnEdtChmbrMat = QLineEdit(self.frame_43)
        self.subLnEdtChmbrMat.setObjectName(u"subLnEdtChmbrMat")
        self.subLnEdtChmbrMat.setMinimumSize(QSize(450, 55))
        self.subLnEdtChmbrMat.setMaximumSize(QSize(16777215, 60))
        self.subLnEdtChmbrMat.setStyleSheet(u"")

        self.verticalLayout_56.addWidget(self.subLnEdtChmbrMat)

        self.subLnEdtChmbrDept = CustomDoubleSpinBox(self.frame_43)
        self.subLnEdtChmbrDept.setObjectName(u"subLnEdtChmbrDept")
        self.subLnEdtChmbrDept.setMinimumSize(QSize(450, 55))
        self.subLnEdtChmbrDept.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtChmbrDept.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtChmbrDept.setDecimals(6)
        self.subLnEdtChmbrDept.setMaximum(10000000.000000000000000)
        self.subLnEdtChmbrDept.setSingleStep(0.000000000000000)

        self.verticalLayout_56.addWidget(self.subLnEdtChmbrDept)

        self.subLnEdtChmbrDiaFrame = QFrame(self.frame_43)
        self.subLnEdtChmbrDiaFrame.setObjectName(u"subLnEdtChmbrDiaFrame")
        self.subLnEdtChmbrDiaFrame.setMinimumSize(QSize(0, 55))
        self.subLnEdtChmbrDiaFrame.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtChmbrDiaFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.subLnEdtChmbrDiaFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_30 = QHBoxLayout(self.subLnEdtChmbrDiaFrame)
        self.horizontalLayout_30.setSpacing(8)
        self.horizontalLayout_30.setObjectName(u"horizontalLayout_30")
        self.horizontalLayout_30.setContentsMargins(0, 0, 0, 0)
        self.label_2 = QLabel(self.subLnEdtChmbrDiaFrame)
        self.label_2.setObjectName(u"label_2")
        self.label_2.setStyleSheet(u"background-color:#1f2937;")

        self.horizontalLayout_30.addWidget(self.label_2)

        self.subLnEdtInternalChmbrDia = CustomDoubleSpinBox(self.subLnEdtChmbrDiaFrame)
        self.subLnEdtInternalChmbrDia.setObjectName(u"subLnEdtInternalChmbrDia")
        self.subLnEdtInternalChmbrDia.setMinimumSize(QSize(180, 45))
        self.subLnEdtInternalChmbrDia.setMaximumSize(QSize(16777215, 45))
        self.subLnEdtInternalChmbrDia.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtInternalChmbrDia.setDecimals(6)
        self.subLnEdtInternalChmbrDia.setMaximum(10000000.000000000000000)
        self.subLnEdtInternalChmbrDia.setSingleStep(0.000000000000000)

        self.horizontalLayout_30.addWidget(self.subLnEdtInternalChmbrDia)

        self.label_3 = QLabel(self.subLnEdtChmbrDiaFrame)
        self.label_3.setObjectName(u"label_3")
        self.label_3.setStyleSheet(u"background-color:#1f2937;")

        self.horizontalLayout_30.addWidget(self.label_3)

        self.subLnEdtExternalChmbrDia = CustomDoubleSpinBox(self.subLnEdtChmbrDiaFrame)
        self.subLnEdtExternalChmbrDia.setObjectName(u"subLnEdtExternalChmbrDia")
        self.subLnEdtExternalChmbrDia.setMinimumSize(QSize(180, 45))
        self.subLnEdtExternalChmbrDia.setMaximumSize(QSize(16777215, 45))
        self.subLnEdtExternalChmbrDia.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtExternalChmbrDia.setDecimals(6)
        self.subLnEdtExternalChmbrDia.setMaximum(10000000.000000000000000)
        self.subLnEdtExternalChmbrDia.setSingleStep(0.000000000000000)

        self.horizontalLayout_30.addWidget(self.subLnEdtExternalChmbrDia)


        self.verticalLayout_56.addWidget(self.subLnEdtChmbrDiaFrame)

        self.subLnEdtNozlThrtDime = CustomDoubleSpinBox(self.frame_43)
        self.subLnEdtNozlThrtDime.setObjectName(u"subLnEdtNozlThrtDime")
        self.subLnEdtNozlThrtDime.setMinimumSize(QSize(450, 55))
        self.subLnEdtNozlThrtDime.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtNozlThrtDime.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtNozlThrtDime.setDecimals(6)
        self.subLnEdtNozlThrtDime.setMaximum(10000000.000000000000000)
        self.subLnEdtNozlThrtDime.setSingleStep(0.000000000000000)

        self.verticalLayout_56.addWidget(self.subLnEdtNozlThrtDime)

        self.subLnEdtRetainerPltOrfcDia = CustomDoubleSpinBox(self.frame_43)
        self.subLnEdtRetainerPltOrfcDia.setObjectName(u"subLnEdtRetainerPltOrfcDia")
        self.subLnEdtRetainerPltOrfcDia.setMinimumSize(QSize(450, 55))
        self.subLnEdtRetainerPltOrfcDia.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtRetainerPltOrfcDia.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtRetainerPltOrfcDia.setDecimals(6)
        self.subLnEdtRetainerPltOrfcDia.setMaximum(10000000.000000000000000)
        self.subLnEdtRetainerPltOrfcDia.setSingleStep(0.000000000000000)

        self.verticalLayout_56.addWidget(self.subLnEdtRetainerPltOrfcDia)

        self.subLnEdtMeshMat = QLineEdit(self.frame_43)
        self.subLnEdtMeshMat.setObjectName(u"subLnEdtMeshMat")
        self.subLnEdtMeshMat.setMinimumSize(QSize(450, 55))
        self.subLnEdtMeshMat.setMaximumSize(QSize(16777215, 60))
        self.subLnEdtMeshMat.setStyleSheet(u"")

        self.verticalLayout_56.addWidget(self.subLnEdtMeshMat)

        self.subLnEdtMeshSize = QLineEdit(self.frame_43)
        self.subLnEdtMeshSize.setObjectName(u"subLnEdtMeshSize")
        self.subLnEdtMeshSize.setMinimumSize(QSize(450, 55))
        self.subLnEdtMeshSize.setMaximumSize(QSize(16777215, 60))
        self.subLnEdtMeshSize.setStyleSheet(u"")

        self.verticalLayout_56.addWidget(self.subLnEdtMeshSize)


        self.gridLayout_12.addWidget(self.frame_43, 0, 2, 1, 1)

        self.scrollArea_5.setWidget(self.scrollAreaWidgetContents_3)

        self.verticalLayout_5.addWidget(self.scrollArea_5)


        self.gridLayout_4.addWidget(self.contentFrameSystmSpeci, 0, 0, 1, 1)

        self.contentStack.addWidget(self.systemSpecification)
        self.propellantSpecification = QWidget()
        self.propellantSpecification.setObjectName(u"propellantSpecification")
        self.gridLayout_6 = QGridLayout(self.propellantSpecification)
        self.gridLayout_6.setObjectName(u"gridLayout_6")
        self.gridLayout_6.setContentsMargins(6, -1, 6, -1)
        self.contentFramePropSpec = QFrame(self.propellantSpecification)
        self.contentFramePropSpec.setObjectName(u"contentFramePropSpec")
        self.contentFramePropSpec.setMinimumSize(QSize(0, 600))
        self.contentFramePropSpec.setMaximumSize(QSize(1200, 600))
        self.contentFramePropSpec.setStyleSheet(u"")
        self.contentFramePropSpec.setFrameShape(QFrame.Shape.NoFrame)
        self.contentFramePropSpec.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_6 = QVBoxLayout(self.contentFramePropSpec)
        self.verticalLayout_6.setObjectName(u"verticalLayout_6")
        self.verticalLayout_6.setContentsMargins(5, 5, 5, -1)
        self.frame_9 = QFrame(self.contentFramePropSpec)
        self.frame_9.setObjectName(u"frame_9")
        self.frame_9.setMinimumSize(QSize(0, 50))
        self.frame_9.setMaximumSize(QSize(16777215, 50))
        self.frame_9.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_9.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_6 = QHBoxLayout(self.frame_9)
        self.horizontalLayout_6.setSpacing(0)
        self.horizontalLayout_6.setObjectName(u"horizontalLayout_6")
        self.horizontalLayout_6.setContentsMargins(0, 0, 5, 0)
        self.horizontalSpacer_7 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_6.addItem(self.horizontalSpacer_7)

        self.subSecPropSpec = QLabel(self.frame_9)
        self.subSecPropSpec.setObjectName(u"subSecPropSpec")
        self.subSecPropSpec.setMinimumSize(QSize(240, 45))
        self.subSecPropSpec.setMaximumSize(QSize(16777215, 16777215))
        self.subSecPropSpec.setFont(font3)
        self.subSecPropSpec.setStyleSheet(u"background-color: #252525;\n"
"border-radius: 17px;\n"
"padding:5px;\n"
"border: 2px solid red;\n"
"\n"
"")
        self.subSecPropSpec.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_6.addWidget(self.subSecPropSpec)

        self.horizontalSpacer_45 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_6.addItem(self.horizontalSpacer_45)


        self.verticalLayout_6.addWidget(self.frame_9)

        self.subFramePropSpec = QFrame(self.contentFramePropSpec)
        self.subFramePropSpec.setObjectName(u"subFramePropSpec")
        self.subFramePropSpec.setStyleSheet(u"QLabel{\n"
"	background-color: #333333;\n"
"	border-radius:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QLineEdit, QDateEdit, QDoubleSpinBox{\n"
"	background-color: rgb(84, 103, 163);\n"
"	border:1px solid #9CAECA;\n"
"	border-radius: 5px;\n"
"	padding:5px;\n"
"	font-size: 17px;\n"
"font-family: Arial;\n"
"}")
        self.subFramePropSpec.setFrameShape(QFrame.Shape.StyledPanel)
        self.subFramePropSpec.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_43 = QHBoxLayout(self.subFramePropSpec)
        self.horizontalLayout_43.setObjectName(u"horizontalLayout_43")
        self.horizontalSpacer_8 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_43.addItem(self.horizontalSpacer_8)

        self.frame_41 = QFrame(self.subFramePropSpec)
        self.frame_41.setObjectName(u"frame_41")
        self.frame_41.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_41.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_54 = QVBoxLayout(self.frame_41)
        self.verticalLayout_54.setSpacing(20)
        self.verticalLayout_54.setObjectName(u"verticalLayout_54")
        self.subLblTypeOfProp = QLabel(self.frame_41)
        self.subLblTypeOfProp.setObjectName(u"subLblTypeOfProp")
        self.subLblTypeOfProp.setMinimumSize(QSize(450, 55))
        self.subLblTypeOfProp.setMaximumSize(QSize(16777215, 55))
        self.subLblTypeOfProp.setStyleSheet(u"")
        self.subLblTypeOfProp.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_54.addWidget(self.subLblTypeOfProp)

        self.subLblConcBefTest = QLabel(self.frame_41)
        self.subLblConcBefTest.setObjectName(u"subLblConcBefTest")
        self.subLblConcBefTest.setMinimumSize(QSize(450, 55))
        self.subLblConcBefTest.setMaximumSize(QSize(16777215, 55))
        self.subLblConcBefTest.setStyleSheet(u"")
        self.subLblConcBefTest.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_54.addWidget(self.subLblConcBefTest)

        self.subLblStability = QLabel(self.frame_41)
        self.subLblStability.setObjectName(u"subLblStability")
        self.subLblStability.setMinimumSize(QSize(450, 55))
        self.subLblStability.setMaximumSize(QSize(16777215, 55))
        self.subLblStability.setStyleSheet(u"")
        self.subLblStability.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_54.addWidget(self.subLblStability)

        self.subLblWghtOfPropBefTest = QLabel(self.frame_41)
        self.subLblWghtOfPropBefTest.setObjectName(u"subLblWghtOfPropBefTest")
        self.subLblWghtOfPropBefTest.setMinimumSize(QSize(450, 55))
        self.subLblWghtOfPropBefTest.setMaximumSize(QSize(16777215, 55))
        self.subLblWghtOfPropBefTest.setStyleSheet(u"")
        self.subLblWghtOfPropBefTest.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_54.addWidget(self.subLblWghtOfPropBefTest)

        self.subLblWghtOfPropAftTest = QLabel(self.frame_41)
        self.subLblWghtOfPropAftTest.setObjectName(u"subLblWghtOfPropAftTest")
        self.subLblWghtOfPropAftTest.setMinimumSize(QSize(450, 55))
        self.subLblWghtOfPropAftTest.setMaximumSize(QSize(16777215, 55))
        self.subLblWghtOfPropAftTest.setStyleSheet(u"")
        self.subLblWghtOfPropAftTest.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_54.addWidget(self.subLblWghtOfPropAftTest)


        self.horizontalLayout_43.addWidget(self.frame_41)

        self.frame_42 = QFrame(self.subFramePropSpec)
        self.frame_42.setObjectName(u"frame_42")
        self.frame_42.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_42.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_55 = QVBoxLayout(self.frame_42)
        self.verticalLayout_55.setSpacing(20)
        self.verticalLayout_55.setObjectName(u"verticalLayout_55")
        self.subLnEdtTypeOfProp = QLineEdit(self.frame_42)
        self.subLnEdtTypeOfProp.setObjectName(u"subLnEdtTypeOfProp")
        self.subLnEdtTypeOfProp.setMinimumSize(QSize(450, 55))
        self.subLnEdtTypeOfProp.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtTypeOfProp.setStyleSheet(u"")

        self.verticalLayout_55.addWidget(self.subLnEdtTypeOfProp)

        self.subLnEdtConcBefTest = CustomDoubleSpinBox(self.frame_42)
        self.subLnEdtConcBefTest.setObjectName(u"subLnEdtConcBefTest")
        self.subLnEdtConcBefTest.setMinimumSize(QSize(450, 55))
        self.subLnEdtConcBefTest.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtConcBefTest.setReadOnly(True)
        self.subLnEdtConcBefTest.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtConcBefTest.setMaximum(100.000000000000000)
        self.subLnEdtConcBefTest.setSingleStep(0.000000000000000)

        self.verticalLayout_55.addWidget(self.subLnEdtConcBefTest)

        self.subLnEdtStability = QLineEdit(self.frame_42)
        self.subLnEdtStability.setObjectName(u"subLnEdtStability")
        self.subLnEdtStability.setMinimumSize(QSize(450, 55))
        self.subLnEdtStability.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtStability.setStyleSheet(u"")

        self.verticalLayout_55.addWidget(self.subLnEdtStability)

        self.subLnEdtWghtOfPropBefTest = CustomDoubleSpinBox(self.frame_42)
        self.subLnEdtWghtOfPropBefTest.setObjectName(u"subLnEdtWghtOfPropBefTest")
        self.subLnEdtWghtOfPropBefTest.setMinimumSize(QSize(450, 55))
        self.subLnEdtWghtOfPropBefTest.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtWghtOfPropBefTest.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtWghtOfPropBefTest.setDecimals(6)
        self.subLnEdtWghtOfPropBefTest.setMaximum(10000000.000000000000000)
        self.subLnEdtWghtOfPropBefTest.setSingleStep(0.000000000000000)

        self.verticalLayout_55.addWidget(self.subLnEdtWghtOfPropBefTest)

        self.subLnEdtWghtOfPropAftTest = CustomDoubleSpinBox(self.frame_42)
        self.subLnEdtWghtOfPropAftTest.setObjectName(u"subLnEdtWghtOfPropAftTest")
        self.subLnEdtWghtOfPropAftTest.setMinimumSize(QSize(450, 55))
        self.subLnEdtWghtOfPropAftTest.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtWghtOfPropAftTest.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtWghtOfPropAftTest.setDecimals(6)
        self.subLnEdtWghtOfPropAftTest.setMaximum(10000000.000000000000000)
        self.subLnEdtWghtOfPropAftTest.setSingleStep(0.000000000000000)

        self.verticalLayout_55.addWidget(self.subLnEdtWghtOfPropAftTest)


        self.horizontalLayout_43.addWidget(self.frame_42)

        self.horizontalSpacer_9 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_43.addItem(self.horizontalSpacer_9)


        self.verticalLayout_6.addWidget(self.subFramePropSpec)


        self.gridLayout_6.addWidget(self.contentFramePropSpec, 0, 0, 1, 1)

        self.contentStack.addWidget(self.propellantSpecification)
        self.catalystSpecification = QWidget()
        self.catalystSpecification.setObjectName(u"catalystSpecification")
        self.gridLayout_8 = QGridLayout(self.catalystSpecification)
        self.gridLayout_8.setObjectName(u"gridLayout_8")
        self.gridLayout_8.setContentsMargins(0, 0, 0, 0)
        self.contentFrameCatSpec = QFrame(self.catalystSpecification)
        self.contentFrameCatSpec.setObjectName(u"contentFrameCatSpec")
        self.contentFrameCatSpec.setMinimumSize(QSize(1000, 600))
        self.contentFrameCatSpec.setMaximumSize(QSize(1200, 600))
        self.contentFrameCatSpec.setStyleSheet(u"")
        self.contentFrameCatSpec.setFrameShape(QFrame.Shape.NoFrame)
        self.contentFrameCatSpec.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_7 = QVBoxLayout(self.contentFrameCatSpec)
        self.verticalLayout_7.setObjectName(u"verticalLayout_7")
        self.verticalLayout_7.setContentsMargins(5, 5, 5, -1)
        self.frame_11 = QFrame(self.contentFrameCatSpec)
        self.frame_11.setObjectName(u"frame_11")
        self.frame_11.setMinimumSize(QSize(0, 50))
        self.frame_11.setMaximumSize(QSize(16777215, 50))
        self.frame_11.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_11.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_7 = QHBoxLayout(self.frame_11)
        self.horizontalLayout_7.setSpacing(0)
        self.horizontalLayout_7.setObjectName(u"horizontalLayout_7")
        self.horizontalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_10 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_7.addItem(self.horizontalSpacer_10)

        self.subSecCatSpec = QLabel(self.frame_11)
        self.subSecCatSpec.setObjectName(u"subSecCatSpec")
        self.subSecCatSpec.setMinimumSize(QSize(230, 45))
        self.subSecCatSpec.setMaximumSize(QSize(16777215, 16777215))
        self.subSecCatSpec.setFont(font3)
        self.subSecCatSpec.setStyleSheet(u"background-color: #252525;\n"
"border-radius: 17px;\n"
"padding:5px;\n"
"border: 2px solid red;\n"
"")
        self.subSecCatSpec.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_7.addWidget(self.subSecCatSpec)

        self.horizontalSpacer_47 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_7.addItem(self.horizontalSpacer_47)


        self.verticalLayout_7.addWidget(self.frame_11)

        self.scrollArea_6 = QScrollArea(self.contentFrameCatSpec)
        self.scrollArea_6.setObjectName(u"scrollArea_6")
        self.scrollArea_6.setStyleSheet(u"QLabel{\n"
"	background-color: #333333;\n"
"	border-radius:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QLineEdit, QDoubleSpinBox{\n"
"	background-color: rgb(84, 103, 163);\n"
"	border:1px solid #9CAECA;\n"
"	border-radius: 5px;\n"
"	padding:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"/* Scrollbar styling */\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    width: 12px;\n"
"    margin: 0px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 4px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}")
        self.scrollArea_6.setWidgetResizable(True)
        self.scrollAreaWidgetContents_4 = QWidget()
        self.scrollAreaWidgetContents_4.setObjectName(u"scrollAreaWidgetContents_4")
        self.scrollAreaWidgetContents_4.setGeometry(QRect(0, 0, 992, 510))
        self.gridLayout_16 = QGridLayout(self.scrollAreaWidgetContents_4)
        self.gridLayout_16.setObjectName(u"gridLayout_16")
        self.horizontalSpacer_11 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_16.addItem(self.horizontalSpacer_11, 0, 0, 1, 1)

        self.frame_40 = QFrame(self.scrollAreaWidgetContents_4)
        self.frame_40.setObjectName(u"frame_40")
        self.frame_40.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_40.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_53 = QVBoxLayout(self.frame_40)
        self.verticalLayout_53.setSpacing(20)
        self.verticalLayout_53.setObjectName(u"verticalLayout_53")
        self.subLblCatType = QLabel(self.frame_40)
        self.subLblCatType.setObjectName(u"subLblCatType")
        self.subLblCatType.setMinimumSize(QSize(450, 55))
        self.subLblCatType.setMaximumSize(QSize(16777215, 55))
        self.subLblCatType.setStyleSheet(u"")
        self.subLblCatType.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_53.addWidget(self.subLblCatType)

        self.subLblCatGrade = QLabel(self.frame_40)
        self.subLblCatGrade.setObjectName(u"subLblCatGrade")
        self.subLblCatGrade.setMinimumSize(QSize(450, 55))
        self.subLblCatGrade.setMaximumSize(QSize(16777215, 55))
        self.subLblCatGrade.setStyleSheet(u"")
        self.subLblCatGrade.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_53.addWidget(self.subLblCatGrade)

        self.subLblCatSize = QLabel(self.frame_40)
        self.subLblCatSize.setObjectName(u"subLblCatSize")
        self.subLblCatSize.setMinimumSize(QSize(450, 55))
        self.subLblCatSize.setMaximumSize(QSize(16777215, 55))
        self.subLblCatSize.setStyleSheet(u"")
        self.subLblCatSize.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_53.addWidget(self.subLblCatSize)

        self.subLblCatWghtBefTest = QLabel(self.frame_40)
        self.subLblCatWghtBefTest.setObjectName(u"subLblCatWghtBefTest")
        self.subLblCatWghtBefTest.setMinimumSize(QSize(450, 55))
        self.subLblCatWghtBefTest.setMaximumSize(QSize(16777215, 55))
        self.subLblCatWghtBefTest.setStyleSheet(u"")
        self.subLblCatWghtBefTest.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_53.addWidget(self.subLblCatWghtBefTest)


        self.gridLayout_16.addWidget(self.frame_40, 0, 1, 1, 1)

        self.frame_39 = QFrame(self.scrollAreaWidgetContents_4)
        self.frame_39.setObjectName(u"frame_39")
        self.frame_39.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_39.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_52 = QVBoxLayout(self.frame_39)
        self.verticalLayout_52.setSpacing(20)
        self.verticalLayout_52.setObjectName(u"verticalLayout_52")
        self.subLnEdtCatType = QLineEdit(self.frame_39)
        self.subLnEdtCatType.setObjectName(u"subLnEdtCatType")
        self.subLnEdtCatType.setMinimumSize(QSize(450, 55))
        self.subLnEdtCatType.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtCatType.setStyleSheet(u"background-color: rgb(84, 103, 163);\n"
"border:1px solid #9CAECA;\n"
"border-radius: 5px;\n"
"padding:5px;")

        self.verticalLayout_52.addWidget(self.subLnEdtCatType)

        self.subLnEdtCatGrade = QLineEdit(self.frame_39)
        self.subLnEdtCatGrade.setObjectName(u"subLnEdtCatGrade")
        self.subLnEdtCatGrade.setMinimumSize(QSize(450, 55))
        self.subLnEdtCatGrade.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtCatGrade.setStyleSheet(u"background-color: rgb(84, 103, 163);\n"
"border:1px solid #9CAECA;\n"
"border-radius: 5px;\n"
"padding:5px;")

        self.verticalLayout_52.addWidget(self.subLnEdtCatGrade)

        self.subLnEdtCatSize = QLineEdit(self.frame_39)
        self.subLnEdtCatSize.setObjectName(u"subLnEdtCatSize")
        self.subLnEdtCatSize.setMinimumSize(QSize(450, 55))
        self.subLnEdtCatSize.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtCatSize.setStyleSheet(u"background-color: rgb(84, 103, 163);\n"
"border:1px solid #9CAECA;\n"
"border-radius: 5px;\n"
"padding:5px;")

        self.verticalLayout_52.addWidget(self.subLnEdtCatSize)

        self.subLnEdtCatWghtBefTest = CustomDoubleSpinBox(self.frame_39)
        self.subLnEdtCatWghtBefTest.setObjectName(u"subLnEdtCatWghtBefTest")
        self.subLnEdtCatWghtBefTest.setMinimumSize(QSize(450, 55))
        self.subLnEdtCatWghtBefTest.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtCatWghtBefTest.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtCatWghtBefTest.setDecimals(6)
        self.subLnEdtCatWghtBefTest.setMaximum(10000000.000000000000000)
        self.subLnEdtCatWghtBefTest.setSingleStep(0.000000000000000)

        self.verticalLayout_52.addWidget(self.subLnEdtCatWghtBefTest)


        self.gridLayout_16.addWidget(self.frame_39, 0, 2, 1, 1)

        self.horizontalSpacer_12 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_16.addItem(self.horizontalSpacer_12, 0, 3, 1, 1)

        self.scrollArea_6.setWidget(self.scrollAreaWidgetContents_4)

        self.verticalLayout_7.addWidget(self.scrollArea_6)


        self.gridLayout_8.addWidget(self.contentFrameCatSpec, 0, 0, 1, 1)

        self.contentStack.addWidget(self.catalystSpecification)
        self.componentDetails = QWidget()
        self.componentDetails.setObjectName(u"componentDetails")
        self.gridLayout_11 = QGridLayout(self.componentDetails)
        self.gridLayout_11.setObjectName(u"gridLayout_11")
        self.gridLayout_11.setContentsMargins(0, 0, 0, 0)
        self.contentFrameCompDet = QFrame(self.componentDetails)
        self.contentFrameCompDet.setObjectName(u"contentFrameCompDet")
        self.contentFrameCompDet.setMinimumSize(QSize(1000, 600))
        self.contentFrameCompDet.setMaximumSize(QSize(1200, 600))
        self.contentFrameCompDet.setStyleSheet(u"")
        self.contentFrameCompDet.setFrameShape(QFrame.Shape.NoFrame)
        self.contentFrameCompDet.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_8 = QVBoxLayout(self.contentFrameCompDet)
        self.verticalLayout_8.setSpacing(6)
        self.verticalLayout_8.setObjectName(u"verticalLayout_8")
        self.verticalLayout_8.setContentsMargins(5, 5, 5, -1)
        self.frame_13 = QFrame(self.contentFrameCompDet)
        self.frame_13.setObjectName(u"frame_13")
        self.frame_13.setMinimumSize(QSize(0, 50))
        self.frame_13.setMaximumSize(QSize(16777215, 50))
        self.frame_13.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_13.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_8 = QHBoxLayout(self.frame_13)
        self.horizontalLayout_8.setSpacing(0)
        self.horizontalLayout_8.setObjectName(u"horizontalLayout_8")
        self.horizontalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_13 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_8.addItem(self.horizontalSpacer_13)

        self.subSecCompDet = QLabel(self.frame_13)
        self.subSecCompDet.setObjectName(u"subSecCompDet")
        self.subSecCompDet.setMinimumSize(QSize(200, 45))
        self.subSecCompDet.setMaximumSize(QSize(16777215, 16777215))
        self.subSecCompDet.setFont(font3)
        self.subSecCompDet.setStyleSheet(u"background-color: #252525;\n"
"border-radius: 17px;\n"
"padding:5px;\n"
"border: 2px solid red;\n"
"")
        self.subSecCompDet.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_8.addWidget(self.subSecCompDet)

        self.horizontalSpacer_48 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_8.addItem(self.horizontalSpacer_48)


        self.verticalLayout_8.addWidget(self.frame_13)

        self.frame_81 = QFrame(self.contentFrameCompDet)
        self.frame_81.setObjectName(u"frame_81")
        self.frame_81.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_81.setFrameShadow(QFrame.Shadow.Raised)
        self.gridLayout = QGridLayout(self.frame_81)
        self.gridLayout.setObjectName(u"gridLayout")
        self.gridLayout.setVerticalSpacing(6)
        self.gridLayout.setContentsMargins(0, 0, 0, 0)
        self.cycleTabWidgetPressureSensor = QTabWidget(self.frame_81)
        self.cycleTabWidgetPressureSensor.setObjectName(u"cycleTabWidgetPressureSensor")
        self.cycleTabWidgetPressureSensor.setStyleSheet(u"QTabBar {\n"
"                background: #1E2023;\n"
"                border-radius: 8px;\n"
"                margin: 10px 20px;\n"
"            }\n"
"            \n"
"QTabBar::tab {\n"
"           	background: transparent;\n"
"               color: #86868b;\n"
"               border: none;\n"
"               padding: 6px 24px;\n"
"               border-radius: 8px;\n"
"               font-size: 14px;\n"
"               min-width: 90px;\n"
"               height: 36px;\n"
"               margin: 2px;\n"
"            }\n"
"            \n"
"QTabBar::tab:selected {\n"
"               background: #2C2C2E;\n"
"               color: #FFFFFF;\n"
"			   	margin-top:11px;\n"
"				height:18px;\n"
"				margin-left:21px;\n"
"            }\n"
"            \n"
"QTabBar::tab:hover:!selected {\n"
"              	background: rgba(255, 255, 255, 0.1);\n"
"          		color: #FFFFFF;\n"
"				margin-top:11px;\n"
"				height:18px;\n"
"				margin-left:21px;\n"
"            }\n"
"\n"
"QTabWidget {\n"
"           	background: transp"
                        "arent;\n"
"              	border: none;\n"
"            }\n"
"\n"
"QTabWidget::pane {\n"
"          		border: none;\n"
"              	background: transparent;\n"
"             	margin-top: -5px;\n"
"            }\n"
"\n"
"QTabWidget::tab-bar {\n"
"         	   alignment: center;\n"
"            }\n"
"\n"
"QLabel{\n"
"	background-color: #333333;\n"
"	border-radius:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QLineEdit, QTimeEdit, QDoubleSpinBox{\n"
"	background-color: rgb(84, 103, 163);\n"
"	border:1px solid #9CAECA;\n"
"	border-radius: 5px;\n"
"	padding:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"")
        self.cycle1_2 = QWidget()
        self.cycle1_2.setObjectName(u"cycle1_2")
        self.gridLayout_31 = QGridLayout(self.cycle1_2)
        self.gridLayout_31.setObjectName(u"gridLayout_31")
        self.gridLayout_31.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_64 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_31.addItem(self.horizontalSpacer_64, 0, 2, 1, 1)

        self.cyc1SubFrame_2 = QFrame(self.cycle1_2)
        self.cyc1SubFrame_2.setObjectName(u"cyc1SubFrame_2")
        self.cyc1SubFrame_2.setFrameShape(QFrame.Shape.StyledPanel)
        self.cyc1SubFrame_2.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_69 = QHBoxLayout(self.cyc1SubFrame_2)
        self.horizontalLayout_69.setObjectName(u"horizontalLayout_69")
        self.horizontalLayout_69.setContentsMargins(0, 0, 0, 0)
        self.frame_82 = QFrame(self.cyc1SubFrame_2)
        self.frame_82.setObjectName(u"frame_82")
        self.frame_82.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_82.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_87 = QVBoxLayout(self.frame_82)
        self.verticalLayout_87.setSpacing(25)
        self.verticalLayout_87.setObjectName(u"verticalLayout_87")
        self.verticalLayout_87.setContentsMargins(0, 0, 0, 0)
        self.Vac_Chamb_Pressure_Sensr_type = QLabel(self.frame_82)
        self.Vac_Chamb_Pressure_Sensr_type.setObjectName(u"Vac_Chamb_Pressure_Sensr_type")
        self.Vac_Chamb_Pressure_Sensr_type.setMinimumSize(QSize(450, 55))
        self.Vac_Chamb_Pressure_Sensr_type.setMaximumSize(QSize(16777215, 55))
        self.Vac_Chamb_Pressure_Sensr_type.setStyleSheet(u"")
        self.Vac_Chamb_Pressure_Sensr_type.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_87.addWidget(self.Vac_Chamb_Pressure_Sensr_type)

        self.Vac_Chamb__Pressure_Snsr_No_Slope_Eqn = QLabel(self.frame_82)
        self.Vac_Chamb__Pressure_Snsr_No_Slope_Eqn.setObjectName(u"Vac_Chamb__Pressure_Snsr_No_Slope_Eqn")
        self.Vac_Chamb__Pressure_Snsr_No_Slope_Eqn.setMinimumSize(QSize(450, 55))
        self.Vac_Chamb__Pressure_Snsr_No_Slope_Eqn.setMaximumSize(QSize(16777215, 55))
        self.Vac_Chamb__Pressure_Snsr_No_Slope_Eqn.setStyleSheet(u"")
        self.Vac_Chamb__Pressure_Snsr_No_Slope_Eqn.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_87.addWidget(self.Vac_Chamb__Pressure_Snsr_No_Slope_Eqn)

        self.Vac_Chamb_Pressure_Snsr_range = QLabel(self.frame_82)
        self.Vac_Chamb_Pressure_Snsr_range.setObjectName(u"Vac_Chamb_Pressure_Snsr_range")
        self.Vac_Chamb_Pressure_Snsr_range.setMinimumSize(QSize(450, 55))
        self.Vac_Chamb_Pressure_Snsr_range.setMaximumSize(QSize(16777215, 55))
        self.Vac_Chamb_Pressure_Snsr_range.setStyleSheet(u"")
        self.Vac_Chamb_Pressure_Snsr_range.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_87.addWidget(self.Vac_Chamb_Pressure_Snsr_range)

        self.Vac_Chamb__Pressure_Snsr_IO = QLabel(self.frame_82)
        self.Vac_Chamb__Pressure_Snsr_IO.setObjectName(u"Vac_Chamb__Pressure_Snsr_IO")
        self.Vac_Chamb__Pressure_Snsr_IO.setMinimumSize(QSize(450, 55))
        self.Vac_Chamb__Pressure_Snsr_IO.setMaximumSize(QSize(16777215, 55))
        self.Vac_Chamb__Pressure_Snsr_IO.setStyleSheet(u"")
        self.Vac_Chamb__Pressure_Snsr_IO.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_87.addWidget(self.Vac_Chamb__Pressure_Snsr_IO)


        self.horizontalLayout_69.addWidget(self.frame_82)

        self.frame_83 = QFrame(self.cyc1SubFrame_2)
        self.frame_83.setObjectName(u"frame_83")
        self.frame_83.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_83.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_88 = QVBoxLayout(self.frame_83)
        self.verticalLayout_88.setSpacing(25)
        self.verticalLayout_88.setObjectName(u"verticalLayout_88")
        self.verticalLayout_88.setContentsMargins(0, 0, 0, 0)
        self.Vac_Chamb_Pressure_Sensr_type_Input = QLineEdit(self.frame_83)
        self.Vac_Chamb_Pressure_Sensr_type_Input.setObjectName(u"Vac_Chamb_Pressure_Sensr_type_Input")
        self.Vac_Chamb_Pressure_Sensr_type_Input.setMinimumSize(QSize(450, 55))
        self.Vac_Chamb_Pressure_Sensr_type_Input.setMaximumSize(QSize(16777215, 55))
        self.Vac_Chamb_Pressure_Sensr_type_Input.setStyleSheet(u"")

        self.verticalLayout_88.addWidget(self.Vac_Chamb_Pressure_Sensr_type_Input)

        self.Vac_Chamb_Pressure_Snsr_No_Slope_Eqn_Input = QLineEdit(self.frame_83)
        self.Vac_Chamb_Pressure_Snsr_No_Slope_Eqn_Input.setObjectName(u"Vac_Chamb_Pressure_Snsr_No_Slope_Eqn_Input")
        self.Vac_Chamb_Pressure_Snsr_No_Slope_Eqn_Input.setMinimumSize(QSize(450, 55))
        self.Vac_Chamb_Pressure_Snsr_No_Slope_Eqn_Input.setMaximumSize(QSize(16777215, 55))
        self.Vac_Chamb_Pressure_Snsr_No_Slope_Eqn_Input.setStyleSheet(u"")

        self.verticalLayout_88.addWidget(self.Vac_Chamb_Pressure_Snsr_No_Slope_Eqn_Input)

        self.Vac_Chamb_Pressure_Snsr_range_Input = QLineEdit(self.frame_83)
        self.Vac_Chamb_Pressure_Snsr_range_Input.setObjectName(u"Vac_Chamb_Pressure_Snsr_range_Input")
        self.Vac_Chamb_Pressure_Snsr_range_Input.setMinimumSize(QSize(450, 55))
        self.Vac_Chamb_Pressure_Snsr_range_Input.setMaximumSize(QSize(16777215, 55))
        self.Vac_Chamb_Pressure_Snsr_range_Input.setStyleSheet(u"")

        self.verticalLayout_88.addWidget(self.Vac_Chamb_Pressure_Snsr_range_Input)

        self.Vac_Chamb_Pressure_Snsr_IO_Input = QLineEdit(self.frame_83)
        self.Vac_Chamb_Pressure_Snsr_IO_Input.setObjectName(u"Vac_Chamb_Pressure_Snsr_IO_Input")
        self.Vac_Chamb_Pressure_Snsr_IO_Input.setMinimumSize(QSize(450, 55))
        self.Vac_Chamb_Pressure_Snsr_IO_Input.setMaximumSize(QSize(16777215, 55))
        self.Vac_Chamb_Pressure_Snsr_IO_Input.setStyleSheet(u"")

        self.verticalLayout_88.addWidget(self.Vac_Chamb_Pressure_Snsr_IO_Input)


        self.horizontalLayout_69.addWidget(self.frame_83)


        self.gridLayout_31.addWidget(self.cyc1SubFrame_2, 0, 1, 1, 1)

        self.horizontalSpacer_35 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_31.addItem(self.horizontalSpacer_35, 0, 0, 1, 1)

        self.cycleTabWidgetPressureSensor.addTab(self.cycle1_2, "")
        self.cycle2_2 = QWidget()
        self.cycle2_2.setObjectName(u"cycle2_2")
        self.gridLayout_34 = QGridLayout(self.cycle2_2)
        self.gridLayout_34.setObjectName(u"gridLayout_34")
        self.gridLayout_34.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_70 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_34.addItem(self.horizontalSpacer_70, 0, 2, 1, 1)

        self.cyc2SubFrame_2 = QFrame(self.cycle2_2)
        self.cyc2SubFrame_2.setObjectName(u"cyc2SubFrame_2")
        self.cyc2SubFrame_2.setFrameShape(QFrame.Shape.StyledPanel)
        self.cyc2SubFrame_2.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_70 = QHBoxLayout(self.cyc2SubFrame_2)
        self.horizontalLayout_70.setSpacing(6)
        self.horizontalLayout_70.setObjectName(u"horizontalLayout_70")
        self.horizontalLayout_70.setContentsMargins(0, 0, 0, 0)
        self.frame_84 = QFrame(self.cyc2SubFrame_2)
        self.frame_84.setObjectName(u"frame_84")
        self.frame_84.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_84.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_89 = QVBoxLayout(self.frame_84)
        self.verticalLayout_89.setSpacing(20)
        self.verticalLayout_89.setObjectName(u"verticalLayout_89")
        self.verticalLayout_89.setContentsMargins(0, 0, 0, 0)
        self.Prop_Tank_Pressure_Sensr_type = QLabel(self.frame_84)
        self.Prop_Tank_Pressure_Sensr_type.setObjectName(u"Prop_Tank_Pressure_Sensr_type")
        self.Prop_Tank_Pressure_Sensr_type.setMinimumSize(QSize(450, 55))
        self.Prop_Tank_Pressure_Sensr_type.setMaximumSize(QSize(16777215, 55))
        self.Prop_Tank_Pressure_Sensr_type.setStyleSheet(u"")
        self.Prop_Tank_Pressure_Sensr_type.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_89.addWidget(self.Prop_Tank_Pressure_Sensr_type)

        self.Prop_Tank__Pressure_Snsr_No_Slope_Eqn = QLabel(self.frame_84)
        self.Prop_Tank__Pressure_Snsr_No_Slope_Eqn.setObjectName(u"Prop_Tank__Pressure_Snsr_No_Slope_Eqn")
        self.Prop_Tank__Pressure_Snsr_No_Slope_Eqn.setMinimumSize(QSize(300, 55))
        self.Prop_Tank__Pressure_Snsr_No_Slope_Eqn.setMaximumSize(QSize(16777215, 55))
        self.Prop_Tank__Pressure_Snsr_No_Slope_Eqn.setStyleSheet(u"")
        self.Prop_Tank__Pressure_Snsr_No_Slope_Eqn.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_89.addWidget(self.Prop_Tank__Pressure_Snsr_No_Slope_Eqn)

        self.Prop_Tank_Pressure_Snsr_range = QLabel(self.frame_84)
        self.Prop_Tank_Pressure_Snsr_range.setObjectName(u"Prop_Tank_Pressure_Snsr_range")
        self.Prop_Tank_Pressure_Snsr_range.setMinimumSize(QSize(450, 55))
        self.Prop_Tank_Pressure_Snsr_range.setMaximumSize(QSize(16777215, 55))
        self.Prop_Tank_Pressure_Snsr_range.setStyleSheet(u"")
        self.Prop_Tank_Pressure_Snsr_range.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_89.addWidget(self.Prop_Tank_Pressure_Snsr_range)

        self.Prop_Tank__Pressure_Snsr_IO = QLabel(self.frame_84)
        self.Prop_Tank__Pressure_Snsr_IO.setObjectName(u"Prop_Tank__Pressure_Snsr_IO")
        self.Prop_Tank__Pressure_Snsr_IO.setMinimumSize(QSize(450, 55))
        self.Prop_Tank__Pressure_Snsr_IO.setMaximumSize(QSize(16777215, 55))
        self.Prop_Tank__Pressure_Snsr_IO.setStyleSheet(u"")
        self.Prop_Tank__Pressure_Snsr_IO.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_89.addWidget(self.Prop_Tank__Pressure_Snsr_IO)


        self.horizontalLayout_70.addWidget(self.frame_84)

        self.frame_85 = QFrame(self.cyc2SubFrame_2)
        self.frame_85.setObjectName(u"frame_85")
        self.frame_85.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_85.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_90 = QVBoxLayout(self.frame_85)
        self.verticalLayout_90.setSpacing(20)
        self.verticalLayout_90.setObjectName(u"verticalLayout_90")
        self.verticalLayout_90.setContentsMargins(0, 0, 0, 0)
        self.Prop_Tank_Pressure_Sensr_type_Input = QLineEdit(self.frame_85)
        self.Prop_Tank_Pressure_Sensr_type_Input.setObjectName(u"Prop_Tank_Pressure_Sensr_type_Input")
        self.Prop_Tank_Pressure_Sensr_type_Input.setMinimumSize(QSize(450, 55))
        self.Prop_Tank_Pressure_Sensr_type_Input.setMaximumSize(QSize(16777215, 55))
        self.Prop_Tank_Pressure_Sensr_type_Input.setStyleSheet(u"")

        self.verticalLayout_90.addWidget(self.Prop_Tank_Pressure_Sensr_type_Input)

        self.Prop_Tank__Pressure_Snsr_No_Slope_Eqn_Input = QLineEdit(self.frame_85)
        self.Prop_Tank__Pressure_Snsr_No_Slope_Eqn_Input.setObjectName(u"Prop_Tank__Pressure_Snsr_No_Slope_Eqn_Input")
        self.Prop_Tank__Pressure_Snsr_No_Slope_Eqn_Input.setMinimumSize(QSize(450, 55))
        self.Prop_Tank__Pressure_Snsr_No_Slope_Eqn_Input.setMaximumSize(QSize(16777215, 55))
        self.Prop_Tank__Pressure_Snsr_No_Slope_Eqn_Input.setStyleSheet(u"")

        self.verticalLayout_90.addWidget(self.Prop_Tank__Pressure_Snsr_No_Slope_Eqn_Input)

        self.Prop_Tank_Pressure_Snsr_range_Input = QLineEdit(self.frame_85)
        self.Prop_Tank_Pressure_Snsr_range_Input.setObjectName(u"Prop_Tank_Pressure_Snsr_range_Input")
        self.Prop_Tank_Pressure_Snsr_range_Input.setMinimumSize(QSize(450, 55))
        self.Prop_Tank_Pressure_Snsr_range_Input.setMaximumSize(QSize(16777215, 55))
        self.Prop_Tank_Pressure_Snsr_range_Input.setStyleSheet(u"")

        self.verticalLayout_90.addWidget(self.Prop_Tank_Pressure_Snsr_range_Input)

        self.Prop_Tank__Pressure_Snsr_IO_Input = QLineEdit(self.frame_85)
        self.Prop_Tank__Pressure_Snsr_IO_Input.setObjectName(u"Prop_Tank__Pressure_Snsr_IO_Input")
        self.Prop_Tank__Pressure_Snsr_IO_Input.setMinimumSize(QSize(450, 55))
        self.Prop_Tank__Pressure_Snsr_IO_Input.setMaximumSize(QSize(16777215, 55))
        self.Prop_Tank__Pressure_Snsr_IO_Input.setStyleSheet(u"")

        self.verticalLayout_90.addWidget(self.Prop_Tank__Pressure_Snsr_IO_Input)


        self.horizontalLayout_70.addWidget(self.frame_85)


        self.gridLayout_34.addWidget(self.cyc2SubFrame_2, 0, 1, 1, 1)

        self.horizontalSpacer_71 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_34.addItem(self.horizontalSpacer_71, 0, 0, 1, 1)

        self.cycleTabWidgetPressureSensor.addTab(self.cycle2_2, "")
        self.cycle3_2 = QWidget()
        self.cycle3_2.setObjectName(u"cycle3_2")
        self.gridLayout_42 = QGridLayout(self.cycle3_2)
        self.gridLayout_42.setObjectName(u"gridLayout_42")
        self.gridLayout_42.setContentsMargins(0, 0, 0, 0)
        self.cyc3SubFrame_2 = QFrame(self.cycle3_2)
        self.cyc3SubFrame_2.setObjectName(u"cyc3SubFrame_2")
        self.cyc3SubFrame_2.setFrameShape(QFrame.Shape.StyledPanel)
        self.cyc3SubFrame_2.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_71 = QHBoxLayout(self.cyc3SubFrame_2)
        self.horizontalLayout_71.setObjectName(u"horizontalLayout_71")
        self.horizontalLayout_71.setContentsMargins(0, 0, 0, 0)
        self.frame_86 = QFrame(self.cyc3SubFrame_2)
        self.frame_86.setObjectName(u"frame_86")
        self.frame_86.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_86.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_91 = QVBoxLayout(self.frame_86)
        self.verticalLayout_91.setSpacing(20)
        self.verticalLayout_91.setObjectName(u"verticalLayout_91")
        self.verticalLayout_91.setContentsMargins(0, 0, 0, 0)
        self.Thruster_Pressure_Sensr_type = QLabel(self.frame_86)
        self.Thruster_Pressure_Sensr_type.setObjectName(u"Thruster_Pressure_Sensr_type")
        self.Thruster_Pressure_Sensr_type.setMinimumSize(QSize(450, 55))
        self.Thruster_Pressure_Sensr_type.setMaximumSize(QSize(16777215, 55))
        self.Thruster_Pressure_Sensr_type.setStyleSheet(u"")
        self.Thruster_Pressure_Sensr_type.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_91.addWidget(self.Thruster_Pressure_Sensr_type)

        self.Thruster_Pressure_Snsr_No_Slope_Eqn = QLabel(self.frame_86)
        self.Thruster_Pressure_Snsr_No_Slope_Eqn.setObjectName(u"Thruster_Pressure_Snsr_No_Slope_Eqn")
        self.Thruster_Pressure_Snsr_No_Slope_Eqn.setMinimumSize(QSize(300, 55))
        self.Thruster_Pressure_Snsr_No_Slope_Eqn.setMaximumSize(QSize(16777215, 55))
        self.Thruster_Pressure_Snsr_No_Slope_Eqn.setStyleSheet(u"")
        self.Thruster_Pressure_Snsr_No_Slope_Eqn.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_91.addWidget(self.Thruster_Pressure_Snsr_No_Slope_Eqn)

        self.Thruster_Pressure_Snsr_range = QLabel(self.frame_86)
        self.Thruster_Pressure_Snsr_range.setObjectName(u"Thruster_Pressure_Snsr_range")
        self.Thruster_Pressure_Snsr_range.setMinimumSize(QSize(300, 55))
        self.Thruster_Pressure_Snsr_range.setMaximumSize(QSize(16777215, 55))
        self.Thruster_Pressure_Snsr_range.setStyleSheet(u"")
        self.Thruster_Pressure_Snsr_range.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_91.addWidget(self.Thruster_Pressure_Snsr_range)

        self.Thruster_Pressure_Snsr_IO = QLabel(self.frame_86)
        self.Thruster_Pressure_Snsr_IO.setObjectName(u"Thruster_Pressure_Snsr_IO")
        self.Thruster_Pressure_Snsr_IO.setMinimumSize(QSize(300, 55))
        self.Thruster_Pressure_Snsr_IO.setMaximumSize(QSize(16777215, 55))
        self.Thruster_Pressure_Snsr_IO.setStyleSheet(u"")
        self.Thruster_Pressure_Snsr_IO.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_91.addWidget(self.Thruster_Pressure_Snsr_IO)


        self.horizontalLayout_71.addWidget(self.frame_86)

        self.frame_87 = QFrame(self.cyc3SubFrame_2)
        self.frame_87.setObjectName(u"frame_87")
        self.frame_87.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_87.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_92 = QVBoxLayout(self.frame_87)
        self.verticalLayout_92.setSpacing(20)
        self.verticalLayout_92.setObjectName(u"verticalLayout_92")
        self.verticalLayout_92.setContentsMargins(0, 0, 0, 0)
        self.Thruster_Pressure_Sensr_type_Input = QLineEdit(self.frame_87)
        self.Thruster_Pressure_Sensr_type_Input.setObjectName(u"Thruster_Pressure_Sensr_type_Input")
        self.Thruster_Pressure_Sensr_type_Input.setMinimumSize(QSize(450, 55))
        self.Thruster_Pressure_Sensr_type_Input.setMaximumSize(QSize(16777215, 55))
        self.Thruster_Pressure_Sensr_type_Input.setStyleSheet(u"")

        self.verticalLayout_92.addWidget(self.Thruster_Pressure_Sensr_type_Input)

        self.Thruster_Pressure_Snsr_No_Slope_Eqn_Input = QLineEdit(self.frame_87)
        self.Thruster_Pressure_Snsr_No_Slope_Eqn_Input.setObjectName(u"Thruster_Pressure_Snsr_No_Slope_Eqn_Input")
        self.Thruster_Pressure_Snsr_No_Slope_Eqn_Input.setMinimumSize(QSize(450, 55))
        self.Thruster_Pressure_Snsr_No_Slope_Eqn_Input.setMaximumSize(QSize(16777215, 55))
        self.Thruster_Pressure_Snsr_No_Slope_Eqn_Input.setStyleSheet(u"")

        self.verticalLayout_92.addWidget(self.Thruster_Pressure_Snsr_No_Slope_Eqn_Input)

        self.Thruster_Pressure_Snsr_range_Input = QLineEdit(self.frame_87)
        self.Thruster_Pressure_Snsr_range_Input.setObjectName(u"Thruster_Pressure_Snsr_range_Input")
        self.Thruster_Pressure_Snsr_range_Input.setMinimumSize(QSize(450, 55))
        self.Thruster_Pressure_Snsr_range_Input.setMaximumSize(QSize(16777215, 55))
        self.Thruster_Pressure_Snsr_range_Input.setStyleSheet(u"")

        self.verticalLayout_92.addWidget(self.Thruster_Pressure_Snsr_range_Input)

        self.Thruster_Pressure_Snsr_IO_Input = QLineEdit(self.frame_87)
        self.Thruster_Pressure_Snsr_IO_Input.setObjectName(u"Thruster_Pressure_Snsr_IO_Input")
        self.Thruster_Pressure_Snsr_IO_Input.setMinimumSize(QSize(450, 55))
        self.Thruster_Pressure_Snsr_IO_Input.setMaximumSize(QSize(16777215, 55))
        self.Thruster_Pressure_Snsr_IO_Input.setStyleSheet(u"")

        self.verticalLayout_92.addWidget(self.Thruster_Pressure_Snsr_IO_Input)


        self.horizontalLayout_71.addWidget(self.frame_87)


        self.gridLayout_42.addWidget(self.cyc3SubFrame_2, 0, 1, 1, 1)

        self.horizontalSpacer_87 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_42.addItem(self.horizontalSpacer_87, 0, 0, 1, 1)

        self.horizontalSpacer_88 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_42.addItem(self.horizontalSpacer_88, 0, 2, 1, 1)

        self.cycleTabWidgetPressureSensor.addTab(self.cycle3_2, "")

        self.gridLayout.addWidget(self.cycleTabWidgetPressureSensor, 0, 0, 1, 1)


        self.verticalLayout_8.addWidget(self.frame_81)

        self.subFrameCompDet = QFrame(self.contentFrameCompDet)
        self.subFrameCompDet.setObjectName(u"subFrameCompDet")
        self.subFrameCompDet.setMaximumSize(QSize(16777215, 16777215))
        self.subFrameCompDet.setStyleSheet(u"QLabel{\n"
"	background-color: #333333;\n"
"	border-radius:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QLineEdit, QDateEdit, QDoubleSpinBox{\n"
"	background-color: rgb(84, 103, 163);\n"
"	border:1px solid #9CAECA;\n"
"	border-radius: 5px;\n"
"	padding:5px;\n"
"	font-size: 17px;\n"
"font-family: Arial;\n"
"}\n"
"")
        self.subFrameCompDet.setFrameShape(QFrame.Shape.StyledPanel)
        self.subFrameCompDet.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_41 = QHBoxLayout(self.subFrameCompDet)
        self.horizontalLayout_41.setSpacing(0)
        self.horizontalLayout_41.setObjectName(u"horizontalLayout_41")
        self.horizontalLayout_41.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_15 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_41.addItem(self.horizontalSpacer_15)

        self.frame_38 = QFrame(self.subFrameCompDet)
        self.frame_38.setObjectName(u"frame_38")
        self.frame_38.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_38.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_51 = QVBoxLayout(self.frame_38)
        self.verticalLayout_51.setSpacing(9)
        self.verticalLayout_51.setObjectName(u"verticalLayout_51")
        self.verticalLayout_51.setContentsMargins(35, 0, 0, -1)
        self.subLblHtrType = QLabel(self.frame_38)
        self.subLblHtrType.setObjectName(u"subLblHtrType")
        self.subLblHtrType.setMinimumSize(QSize(450, 55))
        self.subLblHtrType.setMaximumSize(QSize(16777215, 55))
        self.subLblHtrType.setStyleSheet(u"")
        self.subLblHtrType.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_51.addWidget(self.subLblHtrType)

        self.subLblHtrInpPower = QLabel(self.frame_38)
        self.subLblHtrInpPower.setObjectName(u"subLblHtrInpPower")
        self.subLblHtrInpPower.setMinimumSize(QSize(450, 55))
        self.subLblHtrInpPower.setMaximumSize(QSize(16777215, 55))
        self.subLblHtrInpPower.setStyleSheet(u"")
        self.subLblHtrInpPower.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_51.addWidget(self.subLblHtrInpPower)


        self.horizontalLayout_41.addWidget(self.frame_38)

        self.frame_37 = QFrame(self.subFrameCompDet)
        self.frame_37.setObjectName(u"frame_37")
        self.frame_37.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_37.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_50 = QVBoxLayout(self.frame_37)
        self.verticalLayout_50.setSpacing(9)
        self.verticalLayout_50.setObjectName(u"verticalLayout_50")
        self.verticalLayout_50.setContentsMargins(6, 0, 35, -1)
        self.subLnEdtHtrType_2 = QLineEdit(self.frame_37)
        self.subLnEdtHtrType_2.setObjectName(u"subLnEdtHtrType_2")
        self.subLnEdtHtrType_2.setMinimumSize(QSize(450, 55))
        self.subLnEdtHtrType_2.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtHtrType_2.setStyleSheet(u"background-color: rgb(84, 103, 163);\n"
"border:1px solid #9CAECA;\n"
"border-radius: 5px;\n"
"padding:5px;")

        self.verticalLayout_50.addWidget(self.subLnEdtHtrType_2)

        self.subLnEdtHtrInpPower = CustomDoubleSpinBox(self.frame_37)
        self.subLnEdtHtrInpPower.setObjectName(u"subLnEdtHtrInpPower")
        self.subLnEdtHtrInpPower.setMinimumSize(QSize(450, 55))
        self.subLnEdtHtrInpPower.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtHtrInpPower.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtHtrInpPower.setDecimals(6)
        self.subLnEdtHtrInpPower.setMaximum(10000000.000000000000000)
        self.subLnEdtHtrInpPower.setSingleStep(0.000000000000000)
        self.subLnEdtHtrInpPower.setValue(35.000000000000000)

        self.verticalLayout_50.addWidget(self.subLnEdtHtrInpPower)


        self.horizontalLayout_41.addWidget(self.frame_37)

        self.horizontalSpacer_14 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_41.addItem(self.horizontalSpacer_14)


        self.verticalLayout_8.addWidget(self.subFrameCompDet)

        self.verticalSpacer_7 = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_8.addItem(self.verticalSpacer_7)


        self.gridLayout_11.addWidget(self.contentFrameCompDet, 0, 0, 1, 1)

        self.contentStack.addWidget(self.componentDetails)
        self.testDetails = QWidget()
        self.testDetails.setObjectName(u"testDetails")
        self.gridLayout_5 = QGridLayout(self.testDetails)
        self.gridLayout_5.setObjectName(u"gridLayout_5")
        self.gridLayout_5.setContentsMargins(0, 0, 0, 0)
        self.contentFrameTstDet = QFrame(self.testDetails)
        self.contentFrameTstDet.setObjectName(u"contentFrameTstDet")
        self.contentFrameTstDet.setMinimumSize(QSize(1000, 600))
        self.contentFrameTstDet.setMaximumSize(QSize(1200, 600))
        self.contentFrameTstDet.setStyleSheet(u"")
        self.contentFrameTstDet.setFrameShape(QFrame.Shape.NoFrame)
        self.contentFrameTstDet.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_48 = QVBoxLayout(self.contentFrameTstDet)
        self.verticalLayout_48.setObjectName(u"verticalLayout_48")
        self.verticalLayout_48.setContentsMargins(9, 5, -1, -1)
        self.frame_35 = QFrame(self.contentFrameTstDet)
        self.frame_35.setObjectName(u"frame_35")
        self.frame_35.setMinimumSize(QSize(0, 50))
        self.frame_35.setMaximumSize(QSize(16777215, 50))
        self.frame_35.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_35.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_9 = QHBoxLayout(self.frame_35)
        self.horizontalLayout_9.setSpacing(0)
        self.horizontalLayout_9.setObjectName(u"horizontalLayout_9")
        self.horizontalLayout_9.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_16 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_9.addItem(self.horizontalSpacer_16)

        self.subSectnHderTestDet = QLabel(self.frame_35)
        self.subSectnHderTestDet.setObjectName(u"subSectnHderTestDet")
        self.subSectnHderTestDet.setMinimumSize(QSize(150, 45))
        self.subSectnHderTestDet.setMaximumSize(QSize(16777215, 16777215))
        self.subSectnHderTestDet.setFont(font3)
        self.subSectnHderTestDet.setStyleSheet(u"background-color: #252525;\n"
"border-radius: 17px;\n"
"padding:5px;\n"
"border: 2px solid red;\n"
"")
        self.subSectnHderTestDet.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_9.addWidget(self.subSectnHderTestDet)

        self.horizontalSpacer_49 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_9.addItem(self.horizontalSpacer_49)


        self.verticalLayout_48.addWidget(self.frame_35)

        self.verticalSpacer_3 = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_48.addItem(self.verticalSpacer_3)

        self.subFrameTestDet = QFrame(self.contentFrameTstDet)
        self.subFrameTestDet.setObjectName(u"subFrameTestDet")
        self.subFrameTestDet.setStyleSheet(u"QLabel{\n"
"	background-color: #333333;\n"
"	border-radius:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QLineEdit, QDateEdit, QDoubleSpinBox, QPlainTextEdit{\n"
"	background-color: rgb(84, 103, 163);\n"
"	border:1px solid #9CAECA;\n"
"	border-radius: 5px;\n"
"	padding:5px;\n"
"	font-size: 17px;\n"
"font-family: Arial;\n"
"}")
        self.subFrameTestDet.setFrameShape(QFrame.Shape.StyledPanel)
        self.subFrameTestDet.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_40 = QHBoxLayout(self.subFrameTestDet)
        self.horizontalLayout_40.setSpacing(20)
        self.horizontalLayout_40.setObjectName(u"horizontalLayout_40")
        self.horizontalSpacer_17 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_40.addItem(self.horizontalSpacer_17)

        self.frame_36 = QFrame(self.subFrameTestDet)
        self.frame_36.setObjectName(u"frame_36")
        self.frame_36.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_36.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_49 = QVBoxLayout(self.frame_36)
        self.verticalLayout_49.setSpacing(30)
        self.verticalLayout_49.setObjectName(u"verticalLayout_49")
        self.verticalLayout_49.setContentsMargins(0, 0, 0, 0)
        self.subLblPropTnkHtrCtOffTemp = QLabel(self.frame_36)
        self.subLblPropTnkHtrCtOffTemp.setObjectName(u"subLblPropTnkHtrCtOffTemp")
        self.subLblPropTnkHtrCtOffTemp.setMinimumSize(QSize(450, 55))
        self.subLblPropTnkHtrCtOffTemp.setMaximumSize(QSize(16777215, 55))
        self.subLblPropTnkHtrCtOffTemp.setStyleSheet(u"")
        self.subLblPropTnkHtrCtOffTemp.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_49.addWidget(self.subLblPropTnkHtrCtOffTemp)

        self.subLblPropTnkHtrRstTmp = QLabel(self.frame_36)
        self.subLblPropTnkHtrRstTmp.setObjectName(u"subLblPropTnkHtrRstTmp")
        self.subLblPropTnkHtrRstTmp.setMinimumSize(QSize(450, 55))
        self.subLblPropTnkHtrRstTmp.setMaximumSize(QSize(16777215, 55))
        self.subLblPropTnkHtrRstTmp.setStyleSheet(u"")
        self.subLblPropTnkHtrRstTmp.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_49.addWidget(self.subLblPropTnkHtrRstTmp)

        self.subLblTestProc = QLabel(self.frame_36)
        self.subLblTestProc.setObjectName(u"subLblTestProc")
        self.subLblTestProc.setMinimumSize(QSize(450, 55))
        self.subLblTestProc.setMaximumSize(QSize(16777215, 55))
        self.subLblTestProc.setStyleSheet(u"")
        self.subLblTestProc.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_49.addWidget(self.subLblTestProc)

        self.verticalSpacer_24 = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_49.addItem(self.verticalSpacer_24)


        self.horizontalLayout_40.addWidget(self.frame_36)

        self.frame_50 = QFrame(self.subFrameTestDet)
        self.frame_50.setObjectName(u"frame_50")
        self.frame_50.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_50.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_63 = QVBoxLayout(self.frame_50)
        self.verticalLayout_63.setSpacing(30)
        self.verticalLayout_63.setObjectName(u"verticalLayout_63")
        self.verticalLayout_63.setContentsMargins(0, 0, 0, 0)
        self.subLnEdtPropTnkHtrCtOfTemp = CustomDoubleSpinBox(self.frame_50)
        self.subLnEdtPropTnkHtrCtOfTemp.setObjectName(u"subLnEdtPropTnkHtrCtOfTemp")
        self.subLnEdtPropTnkHtrCtOfTemp.setMinimumSize(QSize(450, 55))
        self.subLnEdtPropTnkHtrCtOfTemp.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtPropTnkHtrCtOfTemp.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtPropTnkHtrCtOfTemp.setDecimals(6)
        self.subLnEdtPropTnkHtrCtOfTemp.setMaximum(10000000.000000000000000)
        self.subLnEdtPropTnkHtrCtOfTemp.setSingleStep(0.000000000000000)

        self.verticalLayout_63.addWidget(self.subLnEdtPropTnkHtrCtOfTemp)

        self.subLnEdtPropTnkHtrRstTemp = CustomDoubleSpinBox(self.frame_50)
        self.subLnEdtPropTnkHtrRstTemp.setObjectName(u"subLnEdtPropTnkHtrRstTemp")
        self.subLnEdtPropTnkHtrRstTemp.setMinimumSize(QSize(450, 55))
        self.subLnEdtPropTnkHtrRstTemp.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtPropTnkHtrRstTemp.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtPropTnkHtrRstTemp.setDecimals(6)
        self.subLnEdtPropTnkHtrRstTemp.setMaximum(10000000.000000000000000)
        self.subLnEdtPropTnkHtrRstTemp.setSingleStep(0.000000000000000)

        self.verticalLayout_63.addWidget(self.subLnEdtPropTnkHtrRstTemp)

        self.subLblTestProcValue = QPlainTextEdit(self.frame_50)
        self.subLblTestProcValue.setObjectName(u"subLblTestProcValue")
        self.subLblTestProcValue.setMinimumSize(QSize(450, 200))
        self.subLblTestProcValue.setMaximumSize(QSize(16777215, 200))

        self.verticalLayout_63.addWidget(self.subLblTestProcValue)

        self.verticalSpacer_25 = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_63.addItem(self.verticalSpacer_25)


        self.horizontalLayout_40.addWidget(self.frame_50)

        self.horizontalSpacer_18 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_40.addItem(self.horizontalSpacer_18)


        self.verticalLayout_48.addWidget(self.subFrameTestDet)

        self.verticalSpacer_4 = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_48.addItem(self.verticalSpacer_4)


        self.gridLayout_5.addWidget(self.contentFrameTstDet, 0, 0, 1, 1)

        self.contentStack.addWidget(self.testDetails)
        self.heaterInformation = QWidget()
        self.heaterInformation.setObjectName(u"heaterInformation")
        self.gridLayout_3 = QGridLayout(self.heaterInformation)
        self.gridLayout_3.setSpacing(0)
        self.gridLayout_3.setObjectName(u"gridLayout_3")
        self.gridLayout_3.setContentsMargins(0, 0, 0, 0)
        self.contentFrameHtrInfo = QFrame(self.heaterInformation)
        self.contentFrameHtrInfo.setObjectName(u"contentFrameHtrInfo")
        self.contentFrameHtrInfo.setMinimumSize(QSize(1000, 500))
        self.contentFrameHtrInfo.setMaximumSize(QSize(1200, 500))
        self.contentFrameHtrInfo.setStyleSheet(u"")
        self.contentFrameHtrInfo.setFrameShape(QFrame.Shape.NoFrame)
        self.contentFrameHtrInfo.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_10 = QVBoxLayout(self.contentFrameHtrInfo)
        self.verticalLayout_10.setSpacing(0)
        self.verticalLayout_10.setObjectName(u"verticalLayout_10")
        self.verticalLayout_10.setContentsMargins(0, 0, 0, 0)
        self.frame_15 = QFrame(self.contentFrameHtrInfo)
        self.frame_15.setObjectName(u"frame_15")
        self.frame_15.setMinimumSize(QSize(0, 50))
        self.frame_15.setMaximumSize(QSize(16777215, 50))
        self.frame_15.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_15.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_10 = QHBoxLayout(self.frame_15)
        self.horizontalLayout_10.setSpacing(0)
        self.horizontalLayout_10.setObjectName(u"horizontalLayout_10")
        self.horizontalLayout_10.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_19 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_10.addItem(self.horizontalSpacer_19)

        self.subSecHtrInfo = QLabel(self.frame_15)
        self.subSecHtrInfo.setObjectName(u"subSecHtrInfo")
        self.subSecHtrInfo.setMinimumSize(QSize(200, 45))
        self.subSecHtrInfo.setMaximumSize(QSize(16777215, 16777215))
        self.subSecHtrInfo.setFont(font3)
        self.subSecHtrInfo.setStyleSheet(u"background-color: #252525;\n"
"border-radius: 17px;\n"
"padding:5px;\n"
"border: 2px solid red;\n"
"")
        self.subSecHtrInfo.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_10.addWidget(self.subSecHtrInfo)

        self.horizontalSpacer_50 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_10.addItem(self.horizontalSpacer_50)


        self.verticalLayout_10.addWidget(self.frame_15)

        self.subFrameHtrInfo = QFrame(self.contentFrameHtrInfo)
        self.subFrameHtrInfo.setObjectName(u"subFrameHtrInfo")
        self.subFrameHtrInfo.setStyleSheet(u"QLabel{\n"
"	background-color: #333333;\n"
"	border-radius:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QLineEdit, QDateEdit, QDoubleSpinBox{\n"
"	background-color: rgb(84, 103, 163);\n"
"	border:1px solid #9CAECA;\n"
"	border-radius: 5px;\n"
"	padding:5px;\n"
"	font-size: 17px;\n"
"font-family: Arial;\n"
"}\n"
"")
        self.subFrameHtrInfo.setFrameShape(QFrame.Shape.StyledPanel)
        self.subFrameHtrInfo.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_39 = QHBoxLayout(self.subFrameHtrInfo)
        self.horizontalLayout_39.setSpacing(0)
        self.horizontalLayout_39.setObjectName(u"horizontalLayout_39")
        self.horizontalSpacer_21 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_39.addItem(self.horizontalSpacer_21)

        self.frame_33 = QFrame(self.subFrameHtrInfo)
        self.frame_33.setObjectName(u"frame_33")
        self.frame_33.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_33.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_46 = QVBoxLayout(self.frame_33)
        self.verticalLayout_46.setObjectName(u"verticalLayout_46")
        self.subLblHtrTyp = QLabel(self.frame_33)
        self.subLblHtrTyp.setObjectName(u"subLblHtrTyp")
        self.subLblHtrTyp.setMinimumSize(QSize(450, 55))
        self.subLblHtrTyp.setMaximumSize(QSize(16777215, 55))
        self.subLblHtrTyp.setStyleSheet(u"")
        self.subLblHtrTyp.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_46.addWidget(self.subLblHtrTyp)

        self.subLblHtrInp = QLabel(self.frame_33)
        self.subLblHtrInp.setObjectName(u"subLblHtrInp")
        self.subLblHtrInp.setMinimumSize(QSize(450, 55))
        self.subLblHtrInp.setMaximumSize(QSize(16777215, 55))
        self.subLblHtrInp.setStyleSheet(u"")
        self.subLblHtrInp.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_46.addWidget(self.subLblHtrInp)

        self.subLblHtrCtOfTemp = QLabel(self.frame_33)
        self.subLblHtrCtOfTemp.setObjectName(u"subLblHtrCtOfTemp")
        self.subLblHtrCtOfTemp.setMinimumSize(QSize(450, 55))
        self.subLblHtrCtOfTemp.setMaximumSize(QSize(16777215, 55))
        self.subLblHtrCtOfTemp.setStyleSheet(u"")
        self.subLblHtrCtOfTemp.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_46.addWidget(self.subLblHtrCtOfTemp)

        self.subLblHtrRstTemp = QLabel(self.frame_33)
        self.subLblHtrRstTemp.setObjectName(u"subLblHtrRstTemp")
        self.subLblHtrRstTemp.setMinimumSize(QSize(450, 55))
        self.subLblHtrRstTemp.setMaximumSize(QSize(16777215, 55))
        self.subLblHtrRstTemp.setStyleSheet(u"")
        self.subLblHtrRstTemp.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_46.addWidget(self.subLblHtrRstTemp)


        self.horizontalLayout_39.addWidget(self.frame_33)

        self.frame_34 = QFrame(self.subFrameHtrInfo)
        self.frame_34.setObjectName(u"frame_34")
        self.frame_34.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_34.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_47 = QVBoxLayout(self.frame_34)
        self.verticalLayout_47.setObjectName(u"verticalLayout_47")
        self.subLnEdtHtrType = QLineEdit(self.frame_34)
        self.subLnEdtHtrType.setObjectName(u"subLnEdtHtrType")
        self.subLnEdtHtrType.setMinimumSize(QSize(470, 55))
        self.subLnEdtHtrType.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtHtrType.setStyleSheet(u"background-color: rgb(84, 103, 163);\n"
"border:1px solid #9CAECA;\n"
"border-radius: 5px;\n"
"padding:5px;")

        self.verticalLayout_47.addWidget(self.subLnEdtHtrType)

        self.frame_88 = QFrame(self.frame_34)
        self.frame_88.setObjectName(u"frame_88")
        self.frame_88.setMinimumSize(QSize(0, 55))
        self.frame_88.setMaximumSize(QSize(16777215, 55))
        self.frame_88.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_88.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_72 = QHBoxLayout(self.frame_88)
        self.horizontalLayout_72.setSpacing(0)
        self.horizontalLayout_72.setObjectName(u"horizontalLayout_72")
        self.horizontalLayout_72.setContentsMargins(0, 0, 0, 0)
        self.subLblHtrInpVoltage = QLabel(self.frame_88)
        self.subLblHtrInpVoltage.setObjectName(u"subLblHtrInpVoltage")
        self.subLblHtrInpVoltage.setMinimumSize(QSize(80, 0))
        self.subLblHtrInpVoltage.setStyleSheet(u"background-color:#1f2937;")
        self.subLblHtrInpVoltage.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_72.addWidget(self.subLblHtrInpVoltage)

        self.subLnEdtHtrInpVoltage = CustomDoubleSpinBox(self.frame_88)
        self.subLnEdtHtrInpVoltage.setObjectName(u"subLnEdtHtrInpVoltage")
        self.subLnEdtHtrInpVoltage.setMinimumSize(QSize(105, 45))
        self.subLnEdtHtrInpVoltage.setMaximumSize(QSize(105, 45))
        self.subLnEdtHtrInpVoltage.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtHtrInpVoltage.setDecimals(6)
        self.subLnEdtHtrInpVoltage.setMaximum(10000000.000000000000000)
        self.subLnEdtHtrInpVoltage.setSingleStep(0.000000000000000)

        self.horizontalLayout_72.addWidget(self.subLnEdtHtrInpVoltage)

        self.subLblHtrInpCurrent = QLabel(self.frame_88)
        self.subLblHtrInpCurrent.setObjectName(u"subLblHtrInpCurrent")
        self.subLblHtrInpCurrent.setMinimumSize(QSize(80, 0))
        self.subLblHtrInpCurrent.setStyleSheet(u"background-color:#1f2937;")
        self.subLblHtrInpCurrent.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_72.addWidget(self.subLblHtrInpCurrent)

        self.subLnEdtHtrInpCurrent = CustomDoubleSpinBox(self.frame_88)
        self.subLnEdtHtrInpCurrent.setObjectName(u"subLnEdtHtrInpCurrent")
        self.subLnEdtHtrInpCurrent.setMinimumSize(QSize(105, 45))
        self.subLnEdtHtrInpCurrent.setMaximumSize(QSize(105, 45))
        self.subLnEdtHtrInpCurrent.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtHtrInpCurrent.setDecimals(6)
        self.subLnEdtHtrInpCurrent.setMaximum(10000000.000000000000000)
        self.subLnEdtHtrInpCurrent.setSingleStep(0.000000000000000)

        self.horizontalLayout_72.addWidget(self.subLnEdtHtrInpCurrent)

        self.subLblHtrInpWattage = QLabel(self.frame_88)
        self.subLblHtrInpWattage.setObjectName(u"subLblHtrInpWattage")
        self.subLblHtrInpWattage.setMinimumSize(QSize(85, 0))
        self.subLblHtrInpWattage.setStyleSheet(u"background-color:#1f2937;")
        self.subLblHtrInpWattage.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_72.addWidget(self.subLblHtrInpWattage)

        self.subLnEdtHtrInpWattage = CustomDoubleSpinBox(self.frame_88)
        self.subLnEdtHtrInpWattage.setObjectName(u"subLnEdtHtrInpWattage")
        self.subLnEdtHtrInpWattage.setMinimumSize(QSize(105, 45))
        self.subLnEdtHtrInpWattage.setMaximumSize(QSize(105, 45))
        self.subLnEdtHtrInpWattage.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtHtrInpWattage.setDecimals(6)
        self.subLnEdtHtrInpWattage.setMaximum(10000000.000000000000000)
        self.subLnEdtHtrInpWattage.setSingleStep(0.000000000000000)

        self.horizontalLayout_72.addWidget(self.subLnEdtHtrInpWattage)


        self.verticalLayout_47.addWidget(self.frame_88)

        self.subLnEdtHtrCtOfTemp = CustomDoubleSpinBox(self.frame_34)
        self.subLnEdtHtrCtOfTemp.setObjectName(u"subLnEdtHtrCtOfTemp")
        self.subLnEdtHtrCtOfTemp.setMinimumSize(QSize(450, 55))
        self.subLnEdtHtrCtOfTemp.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtHtrCtOfTemp.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtHtrCtOfTemp.setDecimals(6)
        self.subLnEdtHtrCtOfTemp.setMaximum(10000000.000000000000000)
        self.subLnEdtHtrCtOfTemp.setSingleStep(0.000000000000000)

        self.verticalLayout_47.addWidget(self.subLnEdtHtrCtOfTemp)

        self.subLnEdtHtrRstTemp = CustomDoubleSpinBox(self.frame_34)
        self.subLnEdtHtrRstTemp.setObjectName(u"subLnEdtHtrRstTemp")
        self.subLnEdtHtrRstTemp.setMinimumSize(QSize(450, 55))
        self.subLnEdtHtrRstTemp.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtHtrRstTemp.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtHtrRstTemp.setDecimals(6)
        self.subLnEdtHtrRstTemp.setMaximum(10000000.000000000000000)
        self.subLnEdtHtrRstTemp.setSingleStep(0.000000000000000)

        self.verticalLayout_47.addWidget(self.subLnEdtHtrRstTemp)


        self.horizontalLayout_39.addWidget(self.frame_34)

        self.horizontalSpacer_20 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_39.addItem(self.horizontalSpacer_20)


        self.verticalLayout_10.addWidget(self.subFrameHtrInfo)


        self.gridLayout_3.addWidget(self.contentFrameHtrInfo, 0, 0, 1, 1)

        self.contentStack.addWidget(self.heaterInformation)
        self.heaterCycles = QWidget()
        self.heaterCycles.setObjectName(u"heaterCycles")
        self.gridLayout_26 = QGridLayout(self.heaterCycles)
        self.gridLayout_26.setObjectName(u"gridLayout_26")
        self.gridLayout_26.setContentsMargins(0, -1, 0, -1)
        self.contentFrameHtrCyc = QFrame(self.heaterCycles)
        self.contentFrameHtrCyc.setObjectName(u"contentFrameHtrCyc")
        self.contentFrameHtrCyc.setMinimumSize(QSize(1000, 600))
        self.contentFrameHtrCyc.setMaximumSize(QSize(1200, 600))
        self.contentFrameHtrCyc.setStyleSheet(u"")
        self.contentFrameHtrCyc.setFrameShape(QFrame.Shape.NoFrame)
        self.contentFrameHtrCyc.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_15 = QVBoxLayout(self.contentFrameHtrCyc)
        self.verticalLayout_15.setObjectName(u"verticalLayout_15")
        self.verticalLayout_15.setContentsMargins(5, 5, 5, -1)
        self.frame_16 = QFrame(self.contentFrameHtrCyc)
        self.frame_16.setObjectName(u"frame_16")
        self.frame_16.setMinimumSize(QSize(0, 50))
        self.frame_16.setMaximumSize(QSize(16777215, 50))
        self.frame_16.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_16.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_12 = QHBoxLayout(self.frame_16)
        self.horizontalLayout_12.setSpacing(0)
        self.horizontalLayout_12.setObjectName(u"horizontalLayout_12")
        self.horizontalLayout_12.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_22 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_12.addItem(self.horizontalSpacer_22)

        self.subSecHtrCyc = QLabel(self.frame_16)
        self.subSecHtrCyc.setObjectName(u"subSecHtrCyc")
        self.subSecHtrCyc.setMinimumSize(QSize(170, 45))
        self.subSecHtrCyc.setMaximumSize(QSize(16777215, 16777215))
        self.subSecHtrCyc.setFont(font3)
        self.subSecHtrCyc.setStyleSheet(u"background-color: #252525;\n"
"border-radius: 17px;\n"
"padding:5px;\n"
"border: 2px solid red;\n"
"")
        self.subSecHtrCyc.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_12.addWidget(self.subSecHtrCyc)

        self.horizontalSpacer_51 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_12.addItem(self.horizontalSpacer_51)


        self.verticalLayout_15.addWidget(self.frame_16)

        self.subFrameHtrCyc = QFrame(self.contentFrameHtrCyc)
        self.subFrameHtrCyc.setObjectName(u"subFrameHtrCyc")
        self.subFrameHtrCyc.setStyleSheet(u"")
        self.subFrameHtrCyc.setFrameShape(QFrame.Shape.StyledPanel)
        self.subFrameHtrCyc.setFrameShadow(QFrame.Shadow.Raised)
        self.gridLayout_17 = QGridLayout(self.subFrameHtrCyc)
        self.gridLayout_17.setObjectName(u"gridLayout_17")
        self.gridLayout_17.setHorizontalSpacing(60)
        self.gridLayout_17.setVerticalSpacing(10)
        self.gridLayout_17.setContentsMargins(0, 0, 0, 0)
        self.scrollArea_4 = QScrollArea(self.subFrameHtrCyc)
        self.scrollArea_4.setObjectName(u"scrollArea_4")
        self.scrollArea_4.setMinimumSize(QSize(0, 500))
        self.scrollArea_4.setStyleSheet(u"QLabel{\n"
"	background-color: #333333;\n"
"	border-radius:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QLineEdit, QSpinBox, QDoubleSpinBox{\n"
"	background-color: rgb(84, 103, 163);\n"
"	border:1px solid #9CAECA;\n"
"	border-radius: 5px;\n"
"	padding:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"/* Scrollbar styling */\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    width: 12px;\n"
"    margin: 0px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 4px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}")
        self.scrollArea_4.setWidgetResizable(True)
        self.scrollAreaWidgetContents_2 = QWidget()
        self.scrollAreaWidgetContents_2.setObjectName(u"scrollAreaWidgetContents_2")
        self.scrollAreaWidgetContents_2.setGeometry(QRect(0, 0, 982, 681))
        self.gridLayout_13 = QGridLayout(self.scrollAreaWidgetContents_2)
        self.gridLayout_13.setObjectName(u"gridLayout_13")
        self.cycleTabWidget = QTabWidget(self.scrollAreaWidgetContents_2)
        self.cycleTabWidget.setObjectName(u"cycleTabWidget")
        self.cycleTabWidget.setStyleSheet(u"QTabBar {\n"
"                background: #1E2023;\n"
"                border-radius: 8px;\n"
"                margin: 10px 20px;\n"
"            }\n"
"            \n"
"QTabBar::tab {\n"
"           	background: transparent;\n"
"               color: #86868b;\n"
"               border: none;\n"
"               padding: 6px 24px;\n"
"               border-radius: 8px;\n"
"               font-size: 14px;\n"
"               min-width: 90px;\n"
"               height: 36px;\n"
"               margin: 2px;\n"
"            }\n"
"            \n"
"QTabBar::tab:selected {\n"
"               background: #2C2C2E;\n"
"               color: #FFFFFF;\n"
"			   	margin-top:11px;\n"
"				height:18px;\n"
"				margin-left:21px;\n"
"            }\n"
"            \n"
"QTabBar::tab:hover:!selected {\n"
"              	background: rgba(255, 255, 255, 0.1);\n"
"          		color: #FFFFFF;\n"
"				margin-top:11px;\n"
"				height:18px;\n"
"				margin-left:21px;\n"
"            }\n"
"\n"
"QTabWidget {\n"
"           	background: transp"
                        "arent;\n"
"              	border: none;\n"
"            }\n"
"\n"
"QTabWidget::pane {\n"
"          		border: none;\n"
"              	background: transparent;\n"
"             	margin-top: -5px;\n"
"            }\n"
"\n"
"QTabWidget::tab-bar {\n"
"         	   alignment: center;\n"
"            }\n"
"\n"
"QLabel{\n"
"	background-color: #333333;\n"
"	border-radius:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QLineEdit, QTimeEdit, QDoubleSpinBox{\n"
"	background-color: rgb(84, 103, 163);\n"
"	border:1px solid #9CAECA;\n"
"	border-radius: 5px;\n"
"	padding:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"")
        self.cycle1 = QWidget()
        self.cycle1.setObjectName(u"cycle1")
        self.gridLayout_19 = QGridLayout(self.cycle1)
        self.gridLayout_19.setObjectName(u"gridLayout_19")
        self.gridLayout_19.setVerticalSpacing(6)
        self.gridLayout_19.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_23 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_19.addItem(self.horizontalSpacer_23, 0, 0, 1, 1)

        self.cyc1SubFrame = QFrame(self.cycle1)
        self.cyc1SubFrame.setObjectName(u"cyc1SubFrame")
        self.cyc1SubFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.cyc1SubFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_35 = QHBoxLayout(self.cyc1SubFrame)
        self.horizontalLayout_35.setSpacing(6)
        self.horizontalLayout_35.setObjectName(u"horizontalLayout_35")
        self.horizontalLayout_35.setContentsMargins(0, 0, 0, 0)
        self.frame_8 = QFrame(self.cyc1SubFrame)
        self.frame_8.setObjectName(u"frame_8")
        self.frame_8.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_8.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_13 = QVBoxLayout(self.frame_8)
        self.verticalLayout_13.setSpacing(10)
        self.verticalLayout_13.setObjectName(u"verticalLayout_13")
        self.verticalLayout_13.setContentsMargins(0, 0, 0, 0)
        self.cyc1SubLblHtrSwtOnTime = QLabel(self.frame_8)
        self.cyc1SubLblHtrSwtOnTime.setObjectName(u"cyc1SubLblHtrSwtOnTime")
        self.cyc1SubLblHtrSwtOnTime.setMinimumSize(QSize(450, 55))
        self.cyc1SubLblHtrSwtOnTime.setMaximumSize(QSize(16777215, 55))
        self.cyc1SubLblHtrSwtOnTime.setStyleSheet(u"")
        self.cyc1SubLblHtrSwtOnTime.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_13.addWidget(self.cyc1SubLblHtrSwtOnTime)

        self.cyc1SubLblHtrSwtONCorspgTankPressure = QLabel(self.frame_8)
        self.cyc1SubLblHtrSwtONCorspgTankPressure.setObjectName(u"cyc1SubLblHtrSwtONCorspgTankPressure")
        self.cyc1SubLblHtrSwtONCorspgTankPressure.setMinimumSize(QSize(450, 55))
        self.cyc1SubLblHtrSwtONCorspgTankPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc1SubLblHtrSwtONCorspgTankPressure.setStyleSheet(u"")
        self.cyc1SubLblHtrSwtONCorspgTankPressure.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_13.addWidget(self.cyc1SubLblHtrSwtONCorspgTankPressure)

        self.cyc1SubLblHtrSwtONCorspgThrusterPressure = QLabel(self.frame_8)
        self.cyc1SubLblHtrSwtONCorspgThrusterPressure.setObjectName(u"cyc1SubLblHtrSwtONCorspgThrusterPressure")
        self.cyc1SubLblHtrSwtONCorspgThrusterPressure.setMinimumSize(QSize(450, 55))
        self.cyc1SubLblHtrSwtONCorspgThrusterPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc1SubLblHtrSwtONCorspgThrusterPressure.setStyleSheet(u"")
        self.cyc1SubLblHtrSwtONCorspgThrusterPressure.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_13.addWidget(self.cyc1SubLblHtrSwtONCorspgThrusterPressure)

        self.cyc1SubLblHtrSwtOffTime = QLabel(self.frame_8)
        self.cyc1SubLblHtrSwtOffTime.setObjectName(u"cyc1SubLblHtrSwtOffTime")
        self.cyc1SubLblHtrSwtOffTime.setMinimumSize(QSize(450, 55))
        self.cyc1SubLblHtrSwtOffTime.setMaximumSize(QSize(16777215, 55))
        self.cyc1SubLblHtrSwtOffTime.setStyleSheet(u"")
        self.cyc1SubLblHtrSwtOffTime.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_13.addWidget(self.cyc1SubLblHtrSwtOffTime)

        self.cyc1SubLblHtrSwtOFFCorspgTankPressure = QLabel(self.frame_8)
        self.cyc1SubLblHtrSwtOFFCorspgTankPressure.setObjectName(u"cyc1SubLblHtrSwtOFFCorspgTankPressure")
        self.cyc1SubLblHtrSwtOFFCorspgTankPressure.setMinimumSize(QSize(450, 55))
        self.cyc1SubLblHtrSwtOFFCorspgTankPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc1SubLblHtrSwtOFFCorspgTankPressure.setStyleSheet(u"")
        self.cyc1SubLblHtrSwtOFFCorspgTankPressure.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_13.addWidget(self.cyc1SubLblHtrSwtOFFCorspgTankPressure)

        self.cyc1SubLblHtrSwtOFFCorspgThrusterPressure = QLabel(self.frame_8)
        self.cyc1SubLblHtrSwtOFFCorspgThrusterPressure.setObjectName(u"cyc1SubLblHtrSwtOFFCorspgThrusterPressure")
        self.cyc1SubLblHtrSwtOFFCorspgThrusterPressure.setMinimumSize(QSize(450, 55))
        self.cyc1SubLblHtrSwtOFFCorspgThrusterPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc1SubLblHtrSwtOFFCorspgThrusterPressure.setStyleSheet(u"")
        self.cyc1SubLblHtrSwtOFFCorspgThrusterPressure.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_13.addWidget(self.cyc1SubLblHtrSwtOFFCorspgThrusterPressure)

        self.cyc1SubLblMaxTemp = QLabel(self.frame_8)
        self.cyc1SubLblMaxTemp.setObjectName(u"cyc1SubLblMaxTemp")
        self.cyc1SubLblMaxTemp.setMinimumSize(QSize(450, 55))
        self.cyc1SubLblMaxTemp.setMaximumSize(QSize(16777215, 55))
        self.cyc1SubLblMaxTemp.setStyleSheet(u"")
        self.cyc1SubLblMaxTemp.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_13.addWidget(self.cyc1SubLblMaxTemp)

        self.cyc1SubLblLoc = QLabel(self.frame_8)
        self.cyc1SubLblLoc.setObjectName(u"cyc1SubLblLoc")
        self.cyc1SubLblLoc.setMinimumSize(QSize(450, 55))
        self.cyc1SubLblLoc.setMaximumSize(QSize(16777215, 55))
        self.cyc1SubLblLoc.setStyleSheet(u"")
        self.cyc1SubLblLoc.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_13.addWidget(self.cyc1SubLblLoc)

        self.cyc1SubLblCorrespgTemp = QLabel(self.frame_8)
        self.cyc1SubLblCorrespgTemp.setObjectName(u"cyc1SubLblCorrespgTemp")
        self.cyc1SubLblCorrespgTemp.setMinimumSize(QSize(450, 55))
        self.cyc1SubLblCorrespgTemp.setMaximumSize(QSize(16777215, 55))
        self.cyc1SubLblCorrespgTemp.setStyleSheet(u"")
        self.cyc1SubLblCorrespgTemp.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_13.addWidget(self.cyc1SubLblCorrespgTemp)


        self.horizontalLayout_35.addWidget(self.frame_8)

        self.frame_10 = QFrame(self.cyc1SubFrame)
        self.frame_10.setObjectName(u"frame_10")
        self.frame_10.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_10.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_39 = QVBoxLayout(self.frame_10)
        self.verticalLayout_39.setSpacing(10)
        self.verticalLayout_39.setObjectName(u"verticalLayout_39")
        self.verticalLayout_39.setContentsMargins(0, 0, 0, 0)
        self.cyc1SubLnEdtHtrSwtOnTime = QTimeEdit(self.frame_10)
        self.cyc1SubLnEdtHtrSwtOnTime.setObjectName(u"cyc1SubLnEdtHtrSwtOnTime")
        self.cyc1SubLnEdtHtrSwtOnTime.setMinimumSize(QSize(450, 55))
        self.cyc1SubLnEdtHtrSwtOnTime.setMaximumSize(QSize(16777215, 55))
        self.cyc1SubLnEdtHtrSwtOnTime.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.cyc1SubLnEdtHtrSwtOnTime.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc1SubLnEdtHtrSwtOnTime.setMinimumTime(QTime(0, 0, 0))
        self.cyc1SubLnEdtHtrSwtOnTime.setCurrentSection(QDateTimeEdit.Section.HourSection)
        self.cyc1SubLnEdtHtrSwtOnTime.setCalendarPopup(True)
        self.cyc1SubLnEdtHtrSwtOnTime.setTimeSpec(Qt.TimeSpec.UTC)
        self.cyc1SubLnEdtHtrSwtOnTime.setTime(QTime(0, 0, 0))

        self.verticalLayout_39.addWidget(self.cyc1SubLnEdtHtrSwtOnTime)

        self.cyc1SubLnEdtHtrSwtONCorspgTankPressure = CustomDoubleSpinBox(self.frame_10)
        self.cyc1SubLnEdtHtrSwtONCorspgTankPressure.setObjectName(u"cyc1SubLnEdtHtrSwtONCorspgTankPressure")
        self.cyc1SubLnEdtHtrSwtONCorspgTankPressure.setMinimumSize(QSize(450, 55))
        self.cyc1SubLnEdtHtrSwtONCorspgTankPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc1SubLnEdtHtrSwtONCorspgTankPressure.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc1SubLnEdtHtrSwtONCorspgTankPressure.setDecimals(6)
        self.cyc1SubLnEdtHtrSwtONCorspgTankPressure.setMaximum(10000000.000000000000000)
        self.cyc1SubLnEdtHtrSwtONCorspgTankPressure.setSingleStep(0.000000000000000)

        self.verticalLayout_39.addWidget(self.cyc1SubLnEdtHtrSwtONCorspgTankPressure)

        self.cyc1SubLnEdtHtrSwtONCorspgThrusterPressure = CustomDoubleSpinBox(self.frame_10)
        self.cyc1SubLnEdtHtrSwtONCorspgThrusterPressure.setObjectName(u"cyc1SubLnEdtHtrSwtONCorspgThrusterPressure")
        self.cyc1SubLnEdtHtrSwtONCorspgThrusterPressure.setMinimumSize(QSize(450, 55))
        self.cyc1SubLnEdtHtrSwtONCorspgThrusterPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc1SubLnEdtHtrSwtONCorspgThrusterPressure.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc1SubLnEdtHtrSwtONCorspgThrusterPressure.setDecimals(6)
        self.cyc1SubLnEdtHtrSwtONCorspgThrusterPressure.setMaximum(10000000.000000000000000)
        self.cyc1SubLnEdtHtrSwtONCorspgThrusterPressure.setSingleStep(0.000000000000000)

        self.verticalLayout_39.addWidget(self.cyc1SubLnEdtHtrSwtONCorspgThrusterPressure)

        self.cyc1SubLnEdtHtrSwtOffTime = QTimeEdit(self.frame_10)
        self.cyc1SubLnEdtHtrSwtOffTime.setObjectName(u"cyc1SubLnEdtHtrSwtOffTime")
        self.cyc1SubLnEdtHtrSwtOffTime.setMinimumSize(QSize(450, 55))
        self.cyc1SubLnEdtHtrSwtOffTime.setMaximumSize(QSize(16777215, 55))
        self.cyc1SubLnEdtHtrSwtOffTime.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.cyc1SubLnEdtHtrSwtOffTime.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc1SubLnEdtHtrSwtOffTime.setMinimumTime(QTime(0, 0, 0))
        self.cyc1SubLnEdtHtrSwtOffTime.setCurrentSection(QDateTimeEdit.Section.HourSection)
        self.cyc1SubLnEdtHtrSwtOffTime.setCalendarPopup(True)
        self.cyc1SubLnEdtHtrSwtOffTime.setTimeSpec(Qt.TimeSpec.UTC)
        self.cyc1SubLnEdtHtrSwtOffTime.setTime(QTime(0, 0, 0))

        self.verticalLayout_39.addWidget(self.cyc1SubLnEdtHtrSwtOffTime)

        self.cyc1SubLnEdtHtrSwtOFFCorspgTankPressure = CustomDoubleSpinBox(self.frame_10)
        self.cyc1SubLnEdtHtrSwtOFFCorspgTankPressure.setObjectName(u"cyc1SubLnEdtHtrSwtOFFCorspgTankPressure")
        self.cyc1SubLnEdtHtrSwtOFFCorspgTankPressure.setMinimumSize(QSize(450, 55))
        self.cyc1SubLnEdtHtrSwtOFFCorspgTankPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc1SubLnEdtHtrSwtOFFCorspgTankPressure.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc1SubLnEdtHtrSwtOFFCorspgTankPressure.setDecimals(6)
        self.cyc1SubLnEdtHtrSwtOFFCorspgTankPressure.setMaximum(10000000.000000000000000)
        self.cyc1SubLnEdtHtrSwtOFFCorspgTankPressure.setSingleStep(0.000000000000000)

        self.verticalLayout_39.addWidget(self.cyc1SubLnEdtHtrSwtOFFCorspgTankPressure)

        self.cyc1SubLnEdtHtrSwtOFFCorspgThrusterPressure = CustomDoubleSpinBox(self.frame_10)
        self.cyc1SubLnEdtHtrSwtOFFCorspgThrusterPressure.setObjectName(u"cyc1SubLnEdtHtrSwtOFFCorspgThrusterPressure")
        self.cyc1SubLnEdtHtrSwtOFFCorspgThrusterPressure.setMinimumSize(QSize(450, 55))
        self.cyc1SubLnEdtHtrSwtOFFCorspgThrusterPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc1SubLnEdtHtrSwtOFFCorspgThrusterPressure.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc1SubLnEdtHtrSwtOFFCorspgThrusterPressure.setDecimals(6)
        self.cyc1SubLnEdtHtrSwtOFFCorspgThrusterPressure.setMaximum(10000000.000000000000000)
        self.cyc1SubLnEdtHtrSwtOFFCorspgThrusterPressure.setSingleStep(0.000000000000000)

        self.verticalLayout_39.addWidget(self.cyc1SubLnEdtHtrSwtOFFCorspgThrusterPressure)

        self.cyc1SubLnEdtMaxTemp = CustomDoubleSpinBox(self.frame_10)
        self.cyc1SubLnEdtMaxTemp.setObjectName(u"cyc1SubLnEdtMaxTemp")
        self.cyc1SubLnEdtMaxTemp.setMinimumSize(QSize(450, 55))
        self.cyc1SubLnEdtMaxTemp.setMaximumSize(QSize(16777215, 55))
        self.cyc1SubLnEdtMaxTemp.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc1SubLnEdtMaxTemp.setDecimals(6)
        self.cyc1SubLnEdtMaxTemp.setMaximum(10000000.000000000000000)
        self.cyc1SubLnEdtMaxTemp.setSingleStep(0.000000000000000)

        self.verticalLayout_39.addWidget(self.cyc1SubLnEdtMaxTemp)

        self.cyc1SubLnEdtLoc = QLineEdit(self.frame_10)
        self.cyc1SubLnEdtLoc.setObjectName(u"cyc1SubLnEdtLoc")
        self.cyc1SubLnEdtLoc.setMinimumSize(QSize(450, 55))
        self.cyc1SubLnEdtLoc.setMaximumSize(QSize(16777215, 55))
        self.cyc1SubLnEdtLoc.setStyleSheet(u"")

        self.verticalLayout_39.addWidget(self.cyc1SubLnEdtLoc)

        self.cyc1SubLnEdtCorrespgTemp = CustomDoubleSpinBox(self.frame_10)
        self.cyc1SubLnEdtCorrespgTemp.setObjectName(u"cyc1SubLnEdtCorrespgTemp")
        self.cyc1SubLnEdtCorrespgTemp.setMinimumSize(QSize(450, 55))
        self.cyc1SubLnEdtCorrespgTemp.setMaximumSize(QSize(16777215, 55))
        self.cyc1SubLnEdtCorrespgTemp.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc1SubLnEdtCorrespgTemp.setDecimals(6)
        self.cyc1SubLnEdtCorrespgTemp.setMaximum(10000000.000000000000000)
        self.cyc1SubLnEdtCorrespgTemp.setSingleStep(0.000000000000000)

        self.verticalLayout_39.addWidget(self.cyc1SubLnEdtCorrespgTemp)


        self.horizontalLayout_35.addWidget(self.frame_10)


        self.gridLayout_19.addWidget(self.cyc1SubFrame, 0, 1, 1, 1)

        self.horizontalSpacer_24 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_19.addItem(self.horizontalSpacer_24, 0, 2, 1, 1)

        self.cycleTabWidget.addTab(self.cycle1, "")
        self.cycle2 = QWidget()
        self.cycle2.setObjectName(u"cycle2")
        self.gridLayout_21 = QGridLayout(self.cycle2)
        self.gridLayout_21.setObjectName(u"gridLayout_21")
        self.gridLayout_21.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_26 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_21.addItem(self.horizontalSpacer_26, 0, 2, 1, 1)

        self.cyc2SubFrame = QFrame(self.cycle2)
        self.cyc2SubFrame.setObjectName(u"cyc2SubFrame")
        self.cyc2SubFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.cyc2SubFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_36 = QHBoxLayout(self.cyc2SubFrame)
        self.horizontalLayout_36.setSpacing(6)
        self.horizontalLayout_36.setObjectName(u"horizontalLayout_36")
        self.horizontalLayout_36.setContentsMargins(0, 0, 0, 0)
        self.frame_18 = QFrame(self.cyc2SubFrame)
        self.frame_18.setObjectName(u"frame_18")
        self.frame_18.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_18.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_41 = QVBoxLayout(self.frame_18)
        self.verticalLayout_41.setSpacing(10)
        self.verticalLayout_41.setObjectName(u"verticalLayout_41")
        self.verticalLayout_41.setContentsMargins(0, 0, 0, 0)
        self.cyc2SubLblHtrSwtOnTime = QLabel(self.frame_18)
        self.cyc2SubLblHtrSwtOnTime.setObjectName(u"cyc2SubLblHtrSwtOnTime")
        self.cyc2SubLblHtrSwtOnTime.setMinimumSize(QSize(450, 55))
        self.cyc2SubLblHtrSwtOnTime.setMaximumSize(QSize(16777215, 55))
        self.cyc2SubLblHtrSwtOnTime.setStyleSheet(u"")
        self.cyc2SubLblHtrSwtOnTime.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_41.addWidget(self.cyc2SubLblHtrSwtOnTime)

        self.cyc2SubLblHtrSwtONCorspgTankPressure = QLabel(self.frame_18)
        self.cyc2SubLblHtrSwtONCorspgTankPressure.setObjectName(u"cyc2SubLblHtrSwtONCorspgTankPressure")
        self.cyc2SubLblHtrSwtONCorspgTankPressure.setMinimumSize(QSize(450, 55))
        self.cyc2SubLblHtrSwtONCorspgTankPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc2SubLblHtrSwtONCorspgTankPressure.setStyleSheet(u"")
        self.cyc2SubLblHtrSwtONCorspgTankPressure.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_41.addWidget(self.cyc2SubLblHtrSwtONCorspgTankPressure)

        self.cyc2SubLblHtrSwtONCorspgThrusterPressure = QLabel(self.frame_18)
        self.cyc2SubLblHtrSwtONCorspgThrusterPressure.setObjectName(u"cyc2SubLblHtrSwtONCorspgThrusterPressure")
        self.cyc2SubLblHtrSwtONCorspgThrusterPressure.setMinimumSize(QSize(450, 55))
        self.cyc2SubLblHtrSwtONCorspgThrusterPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc2SubLblHtrSwtONCorspgThrusterPressure.setStyleSheet(u"")
        self.cyc2SubLblHtrSwtONCorspgThrusterPressure.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_41.addWidget(self.cyc2SubLblHtrSwtONCorspgThrusterPressure)

        self.cyc2SubLblHtrSwtOffTime = QLabel(self.frame_18)
        self.cyc2SubLblHtrSwtOffTime.setObjectName(u"cyc2SubLblHtrSwtOffTime")
        self.cyc2SubLblHtrSwtOffTime.setMinimumSize(QSize(300, 55))
        self.cyc2SubLblHtrSwtOffTime.setMaximumSize(QSize(16777215, 55))
        self.cyc2SubLblHtrSwtOffTime.setStyleSheet(u"")
        self.cyc2SubLblHtrSwtOffTime.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_41.addWidget(self.cyc2SubLblHtrSwtOffTime)

        self.cyc2SubLblHtrSwtOFFCorspgTankPressure = QLabel(self.frame_18)
        self.cyc2SubLblHtrSwtOFFCorspgTankPressure.setObjectName(u"cyc2SubLblHtrSwtOFFCorspgTankPressure")
        self.cyc2SubLblHtrSwtOFFCorspgTankPressure.setMinimumSize(QSize(450, 55))
        self.cyc2SubLblHtrSwtOFFCorspgTankPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc2SubLblHtrSwtOFFCorspgTankPressure.setStyleSheet(u"")
        self.cyc2SubLblHtrSwtOFFCorspgTankPressure.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_41.addWidget(self.cyc2SubLblHtrSwtOFFCorspgTankPressure)

        self.cyc2SubLblHtrSwtOFFCorspgThrusterPressure = QLabel(self.frame_18)
        self.cyc2SubLblHtrSwtOFFCorspgThrusterPressure.setObjectName(u"cyc2SubLblHtrSwtOFFCorspgThrusterPressure")
        self.cyc2SubLblHtrSwtOFFCorspgThrusterPressure.setMinimumSize(QSize(450, 55))
        self.cyc2SubLblHtrSwtOFFCorspgThrusterPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc2SubLblHtrSwtOFFCorspgThrusterPressure.setStyleSheet(u"")
        self.cyc2SubLblHtrSwtOFFCorspgThrusterPressure.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_41.addWidget(self.cyc2SubLblHtrSwtOFFCorspgThrusterPressure)

        self.cyc2SubLblMaxTemp = QLabel(self.frame_18)
        self.cyc2SubLblMaxTemp.setObjectName(u"cyc2SubLblMaxTemp")
        self.cyc2SubLblMaxTemp.setMinimumSize(QSize(450, 55))
        self.cyc2SubLblMaxTemp.setMaximumSize(QSize(16777215, 55))
        self.cyc2SubLblMaxTemp.setStyleSheet(u"")
        self.cyc2SubLblMaxTemp.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_41.addWidget(self.cyc2SubLblMaxTemp)

        self.cyc2SubLblLoc = QLabel(self.frame_18)
        self.cyc2SubLblLoc.setObjectName(u"cyc2SubLblLoc")
        self.cyc2SubLblLoc.setMinimumSize(QSize(450, 55))
        self.cyc2SubLblLoc.setMaximumSize(QSize(16777215, 55))
        self.cyc2SubLblLoc.setStyleSheet(u"")
        self.cyc2SubLblLoc.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_41.addWidget(self.cyc2SubLblLoc)

        self.cyc2SubLblCorrespgTemp = QLabel(self.frame_18)
        self.cyc2SubLblCorrespgTemp.setObjectName(u"cyc2SubLblCorrespgTemp")
        self.cyc2SubLblCorrespgTemp.setMinimumSize(QSize(450, 55))
        self.cyc2SubLblCorrespgTemp.setMaximumSize(QSize(16777215, 55))
        self.cyc2SubLblCorrespgTemp.setStyleSheet(u"")
        self.cyc2SubLblCorrespgTemp.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_41.addWidget(self.cyc2SubLblCorrespgTemp)


        self.horizontalLayout_36.addWidget(self.frame_18)

        self.frame_17 = QFrame(self.cyc2SubFrame)
        self.frame_17.setObjectName(u"frame_17")
        self.frame_17.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_17.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_40 = QVBoxLayout(self.frame_17)
        self.verticalLayout_40.setSpacing(10)
        self.verticalLayout_40.setObjectName(u"verticalLayout_40")
        self.verticalLayout_40.setContentsMargins(0, 0, 0, 0)
        self.cyc2SubLnEdtHtrSwtOnTime = QTimeEdit(self.frame_17)
        self.cyc2SubLnEdtHtrSwtOnTime.setObjectName(u"cyc2SubLnEdtHtrSwtOnTime")
        self.cyc2SubLnEdtHtrSwtOnTime.setMinimumSize(QSize(450, 55))
        self.cyc2SubLnEdtHtrSwtOnTime.setMaximumSize(QSize(16777215, 55))
        self.cyc2SubLnEdtHtrSwtOnTime.setFrame(True)
        self.cyc2SubLnEdtHtrSwtOnTime.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.cyc2SubLnEdtHtrSwtOnTime.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)

        self.verticalLayout_40.addWidget(self.cyc2SubLnEdtHtrSwtOnTime)

        self.cyc2SubLnEdtHtrSwtONCorspgTankPressure = CustomDoubleSpinBox(self.frame_17)
        self.cyc2SubLnEdtHtrSwtONCorspgTankPressure.setObjectName(u"cyc2SubLnEdtHtrSwtONCorspgTankPressure")
        self.cyc2SubLnEdtHtrSwtONCorspgTankPressure.setMinimumSize(QSize(450, 55))
        self.cyc2SubLnEdtHtrSwtONCorspgTankPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc2SubLnEdtHtrSwtONCorspgTankPressure.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc2SubLnEdtHtrSwtONCorspgTankPressure.setDecimals(6)
        self.cyc2SubLnEdtHtrSwtONCorspgTankPressure.setMaximum(10000000.000000000000000)
        self.cyc2SubLnEdtHtrSwtONCorspgTankPressure.setSingleStep(0.000000000000000)

        self.verticalLayout_40.addWidget(self.cyc2SubLnEdtHtrSwtONCorspgTankPressure)

        self.cyc2SubLnEdtHtrSwtONCorspgThrusterPressure = CustomDoubleSpinBox(self.frame_17)
        self.cyc2SubLnEdtHtrSwtONCorspgThrusterPressure.setObjectName(u"cyc2SubLnEdtHtrSwtONCorspgThrusterPressure")
        self.cyc2SubLnEdtHtrSwtONCorspgThrusterPressure.setMinimumSize(QSize(450, 55))
        self.cyc2SubLnEdtHtrSwtONCorspgThrusterPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc2SubLnEdtHtrSwtONCorspgThrusterPressure.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc2SubLnEdtHtrSwtONCorspgThrusterPressure.setDecimals(6)
        self.cyc2SubLnEdtHtrSwtONCorspgThrusterPressure.setMaximum(10000000.000000000000000)
        self.cyc2SubLnEdtHtrSwtONCorspgThrusterPressure.setSingleStep(0.000000000000000)

        self.verticalLayout_40.addWidget(self.cyc2SubLnEdtHtrSwtONCorspgThrusterPressure)

        self.cyc2SubLnEdtHtrSwtOffTime = QTimeEdit(self.frame_17)
        self.cyc2SubLnEdtHtrSwtOffTime.setObjectName(u"cyc2SubLnEdtHtrSwtOffTime")
        self.cyc2SubLnEdtHtrSwtOffTime.setMinimumSize(QSize(450, 55))
        self.cyc2SubLnEdtHtrSwtOffTime.setMaximumSize(QSize(16777215, 55))
        self.cyc2SubLnEdtHtrSwtOffTime.setFrame(True)
        self.cyc2SubLnEdtHtrSwtOffTime.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.cyc2SubLnEdtHtrSwtOffTime.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)

        self.verticalLayout_40.addWidget(self.cyc2SubLnEdtHtrSwtOffTime)

        self.cyc2SubLnEdtHtrSwtOFFCorspgTankPressure = CustomDoubleSpinBox(self.frame_17)
        self.cyc2SubLnEdtHtrSwtOFFCorspgTankPressure.setObjectName(u"cyc2SubLnEdtHtrSwtOFFCorspgTankPressure")
        self.cyc2SubLnEdtHtrSwtOFFCorspgTankPressure.setMinimumSize(QSize(450, 55))
        self.cyc2SubLnEdtHtrSwtOFFCorspgTankPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc2SubLnEdtHtrSwtOFFCorspgTankPressure.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc2SubLnEdtHtrSwtOFFCorspgTankPressure.setDecimals(6)
        self.cyc2SubLnEdtHtrSwtOFFCorspgTankPressure.setMaximum(10000000.000000000000000)
        self.cyc2SubLnEdtHtrSwtOFFCorspgTankPressure.setSingleStep(0.000000000000000)

        self.verticalLayout_40.addWidget(self.cyc2SubLnEdtHtrSwtOFFCorspgTankPressure)

        self.cyc2SubLnEdtHtrSwtOFFCorspgThrusterPressure = CustomDoubleSpinBox(self.frame_17)
        self.cyc2SubLnEdtHtrSwtOFFCorspgThrusterPressure.setObjectName(u"cyc2SubLnEdtHtrSwtOFFCorspgThrusterPressure")
        self.cyc2SubLnEdtHtrSwtOFFCorspgThrusterPressure.setMinimumSize(QSize(450, 55))
        self.cyc2SubLnEdtHtrSwtOFFCorspgThrusterPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc2SubLnEdtHtrSwtOFFCorspgThrusterPressure.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc2SubLnEdtHtrSwtOFFCorspgThrusterPressure.setDecimals(6)
        self.cyc2SubLnEdtHtrSwtOFFCorspgThrusterPressure.setMaximum(10000000.000000000000000)
        self.cyc2SubLnEdtHtrSwtOFFCorspgThrusterPressure.setSingleStep(0.000000000000000)

        self.verticalLayout_40.addWidget(self.cyc2SubLnEdtHtrSwtOFFCorspgThrusterPressure)

        self.cyc2SubLnEdtMaxTemp = CustomDoubleSpinBox(self.frame_17)
        self.cyc2SubLnEdtMaxTemp.setObjectName(u"cyc2SubLnEdtMaxTemp")
        self.cyc2SubLnEdtMaxTemp.setMinimumSize(QSize(450, 55))
        self.cyc2SubLnEdtMaxTemp.setMaximumSize(QSize(16777215, 55))
        self.cyc2SubLnEdtMaxTemp.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc2SubLnEdtMaxTemp.setDecimals(6)
        self.cyc2SubLnEdtMaxTemp.setMaximum(10000000.000000000000000)
        self.cyc2SubLnEdtMaxTemp.setSingleStep(0.000000000000000)

        self.verticalLayout_40.addWidget(self.cyc2SubLnEdtMaxTemp)

        self.cyc2SubLnEdtLoc = QLineEdit(self.frame_17)
        self.cyc2SubLnEdtLoc.setObjectName(u"cyc2SubLnEdtLoc")
        self.cyc2SubLnEdtLoc.setMinimumSize(QSize(450, 55))
        self.cyc2SubLnEdtLoc.setMaximumSize(QSize(16777215, 55))
        self.cyc2SubLnEdtLoc.setStyleSheet(u"")

        self.verticalLayout_40.addWidget(self.cyc2SubLnEdtLoc)

        self.cyc2SubLnEdtCorrespgTemp = CustomDoubleSpinBox(self.frame_17)
        self.cyc2SubLnEdtCorrespgTemp.setObjectName(u"cyc2SubLnEdtCorrespgTemp")
        self.cyc2SubLnEdtCorrespgTemp.setMinimumSize(QSize(450, 55))
        self.cyc2SubLnEdtCorrespgTemp.setMaximumSize(QSize(16777215, 55))
        self.cyc2SubLnEdtCorrespgTemp.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc2SubLnEdtCorrespgTemp.setDecimals(6)
        self.cyc2SubLnEdtCorrespgTemp.setMaximum(10000000.000000000000000)
        self.cyc2SubLnEdtCorrespgTemp.setSingleStep(0.000000000000000)

        self.verticalLayout_40.addWidget(self.cyc2SubLnEdtCorrespgTemp)


        self.horizontalLayout_36.addWidget(self.frame_17)


        self.gridLayout_21.addWidget(self.cyc2SubFrame, 0, 1, 1, 1)

        self.horizontalSpacer_25 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_21.addItem(self.horizontalSpacer_25, 0, 0, 1, 1)

        self.cycleTabWidget.addTab(self.cycle2, "")
        self.cycle3 = QWidget()
        self.cycle3.setObjectName(u"cycle3")
        self.gridLayout_23 = QGridLayout(self.cycle3)
        self.gridLayout_23.setObjectName(u"gridLayout_23")
        self.gridLayout_23.setContentsMargins(0, 0, 0, 0)
        self.cyc3SubFrame = QFrame(self.cycle3)
        self.cyc3SubFrame.setObjectName(u"cyc3SubFrame")
        self.cyc3SubFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.cyc3SubFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_37 = QHBoxLayout(self.cyc3SubFrame)
        self.horizontalLayout_37.setObjectName(u"horizontalLayout_37")
        self.horizontalLayout_37.setContentsMargins(0, 0, 0, 0)
        self.frame_22 = QFrame(self.cyc3SubFrame)
        self.frame_22.setObjectName(u"frame_22")
        self.frame_22.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_22.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_42 = QVBoxLayout(self.frame_22)
        self.verticalLayout_42.setSpacing(10)
        self.verticalLayout_42.setObjectName(u"verticalLayout_42")
        self.verticalLayout_42.setContentsMargins(0, 0, 0, 0)
        self.cyc3SubLblHtrSwtOnTime = QLabel(self.frame_22)
        self.cyc3SubLblHtrSwtOnTime.setObjectName(u"cyc3SubLblHtrSwtOnTime")
        self.cyc3SubLblHtrSwtOnTime.setMinimumSize(QSize(450, 55))
        self.cyc3SubLblHtrSwtOnTime.setMaximumSize(QSize(16777215, 55))
        self.cyc3SubLblHtrSwtOnTime.setStyleSheet(u"")
        self.cyc3SubLblHtrSwtOnTime.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_42.addWidget(self.cyc3SubLblHtrSwtOnTime)

        self.cyc3SubLblHtrSwtONCorspgTankPressure = QLabel(self.frame_22)
        self.cyc3SubLblHtrSwtONCorspgTankPressure.setObjectName(u"cyc3SubLblHtrSwtONCorspgTankPressure")
        self.cyc3SubLblHtrSwtONCorspgTankPressure.setMinimumSize(QSize(450, 55))
        self.cyc3SubLblHtrSwtONCorspgTankPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc3SubLblHtrSwtONCorspgTankPressure.setStyleSheet(u"")
        self.cyc3SubLblHtrSwtONCorspgTankPressure.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_42.addWidget(self.cyc3SubLblHtrSwtONCorspgTankPressure)

        self.cyc3SubLblHtrSwtONCorspgThrusterPressure = QLabel(self.frame_22)
        self.cyc3SubLblHtrSwtONCorspgThrusterPressure.setObjectName(u"cyc3SubLblHtrSwtONCorspgThrusterPressure")
        self.cyc3SubLblHtrSwtONCorspgThrusterPressure.setMinimumSize(QSize(450, 55))
        self.cyc3SubLblHtrSwtONCorspgThrusterPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc3SubLblHtrSwtONCorspgThrusterPressure.setStyleSheet(u"")
        self.cyc3SubLblHtrSwtONCorspgThrusterPressure.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_42.addWidget(self.cyc3SubLblHtrSwtONCorspgThrusterPressure)

        self.cyc3SubLblHtrSwtOffTime = QLabel(self.frame_22)
        self.cyc3SubLblHtrSwtOffTime.setObjectName(u"cyc3SubLblHtrSwtOffTime")
        self.cyc3SubLblHtrSwtOffTime.setMinimumSize(QSize(300, 55))
        self.cyc3SubLblHtrSwtOffTime.setMaximumSize(QSize(16777215, 55))
        self.cyc3SubLblHtrSwtOffTime.setStyleSheet(u"")
        self.cyc3SubLblHtrSwtOffTime.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_42.addWidget(self.cyc3SubLblHtrSwtOffTime)

        self.cyc3SubLblHtrSwtOFFCorspgTankPressure = QLabel(self.frame_22)
        self.cyc3SubLblHtrSwtOFFCorspgTankPressure.setObjectName(u"cyc3SubLblHtrSwtOFFCorspgTankPressure")
        self.cyc3SubLblHtrSwtOFFCorspgTankPressure.setMinimumSize(QSize(450, 55))
        self.cyc3SubLblHtrSwtOFFCorspgTankPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc3SubLblHtrSwtOFFCorspgTankPressure.setStyleSheet(u"")
        self.cyc3SubLblHtrSwtOFFCorspgTankPressure.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_42.addWidget(self.cyc3SubLblHtrSwtOFFCorspgTankPressure)

        self.cyc3SubLblHtrSwtOFFCorspgThrusterPressure = QLabel(self.frame_22)
        self.cyc3SubLblHtrSwtOFFCorspgThrusterPressure.setObjectName(u"cyc3SubLblHtrSwtOFFCorspgThrusterPressure")
        self.cyc3SubLblHtrSwtOFFCorspgThrusterPressure.setMinimumSize(QSize(450, 55))
        self.cyc3SubLblHtrSwtOFFCorspgThrusterPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc3SubLblHtrSwtOFFCorspgThrusterPressure.setStyleSheet(u"")
        self.cyc3SubLblHtrSwtOFFCorspgThrusterPressure.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_42.addWidget(self.cyc3SubLblHtrSwtOFFCorspgThrusterPressure)

        self.cyc3SubLblMaxTemp = QLabel(self.frame_22)
        self.cyc3SubLblMaxTemp.setObjectName(u"cyc3SubLblMaxTemp")
        self.cyc3SubLblMaxTemp.setMinimumSize(QSize(300, 55))
        self.cyc3SubLblMaxTemp.setMaximumSize(QSize(16777215, 55))
        self.cyc3SubLblMaxTemp.setStyleSheet(u"")
        self.cyc3SubLblMaxTemp.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_42.addWidget(self.cyc3SubLblMaxTemp)

        self.cyc3SubLblLoc = QLabel(self.frame_22)
        self.cyc3SubLblLoc.setObjectName(u"cyc3SubLblLoc")
        self.cyc3SubLblLoc.setMinimumSize(QSize(300, 55))
        self.cyc3SubLblLoc.setMaximumSize(QSize(16777215, 55))
        self.cyc3SubLblLoc.setStyleSheet(u"")
        self.cyc3SubLblLoc.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_42.addWidget(self.cyc3SubLblLoc)

        self.cyc3SubLblCorrespgTemp = QLabel(self.frame_22)
        self.cyc3SubLblCorrespgTemp.setObjectName(u"cyc3SubLblCorrespgTemp")
        self.cyc3SubLblCorrespgTemp.setMinimumSize(QSize(300, 55))
        self.cyc3SubLblCorrespgTemp.setMaximumSize(QSize(16777215, 55))
        self.cyc3SubLblCorrespgTemp.setStyleSheet(u"")
        self.cyc3SubLblCorrespgTemp.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_42.addWidget(self.cyc3SubLblCorrespgTemp)


        self.horizontalLayout_37.addWidget(self.frame_22)

        self.frame_23 = QFrame(self.cyc3SubFrame)
        self.frame_23.setObjectName(u"frame_23")
        self.frame_23.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_23.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_43 = QVBoxLayout(self.frame_23)
        self.verticalLayout_43.setSpacing(10)
        self.verticalLayout_43.setObjectName(u"verticalLayout_43")
        self.verticalLayout_43.setContentsMargins(0, 0, 0, 0)
        self.cyc3SubLnEdtHtrSwtOnTime = QTimeEdit(self.frame_23)
        self.cyc3SubLnEdtHtrSwtOnTime.setObjectName(u"cyc3SubLnEdtHtrSwtOnTime")
        self.cyc3SubLnEdtHtrSwtOnTime.setMinimumSize(QSize(450, 55))
        self.cyc3SubLnEdtHtrSwtOnTime.setMaximumSize(QSize(16777215, 55))
        self.cyc3SubLnEdtHtrSwtOnTime.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.cyc3SubLnEdtHtrSwtOnTime.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)

        self.verticalLayout_43.addWidget(self.cyc3SubLnEdtHtrSwtOnTime)

        self.cyc3SubLnEdtHtrSwtONCorspgTankPressure = CustomDoubleSpinBox(self.frame_23)
        self.cyc3SubLnEdtHtrSwtONCorspgTankPressure.setObjectName(u"cyc3SubLnEdtHtrSwtONCorspgTankPressure")
        self.cyc3SubLnEdtHtrSwtONCorspgTankPressure.setMinimumSize(QSize(450, 55))
        self.cyc3SubLnEdtHtrSwtONCorspgTankPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc3SubLnEdtHtrSwtONCorspgTankPressure.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc3SubLnEdtHtrSwtONCorspgTankPressure.setDecimals(6)
        self.cyc3SubLnEdtHtrSwtONCorspgTankPressure.setMaximum(10000000.000000000000000)
        self.cyc3SubLnEdtHtrSwtONCorspgTankPressure.setSingleStep(0.000000000000000)

        self.verticalLayout_43.addWidget(self.cyc3SubLnEdtHtrSwtONCorspgTankPressure)

        self.cyc3SubLnEdtHtrSwtONCorspgThrusterPressure = CustomDoubleSpinBox(self.frame_23)
        self.cyc3SubLnEdtHtrSwtONCorspgThrusterPressure.setObjectName(u"cyc3SubLnEdtHtrSwtONCorspgThrusterPressure")
        self.cyc3SubLnEdtHtrSwtONCorspgThrusterPressure.setMinimumSize(QSize(450, 55))
        self.cyc3SubLnEdtHtrSwtONCorspgThrusterPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc3SubLnEdtHtrSwtONCorspgThrusterPressure.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc3SubLnEdtHtrSwtONCorspgThrusterPressure.setDecimals(6)
        self.cyc3SubLnEdtHtrSwtONCorspgThrusterPressure.setMaximum(10000000.000000000000000)
        self.cyc3SubLnEdtHtrSwtONCorspgThrusterPressure.setSingleStep(0.000000000000000)

        self.verticalLayout_43.addWidget(self.cyc3SubLnEdtHtrSwtONCorspgThrusterPressure)

        self.cyc3SubLnEdtHtrSwtOffTime = QTimeEdit(self.frame_23)
        self.cyc3SubLnEdtHtrSwtOffTime.setObjectName(u"cyc3SubLnEdtHtrSwtOffTime")
        self.cyc3SubLnEdtHtrSwtOffTime.setMinimumSize(QSize(450, 55))
        self.cyc3SubLnEdtHtrSwtOffTime.setMaximumSize(QSize(16777215, 55))
        self.cyc3SubLnEdtHtrSwtOffTime.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.cyc3SubLnEdtHtrSwtOffTime.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)

        self.verticalLayout_43.addWidget(self.cyc3SubLnEdtHtrSwtOffTime)

        self.cyc3SubLnEdtHtrSwtOFFCorspgTankPressure = CustomDoubleSpinBox(self.frame_23)
        self.cyc3SubLnEdtHtrSwtOFFCorspgTankPressure.setObjectName(u"cyc3SubLnEdtHtrSwtOFFCorspgTankPressure")
        self.cyc3SubLnEdtHtrSwtOFFCorspgTankPressure.setMinimumSize(QSize(450, 55))
        self.cyc3SubLnEdtHtrSwtOFFCorspgTankPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc3SubLnEdtHtrSwtOFFCorspgTankPressure.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc3SubLnEdtHtrSwtOFFCorspgTankPressure.setDecimals(6)
        self.cyc3SubLnEdtHtrSwtOFFCorspgTankPressure.setMaximum(10000000.000000000000000)
        self.cyc3SubLnEdtHtrSwtOFFCorspgTankPressure.setSingleStep(0.000000000000000)

        self.verticalLayout_43.addWidget(self.cyc3SubLnEdtHtrSwtOFFCorspgTankPressure)

        self.cyc3SubLnEdtHtrSwtOFFCorspgThrusterPressure = CustomDoubleSpinBox(self.frame_23)
        self.cyc3SubLnEdtHtrSwtOFFCorspgThrusterPressure.setObjectName(u"cyc3SubLnEdtHtrSwtOFFCorspgThrusterPressure")
        self.cyc3SubLnEdtHtrSwtOFFCorspgThrusterPressure.setMinimumSize(QSize(450, 55))
        self.cyc3SubLnEdtHtrSwtOFFCorspgThrusterPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc3SubLnEdtHtrSwtOFFCorspgThrusterPressure.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc3SubLnEdtHtrSwtOFFCorspgThrusterPressure.setDecimals(6)
        self.cyc3SubLnEdtHtrSwtOFFCorspgThrusterPressure.setMaximum(10000000.000000000000000)
        self.cyc3SubLnEdtHtrSwtOFFCorspgThrusterPressure.setSingleStep(0.000000000000000)

        self.verticalLayout_43.addWidget(self.cyc3SubLnEdtHtrSwtOFFCorspgThrusterPressure)

        self.cyc3SubLnEdtMaxTemp = CustomDoubleSpinBox(self.frame_23)
        self.cyc3SubLnEdtMaxTemp.setObjectName(u"cyc3SubLnEdtMaxTemp")
        self.cyc3SubLnEdtMaxTemp.setMinimumSize(QSize(450, 55))
        self.cyc3SubLnEdtMaxTemp.setMaximumSize(QSize(16777215, 55))
        self.cyc3SubLnEdtMaxTemp.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc3SubLnEdtMaxTemp.setDecimals(6)
        self.cyc3SubLnEdtMaxTemp.setMaximum(10000000.000000000000000)
        self.cyc3SubLnEdtMaxTemp.setSingleStep(0.000000000000000)

        self.verticalLayout_43.addWidget(self.cyc3SubLnEdtMaxTemp)

        self.cyc3SubLnEdtLoc = QLineEdit(self.frame_23)
        self.cyc3SubLnEdtLoc.setObjectName(u"cyc3SubLnEdtLoc")
        self.cyc3SubLnEdtLoc.setMinimumSize(QSize(450, 55))
        self.cyc3SubLnEdtLoc.setMaximumSize(QSize(16777215, 55))
        self.cyc3SubLnEdtLoc.setStyleSheet(u"")

        self.verticalLayout_43.addWidget(self.cyc3SubLnEdtLoc)

        self.cyc3SubLnEdtCorrespgTemp = CustomDoubleSpinBox(self.frame_23)
        self.cyc3SubLnEdtCorrespgTemp.setObjectName(u"cyc3SubLnEdtCorrespgTemp")
        self.cyc3SubLnEdtCorrespgTemp.setMinimumSize(QSize(450, 55))
        self.cyc3SubLnEdtCorrespgTemp.setMaximumSize(QSize(16777215, 55))
        self.cyc3SubLnEdtCorrespgTemp.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc3SubLnEdtCorrespgTemp.setDecimals(6)
        self.cyc3SubLnEdtCorrespgTemp.setMaximum(10000000.000000000000000)
        self.cyc3SubLnEdtCorrespgTemp.setSingleStep(0.000000000000000)

        self.verticalLayout_43.addWidget(self.cyc3SubLnEdtCorrespgTemp)


        self.horizontalLayout_37.addWidget(self.frame_23)


        self.gridLayout_23.addWidget(self.cyc3SubFrame, 0, 1, 1, 1)

        self.horizontalSpacer_27 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_23.addItem(self.horizontalSpacer_27, 0, 0, 1, 1)

        self.horizontalSpacer_28 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_23.addItem(self.horizontalSpacer_28, 0, 2, 1, 1)

        self.cycleTabWidget.addTab(self.cycle3, "")
        self.cycle4 = QWidget()
        self.cycle4.setObjectName(u"cycle4")
        self.gridLayout_25 = QGridLayout(self.cycle4)
        self.gridLayout_25.setObjectName(u"gridLayout_25")
        self.gridLayout_25.setContentsMargins(0, 0, 0, 0)
        self.cyc4SubFrame = QFrame(self.cycle4)
        self.cyc4SubFrame.setObjectName(u"cyc4SubFrame")
        self.cyc4SubFrame.setStyleSheet(u"")
        self.cyc4SubFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.cyc4SubFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_38 = QHBoxLayout(self.cyc4SubFrame)
        self.horizontalLayout_38.setObjectName(u"horizontalLayout_38")
        self.horizontalLayout_38.setContentsMargins(0, 0, 0, 0)
        self.frame_24 = QFrame(self.cyc4SubFrame)
        self.frame_24.setObjectName(u"frame_24")
        self.frame_24.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_24.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_44 = QVBoxLayout(self.frame_24)
        self.verticalLayout_44.setSpacing(10)
        self.verticalLayout_44.setObjectName(u"verticalLayout_44")
        self.verticalLayout_44.setContentsMargins(0, 0, 0, 0)
        self.cyc4SubLblHtrSwtOnTime = QLabel(self.frame_24)
        self.cyc4SubLblHtrSwtOnTime.setObjectName(u"cyc4SubLblHtrSwtOnTime")
        self.cyc4SubLblHtrSwtOnTime.setMinimumSize(QSize(450, 55))
        self.cyc4SubLblHtrSwtOnTime.setMaximumSize(QSize(16777215, 55))
        self.cyc4SubLblHtrSwtOnTime.setStyleSheet(u"")
        self.cyc4SubLblHtrSwtOnTime.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_44.addWidget(self.cyc4SubLblHtrSwtOnTime)

        self.cyc4SubLblHtrSwtONCorspgTankPressure = QLabel(self.frame_24)
        self.cyc4SubLblHtrSwtONCorspgTankPressure.setObjectName(u"cyc4SubLblHtrSwtONCorspgTankPressure")
        self.cyc4SubLblHtrSwtONCorspgTankPressure.setMinimumSize(QSize(450, 55))
        self.cyc4SubLblHtrSwtONCorspgTankPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc4SubLblHtrSwtONCorspgTankPressure.setStyleSheet(u"")
        self.cyc4SubLblHtrSwtONCorspgTankPressure.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_44.addWidget(self.cyc4SubLblHtrSwtONCorspgTankPressure)

        self.cyc4SubLblHtrSwtONCorspgThrusterPressure = QLabel(self.frame_24)
        self.cyc4SubLblHtrSwtONCorspgThrusterPressure.setObjectName(u"cyc4SubLblHtrSwtONCorspgThrusterPressure")
        self.cyc4SubLblHtrSwtONCorspgThrusterPressure.setMinimumSize(QSize(450, 55))
        self.cyc4SubLblHtrSwtONCorspgThrusterPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc4SubLblHtrSwtONCorspgThrusterPressure.setStyleSheet(u"")
        self.cyc4SubLblHtrSwtONCorspgThrusterPressure.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_44.addWidget(self.cyc4SubLblHtrSwtONCorspgThrusterPressure)

        self.cyc4SubLblHtrSwtOffTime = QLabel(self.frame_24)
        self.cyc4SubLblHtrSwtOffTime.setObjectName(u"cyc4SubLblHtrSwtOffTime")
        self.cyc4SubLblHtrSwtOffTime.setMinimumSize(QSize(450, 55))
        self.cyc4SubLblHtrSwtOffTime.setMaximumSize(QSize(16777215, 55))
        self.cyc4SubLblHtrSwtOffTime.setStyleSheet(u"")
        self.cyc4SubLblHtrSwtOffTime.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_44.addWidget(self.cyc4SubLblHtrSwtOffTime)

        self.cyc4SubLblHtrSwtOFFCorspgTankPressure = QLabel(self.frame_24)
        self.cyc4SubLblHtrSwtOFFCorspgTankPressure.setObjectName(u"cyc4SubLblHtrSwtOFFCorspgTankPressure")
        self.cyc4SubLblHtrSwtOFFCorspgTankPressure.setMinimumSize(QSize(450, 55))
        self.cyc4SubLblHtrSwtOFFCorspgTankPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc4SubLblHtrSwtOFFCorspgTankPressure.setStyleSheet(u"")
        self.cyc4SubLblHtrSwtOFFCorspgTankPressure.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_44.addWidget(self.cyc4SubLblHtrSwtOFFCorspgTankPressure)

        self.cyc4SubLblHtrSwtOFFCorspgThrusterPressure = QLabel(self.frame_24)
        self.cyc4SubLblHtrSwtOFFCorspgThrusterPressure.setObjectName(u"cyc4SubLblHtrSwtOFFCorspgThrusterPressure")
        self.cyc4SubLblHtrSwtOFFCorspgThrusterPressure.setMinimumSize(QSize(450, 55))
        self.cyc4SubLblHtrSwtOFFCorspgThrusterPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc4SubLblHtrSwtOFFCorspgThrusterPressure.setStyleSheet(u"")
        self.cyc4SubLblHtrSwtOFFCorspgThrusterPressure.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_44.addWidget(self.cyc4SubLblHtrSwtOFFCorspgThrusterPressure)

        self.cyc4SubLblMaxTemp = QLabel(self.frame_24)
        self.cyc4SubLblMaxTemp.setObjectName(u"cyc4SubLblMaxTemp")
        self.cyc4SubLblMaxTemp.setMinimumSize(QSize(450, 55))
        self.cyc4SubLblMaxTemp.setMaximumSize(QSize(16777215, 55))
        self.cyc4SubLblMaxTemp.setStyleSheet(u"")
        self.cyc4SubLblMaxTemp.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_44.addWidget(self.cyc4SubLblMaxTemp)

        self.cyc4SubLblLoc = QLabel(self.frame_24)
        self.cyc4SubLblLoc.setObjectName(u"cyc4SubLblLoc")
        self.cyc4SubLblLoc.setMinimumSize(QSize(450, 55))
        self.cyc4SubLblLoc.setMaximumSize(QSize(16777215, 55))
        self.cyc4SubLblLoc.setStyleSheet(u"")
        self.cyc4SubLblLoc.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_44.addWidget(self.cyc4SubLblLoc)

        self.cyc4SubLblCorrespgTemp = QLabel(self.frame_24)
        self.cyc4SubLblCorrespgTemp.setObjectName(u"cyc4SubLblCorrespgTemp")
        self.cyc4SubLblCorrespgTemp.setMinimumSize(QSize(450, 55))
        self.cyc4SubLblCorrespgTemp.setMaximumSize(QSize(16777215, 55))
        self.cyc4SubLblCorrespgTemp.setStyleSheet(u"")
        self.cyc4SubLblCorrespgTemp.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_44.addWidget(self.cyc4SubLblCorrespgTemp)


        self.horizontalLayout_38.addWidget(self.frame_24)

        self.frame_32 = QFrame(self.cyc4SubFrame)
        self.frame_32.setObjectName(u"frame_32")
        self.frame_32.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_32.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_45 = QVBoxLayout(self.frame_32)
        self.verticalLayout_45.setSpacing(10)
        self.verticalLayout_45.setObjectName(u"verticalLayout_45")
        self.verticalLayout_45.setContentsMargins(0, 0, 0, 0)
        self.cyc4SubLnEdtHtrSwtOnTime = QTimeEdit(self.frame_32)
        self.cyc4SubLnEdtHtrSwtOnTime.setObjectName(u"cyc4SubLnEdtHtrSwtOnTime")
        self.cyc4SubLnEdtHtrSwtOnTime.setMinimumSize(QSize(450, 55))
        self.cyc4SubLnEdtHtrSwtOnTime.setMaximumSize(QSize(16777215, 55))
        self.cyc4SubLnEdtHtrSwtOnTime.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.cyc4SubLnEdtHtrSwtOnTime.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)

        self.verticalLayout_45.addWidget(self.cyc4SubLnEdtHtrSwtOnTime)

        self.cyc4SubLnEdtHtrSwtONCorspgTankPressure = CustomDoubleSpinBox(self.frame_32)
        self.cyc4SubLnEdtHtrSwtONCorspgTankPressure.setObjectName(u"cyc4SubLnEdtHtrSwtONCorspgTankPressure")
        self.cyc4SubLnEdtHtrSwtONCorspgTankPressure.setMinimumSize(QSize(450, 55))
        self.cyc4SubLnEdtHtrSwtONCorspgTankPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc4SubLnEdtHtrSwtONCorspgTankPressure.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc4SubLnEdtHtrSwtONCorspgTankPressure.setDecimals(6)
        self.cyc4SubLnEdtHtrSwtONCorspgTankPressure.setMaximum(10000000.000000000000000)
        self.cyc4SubLnEdtHtrSwtONCorspgTankPressure.setSingleStep(0.000000000000000)

        self.verticalLayout_45.addWidget(self.cyc4SubLnEdtHtrSwtONCorspgTankPressure)

        self.cyc4SubLnEdtHtrSwtONCorspgThrusterPressure = CustomDoubleSpinBox(self.frame_32)
        self.cyc4SubLnEdtHtrSwtONCorspgThrusterPressure.setObjectName(u"cyc4SubLnEdtHtrSwtONCorspgThrusterPressure")
        self.cyc4SubLnEdtHtrSwtONCorspgThrusterPressure.setMinimumSize(QSize(450, 55))
        self.cyc4SubLnEdtHtrSwtONCorspgThrusterPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc4SubLnEdtHtrSwtONCorspgThrusterPressure.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc4SubLnEdtHtrSwtONCorspgThrusterPressure.setDecimals(6)
        self.cyc4SubLnEdtHtrSwtONCorspgThrusterPressure.setMaximum(10000000.000000000000000)
        self.cyc4SubLnEdtHtrSwtONCorspgThrusterPressure.setSingleStep(0.000000000000000)

        self.verticalLayout_45.addWidget(self.cyc4SubLnEdtHtrSwtONCorspgThrusterPressure)

        self.cyc4SubLnEdtHtrSwtOffTime = QTimeEdit(self.frame_32)
        self.cyc4SubLnEdtHtrSwtOffTime.setObjectName(u"cyc4SubLnEdtHtrSwtOffTime")
        self.cyc4SubLnEdtHtrSwtOffTime.setMinimumSize(QSize(450, 55))
        self.cyc4SubLnEdtHtrSwtOffTime.setMaximumSize(QSize(16777215, 55))
        self.cyc4SubLnEdtHtrSwtOffTime.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.cyc4SubLnEdtHtrSwtOffTime.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)

        self.verticalLayout_45.addWidget(self.cyc4SubLnEdtHtrSwtOffTime)

        self.cyc4SubLnEdtHtrSwtOFFCorspgTankPressure = CustomDoubleSpinBox(self.frame_32)
        self.cyc4SubLnEdtHtrSwtOFFCorspgTankPressure.setObjectName(u"cyc4SubLnEdtHtrSwtOFFCorspgTankPressure")
        self.cyc4SubLnEdtHtrSwtOFFCorspgTankPressure.setMinimumSize(QSize(450, 55))
        self.cyc4SubLnEdtHtrSwtOFFCorspgTankPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc4SubLnEdtHtrSwtOFFCorspgTankPressure.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc4SubLnEdtHtrSwtOFFCorspgTankPressure.setDecimals(6)
        self.cyc4SubLnEdtHtrSwtOFFCorspgTankPressure.setMaximum(10000000.000000000000000)
        self.cyc4SubLnEdtHtrSwtOFFCorspgTankPressure.setSingleStep(0.000000000000000)

        self.verticalLayout_45.addWidget(self.cyc4SubLnEdtHtrSwtOFFCorspgTankPressure)

        self.cyc4SubLnEdtHtrSwtOFFCorspgThrusterPressure = CustomDoubleSpinBox(self.frame_32)
        self.cyc4SubLnEdtHtrSwtOFFCorspgThrusterPressure.setObjectName(u"cyc4SubLnEdtHtrSwtOFFCorspgThrusterPressure")
        self.cyc4SubLnEdtHtrSwtOFFCorspgThrusterPressure.setMinimumSize(QSize(450, 55))
        self.cyc4SubLnEdtHtrSwtOFFCorspgThrusterPressure.setMaximumSize(QSize(16777215, 55))
        self.cyc4SubLnEdtHtrSwtOFFCorspgThrusterPressure.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc4SubLnEdtHtrSwtOFFCorspgThrusterPressure.setDecimals(6)
        self.cyc4SubLnEdtHtrSwtOFFCorspgThrusterPressure.setMaximum(10000000.000000000000000)
        self.cyc4SubLnEdtHtrSwtOFFCorspgThrusterPressure.setSingleStep(0.000000000000000)

        self.verticalLayout_45.addWidget(self.cyc4SubLnEdtHtrSwtOFFCorspgThrusterPressure)

        self.cyc4SubLnEdtMaxTemp = CustomDoubleSpinBox(self.frame_32)
        self.cyc4SubLnEdtMaxTemp.setObjectName(u"cyc4SubLnEdtMaxTemp")
        self.cyc4SubLnEdtMaxTemp.setMinimumSize(QSize(450, 55))
        self.cyc4SubLnEdtMaxTemp.setMaximumSize(QSize(16777215, 55))
        self.cyc4SubLnEdtMaxTemp.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc4SubLnEdtMaxTemp.setDecimals(6)
        self.cyc4SubLnEdtMaxTemp.setMaximum(10000000.000000000000000)
        self.cyc4SubLnEdtMaxTemp.setSingleStep(0.000000000000000)

        self.verticalLayout_45.addWidget(self.cyc4SubLnEdtMaxTemp)

        self.cyc4SubLnEdtLoc = QLineEdit(self.frame_32)
        self.cyc4SubLnEdtLoc.setObjectName(u"cyc4SubLnEdtLoc")
        self.cyc4SubLnEdtLoc.setMinimumSize(QSize(450, 55))
        self.cyc4SubLnEdtLoc.setMaximumSize(QSize(16777215, 55))
        self.cyc4SubLnEdtLoc.setStyleSheet(u"")

        self.verticalLayout_45.addWidget(self.cyc4SubLnEdtLoc)

        self.cyc4SubLnEdtCorrespgTemp = CustomDoubleSpinBox(self.frame_32)
        self.cyc4SubLnEdtCorrespgTemp.setObjectName(u"cyc4SubLnEdtCorrespgTemp")
        self.cyc4SubLnEdtCorrespgTemp.setMinimumSize(QSize(450, 55))
        self.cyc4SubLnEdtCorrespgTemp.setMaximumSize(QSize(16777215, 55))
        self.cyc4SubLnEdtCorrespgTemp.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.cyc4SubLnEdtCorrespgTemp.setDecimals(6)
        self.cyc4SubLnEdtCorrespgTemp.setMaximum(10000000.000000000000000)
        self.cyc4SubLnEdtCorrespgTemp.setSingleStep(0.000000000000000)

        self.verticalLayout_45.addWidget(self.cyc4SubLnEdtCorrespgTemp)


        self.horizontalLayout_38.addWidget(self.frame_32)


        self.gridLayout_25.addWidget(self.cyc4SubFrame, 0, 1, 1, 1)

        self.horizontalSpacer_29 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_25.addItem(self.horizontalSpacer_29, 0, 0, 1, 1)

        self.horizontalSpacer_30 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_25.addItem(self.horizontalSpacer_30, 0, 2, 1, 1)

        self.cycleTabWidget.addTab(self.cycle4, "")

        self.gridLayout_13.addWidget(self.cycleTabWidget, 0, 0, 1, 1)

        self.scrollArea_4.setWidget(self.scrollAreaWidgetContents_2)

        self.gridLayout_17.addWidget(self.scrollArea_4, 0, 0, 1, 1)


        self.verticalLayout_15.addWidget(self.subFrameHtrCyc)

        self.frame_57 = QFrame(self.contentFrameHtrCyc)
        self.frame_57.setObjectName(u"frame_57")
        self.frame_57.setMinimumSize(QSize(0, 38))
        self.frame_57.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_57.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_46 = QHBoxLayout(self.frame_57)
        self.horizontalLayout_46.setObjectName(u"horizontalLayout_46")
        self.horizontalLayout_46.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_65 = QSpacerItem(287, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_46.addItem(self.horizontalSpacer_65)

        self.cycleNavigationBtnFrame = QFrame(self.frame_57)
        self.cycleNavigationBtnFrame.setObjectName(u"cycleNavigationBtnFrame")
        self.cycleNavigationBtnFrame.setMinimumSize(QSize(0, 38))
        self.cycleNavigationBtnFrame.setStyleSheet(u"QPushButton{\n"
"	border-radius:10px;\n"
"	padding:5px;\n"
"	font-size: 15px;\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	background-color:white;\n"
"}\n"
"\n"
"QPushButton:pressed{\n"
"	background-color:black;\n"
"}\n"
"")
        self.cycleNavigationBtnFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.cycleNavigationBtnFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_47 = QHBoxLayout(self.cycleNavigationBtnFrame)
        self.horizontalLayout_47.setSpacing(0)
        self.horizontalLayout_47.setObjectName(u"horizontalLayout_47")
        self.horizontalLayout_47.setContentsMargins(2, 0, 0, 3)
        self.btnCycleBack = QPushButton(self.cycleNavigationBtnFrame)
        self.btnCycleBack.setObjectName(u"btnCycleBack")
        self.btnCycleBack.setMinimumSize(QSize(90, 35))
        self.btnCycleBack.setMaximumSize(QSize(90, 50))
        self.btnCycleBack.setStyleSheet(u"")
        icon2 = QIcon()
        icon2.addFile(u"../../tempDevelop2/assets/left_arrow.drawio.png", QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        self.btnCycleBack.setIcon(icon2)
        self.btnCycleBack.setIconSize(QSize(70, 70))

        self.horizontalLayout_47.addWidget(self.btnCycleBack)

        self.horizontalSpacer_67 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_47.addItem(self.horizontalSpacer_67)

        self.btnCycleNext = QPushButton(self.cycleNavigationBtnFrame)
        self.btnCycleNext.setObjectName(u"btnCycleNext")
        self.btnCycleNext.setMinimumSize(QSize(90, 35))
        self.btnCycleNext.setMaximumSize(QSize(90, 50))
        self.btnCycleNext.setStyleSheet(u"")
        icon3 = QIcon()
        icon3.addFile(u"../../tempDevelop2/assets/right_arrow.drawio.png", QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        self.btnCycleNext.setIcon(icon3)
        self.btnCycleNext.setIconSize(QSize(70, 70))

        self.horizontalLayout_47.addWidget(self.btnCycleNext)


        self.horizontalLayout_46.addWidget(self.cycleNavigationBtnFrame)

        self.horizontalSpacer_66 = QSpacerItem(287, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_46.addItem(self.horizontalSpacer_66)


        self.verticalLayout_15.addWidget(self.frame_57)

        self.verticalSpacer_2 = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_15.addItem(self.verticalSpacer_2)


        self.gridLayout_26.addWidget(self.contentFrameHtrCyc, 0, 0, 1, 1)

        self.contentStack.addWidget(self.heaterCycles)
        self.postTestingObs = QWidget()
        self.postTestingObs.setObjectName(u"postTestingObs")
        self.gridLayout_38 = QGridLayout(self.postTestingObs)
        self.gridLayout_38.setObjectName(u"gridLayout_38")
        self.gridLayout_38.setContentsMargins(0, -1, 0, -1)
        self.contentFramePstTestObs = QFrame(self.postTestingObs)
        self.contentFramePstTestObs.setObjectName(u"contentFramePstTestObs")
        self.contentFramePstTestObs.setMinimumSize(QSize(1000, 600))
        self.contentFramePstTestObs.setMaximumSize(QSize(1200, 600))
        self.contentFramePstTestObs.setStyleSheet(u"")
        self.contentFramePstTestObs.setFrameShape(QFrame.Shape.NoFrame)
        self.contentFramePstTestObs.setFrameShadow(QFrame.Shadow.Raised)
        self.gridLayout_22 = QGridLayout(self.contentFramePstTestObs)
        self.gridLayout_22.setObjectName(u"gridLayout_22")
        self.scrollArea = QScrollArea(self.contentFramePstTestObs)
        self.scrollArea.setObjectName(u"scrollArea")
        self.scrollArea.setStyleSheet(u"QTabBar::tab:selected{\n"
"	background:#3E514B;\n"
"	border-radius:3px;\n"
"	width: 80px;\n"
"}\n"
"\n"
"QTabWidget::pane{\n"
"	border:none;\n"
"}\n"
"\n"
"QTabBar::tab{\n"
"	font-size: 17px;\n"
"	border: 5px solid #446699 ;\n"
"}\n"
"\n"
"\n"
"QLabel{\n"
"	background-color: #333333;\n"
"	border-radius:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QLineEdit, QSpinBox, QDoubleSpinBox{\n"
"	background-color: rgb(84, 103, 163);\n"
"	border:1px solid #9CAECA;\n"
"	border-radius: 5px;\n"
"	padding:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"/* Scrollbar styling */\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    width: 12px;\n"
"    margin: 0px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 4px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical,\n"
"QScrollBar::sub-line:vertical {\n"
""
                        "    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}")
        self.scrollArea.setWidgetResizable(True)
        self.scrollAreaWidgetContents_5 = QWidget()
        self.scrollAreaWidgetContents_5.setObjectName(u"scrollAreaWidgetContents_5")
        self.scrollAreaWidgetContents_5.setGeometry(QRect(0, 0, 984, 942))
        self.gridLayout_30 = QGridLayout(self.scrollAreaWidgetContents_5)
        self.gridLayout_30.setObjectName(u"gridLayout_30")
        self.gridLayout_30.setHorizontalSpacing(50)
        self.frame_65 = QFrame(self.scrollAreaWidgetContents_5)
        self.frame_65.setObjectName(u"frame_65")
        self.frame_65.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_65.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_57 = QHBoxLayout(self.frame_65)
        self.horizontalLayout_57.setObjectName(u"horizontalLayout_57")
        self.horizontalSpacer_69 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_57.addItem(self.horizontalSpacer_69)

        self.frame_72 = QFrame(self.frame_65)
        self.frame_72.setObjectName(u"frame_72")
        self.frame_72.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_72.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_66 = QVBoxLayout(self.frame_72)
        self.verticalLayout_66.setSpacing(20)
        self.verticalLayout_66.setObjectName(u"verticalLayout_66")
        self.verticalLayout_66.setContentsMargins(0, 0, 0, 0)
        self.subLblChmbrNo_3 = QLabel(self.frame_72)
        self.subLblChmbrNo_3.setObjectName(u"subLblChmbrNo_3")
        self.subLblChmbrNo_3.setMinimumSize(QSize(450, 55))
        self.subLblChmbrNo_3.setMaximumSize(QSize(16777215, 55))
        self.subLblChmbrNo_3.setStyleSheet(u"")
        self.subLblChmbrNo_3.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_66.addWidget(self.subLblChmbrNo_3)

        self.subLblChmbrLen_2 = QLabel(self.frame_72)
        self.subLblChmbrLen_2.setObjectName(u"subLblChmbrLen_2")
        self.subLblChmbrLen_2.setMinimumSize(QSize(450, 55))
        self.subLblChmbrLen_2.setMaximumSize(QSize(16777215, 55))
        self.subLblChmbrLen_2.setStyleSheet(u"")
        self.subLblChmbrLen_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_66.addWidget(self.subLblChmbrLen_2)

        self.subLblChmbrIntDia_2 = QLabel(self.frame_72)
        self.subLblChmbrIntDia_2.setObjectName(u"subLblChmbrIntDia_2")
        self.subLblChmbrIntDia_2.setMinimumSize(QSize(450, 55))
        self.subLblChmbrIntDia_2.setMaximumSize(QSize(16777215, 55))
        self.subLblChmbrIntDia_2.setStyleSheet(u"")
        self.subLblChmbrIntDia_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_66.addWidget(self.subLblChmbrIntDia_2)

        self.subLblChmbrExtDia_2 = QLabel(self.frame_72)
        self.subLblChmbrExtDia_2.setObjectName(u"subLblChmbrExtDia_2")
        self.subLblChmbrExtDia_2.setMinimumSize(QSize(450, 55))
        self.subLblChmbrExtDia_2.setMaximumSize(QSize(16777215, 55))
        self.subLblChmbrExtDia_2.setStyleSheet(u"")
        self.subLblChmbrExtDia_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_66.addWidget(self.subLblChmbrExtDia_2)

        self.subLblMeshCond_2 = QLabel(self.frame_72)
        self.subLblMeshCond_2.setObjectName(u"subLblMeshCond_2")
        self.subLblMeshCond_2.setMinimumSize(QSize(450, 55))
        self.subLblMeshCond_2.setMaximumSize(QSize(16777215, 55))
        self.subLblMeshCond_2.setStyleSheet(u"")
        self.subLblMeshCond_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_66.addWidget(self.subLblMeshCond_2)

        self.subLblRetainerPltCond_2 = QLabel(self.frame_72)
        self.subLblRetainerPltCond_2.setObjectName(u"subLblRetainerPltCond_2")
        self.subLblRetainerPltCond_2.setMinimumSize(QSize(450, 55))
        self.subLblRetainerPltCond_2.setMaximumSize(QSize(16777215, 55))
        self.subLblRetainerPltCond_2.setStyleSheet(u"")
        self.subLblRetainerPltCond_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_66.addWidget(self.subLblRetainerPltCond_2)

        self.subLblCatPhtoBfrFirg_2 = QLabel(self.frame_72)
        self.subLblCatPhtoBfrFirg_2.setObjectName(u"subLblCatPhtoBfrFirg_2")
        self.subLblCatPhtoBfrFirg_2.setMinimumSize(QSize(450, 55))
        self.subLblCatPhtoBfrFirg_2.setMaximumSize(QSize(16777215, 55))
        self.subLblCatPhtoBfrFirg_2.setStyleSheet(u"")
        self.subLblCatPhtoBfrFirg_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_66.addWidget(self.subLblCatPhtoBfrFirg_2)

        self.subLblCatPhtoAftFirg_2 = QLabel(self.frame_72)
        self.subLblCatPhtoAftFirg_2.setObjectName(u"subLblCatPhtoAftFirg_2")
        self.subLblCatPhtoAftFirg_2.setMinimumSize(QSize(450, 55))
        self.subLblCatPhtoAftFirg_2.setMaximumSize(QSize(16777215, 55))
        self.subLblCatPhtoAftFirg_2.setStyleSheet(u"")
        self.subLblCatPhtoAftFirg_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_66.addWidget(self.subLblCatPhtoAftFirg_2)

        self.subLblPropPhtoBfr_2 = QLabel(self.frame_72)
        self.subLblPropPhtoBfr_2.setObjectName(u"subLblPropPhtoBfr_2")
        self.subLblPropPhtoBfr_2.setMinimumSize(QSize(450, 55))
        self.subLblPropPhtoBfr_2.setMaximumSize(QSize(16777215, 55))
        self.subLblPropPhtoBfr_2.setStyleSheet(u"")
        self.subLblPropPhtoBfr_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_66.addWidget(self.subLblPropPhtoBfr_2)

        self.subLblPropPhtoAft_2 = QLabel(self.frame_72)
        self.subLblPropPhtoAft_2.setObjectName(u"subLblPropPhtoAft_2")
        self.subLblPropPhtoAft_2.setMinimumSize(QSize(450, 55))
        self.subLblPropPhtoAft_2.setMaximumSize(QSize(16777215, 55))
        self.subLblPropPhtoAft_2.setStyleSheet(u"")
        self.subLblPropPhtoAft_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_66.addWidget(self.subLblPropPhtoAft_2)


        self.horizontalLayout_57.addWidget(self.frame_72)

        self.frame_73 = QFrame(self.frame_65)
        self.frame_73.setObjectName(u"frame_73")
        self.frame_73.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_73.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_62 = QVBoxLayout(self.frame_73)
        self.verticalLayout_62.setSpacing(20)
        self.verticalLayout_62.setObjectName(u"verticalLayout_62")
        self.verticalLayout_62.setContentsMargins(0, 0, 0, 0)
        self.subLnEdtChmbrNoPostTestObs = QLineEdit(self.frame_73)
        self.subLnEdtChmbrNoPostTestObs.setObjectName(u"subLnEdtChmbrNoPostTestObs")
        self.subLnEdtChmbrNoPostTestObs.setMinimumSize(QSize(450, 55))
        self.subLnEdtChmbrNoPostTestObs.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtChmbrNoPostTestObs.setStyleSheet(u"")

        self.verticalLayout_62.addWidget(self.subLnEdtChmbrNoPostTestObs)

        self.subLnEdtChmbrLen_2 = CustomDoubleSpinBox(self.frame_73)
        self.subLnEdtChmbrLen_2.setObjectName(u"subLnEdtChmbrLen_2")
        self.subLnEdtChmbrLen_2.setMinimumSize(QSize(450, 55))
        self.subLnEdtChmbrLen_2.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtChmbrLen_2.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtChmbrLen_2.setDecimals(6)
        self.subLnEdtChmbrLen_2.setMaximum(10000000.000000000000000)
        self.subLnEdtChmbrLen_2.setSingleStep(0.000000000000000)

        self.verticalLayout_62.addWidget(self.subLnEdtChmbrLen_2)

        self.subLnEdtChmbrIntDia_2 = CustomDoubleSpinBox(self.frame_73)
        self.subLnEdtChmbrIntDia_2.setObjectName(u"subLnEdtChmbrIntDia_2")
        self.subLnEdtChmbrIntDia_2.setMinimumSize(QSize(450, 55))
        self.subLnEdtChmbrIntDia_2.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtChmbrIntDia_2.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtChmbrIntDia_2.setDecimals(6)
        self.subLnEdtChmbrIntDia_2.setMaximum(10000000.000000000000000)
        self.subLnEdtChmbrIntDia_2.setSingleStep(0.000000000000000)

        self.verticalLayout_62.addWidget(self.subLnEdtChmbrIntDia_2)

        self.subLnEdtChmbrExtDia_2 = CustomDoubleSpinBox(self.frame_73)
        self.subLnEdtChmbrExtDia_2.setObjectName(u"subLnEdtChmbrExtDia_2")
        self.subLnEdtChmbrExtDia_2.setMinimumSize(QSize(450, 55))
        self.subLnEdtChmbrExtDia_2.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtChmbrExtDia_2.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtChmbrExtDia_2.setDecimals(6)
        self.subLnEdtChmbrExtDia_2.setMaximum(10000000.000000000000000)
        self.subLnEdtChmbrExtDia_2.setSingleStep(0.000000000000000)

        self.verticalLayout_62.addWidget(self.subLnEdtChmbrExtDia_2)

        self.subLnEdtMeshCond_2 = QLineEdit(self.frame_73)
        self.subLnEdtMeshCond_2.setObjectName(u"subLnEdtMeshCond_2")
        self.subLnEdtMeshCond_2.setMinimumSize(QSize(450, 55))
        self.subLnEdtMeshCond_2.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtMeshCond_2.setStyleSheet(u"")

        self.verticalLayout_62.addWidget(self.subLnEdtMeshCond_2)

        self.subLnEdtRetainerPltCond_2 = QLineEdit(self.frame_73)
        self.subLnEdtRetainerPltCond_2.setObjectName(u"subLnEdtRetainerPltCond_2")
        self.subLnEdtRetainerPltCond_2.setMinimumSize(QSize(450, 55))
        self.subLnEdtRetainerPltCond_2.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtRetainerPltCond_2.setStyleSheet(u"")

        self.verticalLayout_62.addWidget(self.subLnEdtRetainerPltCond_2)

        self.widgetCatPhotoBef_2 = QWidget(self.frame_73)
        self.widgetCatPhotoBef_2.setObjectName(u"widgetCatPhotoBef_2")
        self.widgetCatPhotoBef_2.setMinimumSize(QSize(0, 55))
        self.widgetCatPhotoBef_2.setMaximumSize(QSize(16777215, 55))
        self.horizontalLayout_53 = QHBoxLayout(self.widgetCatPhotoBef_2)
        self.horizontalLayout_53.setObjectName(u"horizontalLayout_53")
        self.horizontalLayout_53.setContentsMargins(0, 0, 0, 0)
        self.subLnEdtCatPhtoBfr_2 = QLineEdit(self.widgetCatPhotoBef_2)
        self.subLnEdtCatPhtoBfr_2.setObjectName(u"subLnEdtCatPhtoBfr_2")
        self.subLnEdtCatPhtoBfr_2.setMinimumSize(QSize(300, 50))
        self.subLnEdtCatPhtoBfr_2.setMaximumSize(QSize(16777215, 50))
        self.subLnEdtCatPhtoBfr_2.setStyleSheet(u"font-size: 13px;")
        self.subLnEdtCatPhtoBfr_2.setReadOnly(True)

        self.horizontalLayout_53.addWidget(self.subLnEdtCatPhtoBfr_2)

        self.btnCatPhotoBef_2 = QPushButton(self.widgetCatPhotoBef_2)
        self.btnCatPhotoBef_2.setObjectName(u"btnCatPhotoBef_2")
        self.btnCatPhotoBef_2.setMinimumSize(QSize(80, 35))
        self.btnCatPhotoBef_2.setMaximumSize(QSize(80, 35))
        font5 = QFont()
        font5.setPointSize(8)
        self.btnCatPhotoBef_2.setFont(font5)
        self.btnCatPhotoBef_2.setStyleSheet(u"border:1px solid gray;")

        self.horizontalLayout_53.addWidget(self.btnCatPhotoBef_2)

        self.lblCatPhotoPrevBef_2 = QLabel(self.widgetCatPhotoBef_2)
        self.lblCatPhotoPrevBef_2.setObjectName(u"lblCatPhotoPrevBef_2")
        self.lblCatPhotoPrevBef_2.setMinimumSize(QSize(50, 50))
        self.lblCatPhotoPrevBef_2.setMaximumSize(QSize(45, 45))

        self.horizontalLayout_53.addWidget(self.lblCatPhotoPrevBef_2)


        self.verticalLayout_62.addWidget(self.widgetCatPhotoBef_2)

        self.widgetCatPhotoAft_2 = QWidget(self.frame_73)
        self.widgetCatPhotoAft_2.setObjectName(u"widgetCatPhotoAft_2")
        self.widgetCatPhotoAft_2.setMinimumSize(QSize(0, 55))
        self.widgetCatPhotoAft_2.setMaximumSize(QSize(16777215, 55))
        self.horizontalLayout_54 = QHBoxLayout(self.widgetCatPhotoAft_2)
        self.horizontalLayout_54.setObjectName(u"horizontalLayout_54")
        self.horizontalLayout_54.setContentsMargins(0, 0, 0, 0)
        self.subLnEdtCatPhtoAft_2 = QLineEdit(self.widgetCatPhotoAft_2)
        self.subLnEdtCatPhtoAft_2.setObjectName(u"subLnEdtCatPhtoAft_2")
        self.subLnEdtCatPhtoAft_2.setMinimumSize(QSize(300, 50))
        self.subLnEdtCatPhtoAft_2.setMaximumSize(QSize(16777215, 50))
        self.subLnEdtCatPhtoAft_2.setStyleSheet(u"font-size: 13px;")
        self.subLnEdtCatPhtoAft_2.setReadOnly(True)

        self.horizontalLayout_54.addWidget(self.subLnEdtCatPhtoAft_2)

        self.btnCatPhotoAft_2 = QPushButton(self.widgetCatPhotoAft_2)
        self.btnCatPhotoAft_2.setObjectName(u"btnCatPhotoAft_2")
        self.btnCatPhotoAft_2.setMinimumSize(QSize(80, 35))
        self.btnCatPhotoAft_2.setMaximumSize(QSize(80, 35))
        self.btnCatPhotoAft_2.setFont(font5)
        self.btnCatPhotoAft_2.setStyleSheet(u"border:1px solid gray;")

        self.horizontalLayout_54.addWidget(self.btnCatPhotoAft_2)

        self.lblCatPhotoPrevAft_2 = QLabel(self.widgetCatPhotoAft_2)
        self.lblCatPhotoPrevAft_2.setObjectName(u"lblCatPhotoPrevAft_2")
        self.lblCatPhotoPrevAft_2.setMinimumSize(QSize(50, 50))
        self.lblCatPhotoPrevAft_2.setMaximumSize(QSize(45, 45))

        self.horizontalLayout_54.addWidget(self.lblCatPhotoPrevAft_2)


        self.verticalLayout_62.addWidget(self.widgetCatPhotoAft_2)

        self.widgetPropPhotoBef_2 = QWidget(self.frame_73)
        self.widgetPropPhotoBef_2.setObjectName(u"widgetPropPhotoBef_2")
        self.widgetPropPhotoBef_2.setMinimumSize(QSize(0, 55))
        self.widgetPropPhotoBef_2.setMaximumSize(QSize(16777215, 55))
        self.horizontalLayout_55 = QHBoxLayout(self.widgetPropPhotoBef_2)
        self.horizontalLayout_55.setObjectName(u"horizontalLayout_55")
        self.horizontalLayout_55.setContentsMargins(0, 0, 0, 0)
        self.subLnEdtPropPhtoBfr_2 = QLineEdit(self.widgetPropPhotoBef_2)
        self.subLnEdtPropPhtoBfr_2.setObjectName(u"subLnEdtPropPhtoBfr_2")
        self.subLnEdtPropPhtoBfr_2.setMinimumSize(QSize(300, 50))
        self.subLnEdtPropPhtoBfr_2.setMaximumSize(QSize(16777215, 50))
        self.subLnEdtPropPhtoBfr_2.setStyleSheet(u"font-size: 13px;\n"
"")
        self.subLnEdtPropPhtoBfr_2.setReadOnly(True)

        self.horizontalLayout_55.addWidget(self.subLnEdtPropPhtoBfr_2)

        self.btnPropPhotoBef_2 = QPushButton(self.widgetPropPhotoBef_2)
        self.btnPropPhotoBef_2.setObjectName(u"btnPropPhotoBef_2")
        self.btnPropPhotoBef_2.setMinimumSize(QSize(80, 35))
        self.btnPropPhotoBef_2.setMaximumSize(QSize(80, 35))
        self.btnPropPhotoBef_2.setFont(font5)
        self.btnPropPhotoBef_2.setStyleSheet(u"border:1px solid gray;")

        self.horizontalLayout_55.addWidget(self.btnPropPhotoBef_2)

        self.lblPropPhotoPrevBef_2 = QLabel(self.widgetPropPhotoBef_2)
        self.lblPropPhotoPrevBef_2.setObjectName(u"lblPropPhotoPrevBef_2")
        self.lblPropPhotoPrevBef_2.setMinimumSize(QSize(50, 50))
        self.lblPropPhotoPrevBef_2.setMaximumSize(QSize(45, 45))

        self.horizontalLayout_55.addWidget(self.lblPropPhotoPrevBef_2)


        self.verticalLayout_62.addWidget(self.widgetPropPhotoBef_2)

        self.widgetPropPhotoAft_2 = QWidget(self.frame_73)
        self.widgetPropPhotoAft_2.setObjectName(u"widgetPropPhotoAft_2")
        self.widgetPropPhotoAft_2.setMinimumSize(QSize(0, 55))
        self.widgetPropPhotoAft_2.setMaximumSize(QSize(16777215, 55))
        self.horizontalLayout_56 = QHBoxLayout(self.widgetPropPhotoAft_2)
        self.horizontalLayout_56.setObjectName(u"horizontalLayout_56")
        self.horizontalLayout_56.setContentsMargins(0, 0, 0, 0)
        self.subLnEdtPropPhtoAft_2 = QLineEdit(self.widgetPropPhotoAft_2)
        self.subLnEdtPropPhtoAft_2.setObjectName(u"subLnEdtPropPhtoAft_2")
        self.subLnEdtPropPhtoAft_2.setMinimumSize(QSize(300, 50))
        self.subLnEdtPropPhtoAft_2.setMaximumSize(QSize(16777215, 50))
        self.subLnEdtPropPhtoAft_2.setStyleSheet(u"font-size: 13px;")
        self.subLnEdtPropPhtoAft_2.setReadOnly(True)

        self.horizontalLayout_56.addWidget(self.subLnEdtPropPhtoAft_2)

        self.btnPropPhotoAft_2 = QPushButton(self.widgetPropPhotoAft_2)
        self.btnPropPhotoAft_2.setObjectName(u"btnPropPhotoAft_2")
        self.btnPropPhotoAft_2.setMinimumSize(QSize(80, 35))
        self.btnPropPhotoAft_2.setMaximumSize(QSize(80, 35))
        self.btnPropPhotoAft_2.setFont(font5)
        self.btnPropPhotoAft_2.setStyleSheet(u"border:1px solid gray;")

        self.horizontalLayout_56.addWidget(self.btnPropPhotoAft_2)

        self.lblPropPhotoPrevAft_2 = QLabel(self.widgetPropPhotoAft_2)
        self.lblPropPhotoPrevAft_2.setObjectName(u"lblPropPhotoPrevAft_2")
        self.lblPropPhotoPrevAft_2.setMinimumSize(QSize(50, 50))
        self.lblPropPhotoPrevAft_2.setMaximumSize(QSize(45, 45))

        self.horizontalLayout_56.addWidget(self.lblPropPhotoPrevAft_2)


        self.verticalLayout_62.addWidget(self.widgetPropPhotoAft_2)


        self.horizontalLayout_57.addWidget(self.frame_73)

        self.horizontalSpacer_77 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_57.addItem(self.horizontalSpacer_77)


        self.gridLayout_30.addWidget(self.frame_65, 0, 0, 1, 1)

        self.frame_71 = QFrame(self.scrollAreaWidgetContents_5)
        self.frame_71.setObjectName(u"frame_71")
        self.frame_71.setMinimumSize(QSize(0, 150))
        self.frame_71.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_71.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_58 = QHBoxLayout(self.frame_71)
        self.horizontalLayout_58.setObjectName(u"horizontalLayout_58")
        self.horizontalLayout_58.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_78 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_58.addItem(self.horizontalSpacer_78)

        self.frame_74 = QFrame(self.frame_71)
        self.frame_74.setObjectName(u"frame_74")
        self.frame_74.setMinimumSize(QSize(900, 0))
        self.frame_74.setStyleSheet(u"background-color: #333333")
        self.frame_74.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_74.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_16 = QVBoxLayout(self.frame_74)
        self.verticalLayout_16.setSpacing(0)
        self.verticalLayout_16.setObjectName(u"verticalLayout_16")
        self.verticalLayout_16.setContentsMargins(0, 0, 0, 0)
        self.frame_75 = QFrame(self.frame_74)
        self.frame_75.setObjectName(u"frame_75")
        self.frame_75.setMaximumSize(QSize(16777215, 45))
        self.frame_75.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_75.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_59 = QHBoxLayout(self.frame_75)
        self.horizontalLayout_59.setObjectName(u"horizontalLayout_59")
        self.horizontalLayout_59.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_80 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_59.addItem(self.horizontalSpacer_80)

        self.lineEdit = QLineEdit(self.frame_75)
        self.lineEdit.setObjectName(u"lineEdit")
        self.lineEdit.setMinimumSize(QSize(0, 40))
        self.lineEdit.setStyleSheet(u"background-color: #8c2db2")
        self.lineEdit.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.lineEdit.setReadOnly(True)

        self.horizontalLayout_59.addWidget(self.lineEdit)

        self.horizontalSpacer_81 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_59.addItem(self.horizontalSpacer_81)


        self.verticalLayout_16.addWidget(self.frame_75)

        self.subLnEdtNote = QPlainTextEdit(self.frame_74)
        self.subLnEdtNote.setObjectName(u"subLnEdtNote")
        self.subLnEdtNote.setMinimumSize(QSize(0, 80))
        self.subLnEdtNote.setStyleSheet(u"QPlainTextEdit{\n"
"background-color:#5467a3;\n"
"border:1px solid ivery;\n"
"color: black;\n"
"font-family: Arial;\n"
"font-size: 17px;\n"
"}\n"
"\n"
"/* Scrollbar styling */\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    width: 12px;\n"
"    margin: 0px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 4px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}")

        self.verticalLayout_16.addWidget(self.subLnEdtNote)


        self.horizontalLayout_58.addWidget(self.frame_74)

        self.horizontalSpacer_79 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_58.addItem(self.horizontalSpacer_79)


        self.gridLayout_30.addWidget(self.frame_71, 1, 0, 1, 1)

        self.scrollArea.setWidget(self.scrollAreaWidgetContents_5)

        self.gridLayout_22.addWidget(self.scrollArea, 1, 0, 1, 1)

        self.frame_21 = QFrame(self.contentFramePstTestObs)
        self.frame_21.setObjectName(u"frame_21")
        self.frame_21.setMinimumSize(QSize(0, 50))
        self.frame_21.setMaximumSize(QSize(16777215, 50))
        self.frame_21.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_21.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_13 = QHBoxLayout(self.frame_21)
        self.horizontalLayout_13.setSpacing(0)
        self.horizontalLayout_13.setObjectName(u"horizontalLayout_13")
        self.horizontalLayout_13.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_31 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_13.addItem(self.horizontalSpacer_31)

        self.subSecPostTestObs = QLabel(self.frame_21)
        self.subSecPostTestObs.setObjectName(u"subSecPostTestObs")
        self.subSecPostTestObs.setMinimumSize(QSize(260, 45))
        self.subSecPostTestObs.setMaximumSize(QSize(280, 16777215))
        self.subSecPostTestObs.setFont(font3)
        self.subSecPostTestObs.setStyleSheet(u"background-color: #252525;\n"
"border-radius: 17px;\n"
"padding:5px;\n"
"border: 2px solid red;\n"
"")
        self.subSecPostTestObs.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_13.addWidget(self.subSecPostTestObs)

        self.horizontalSpacer_52 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_13.addItem(self.horizontalSpacer_52)


        self.gridLayout_22.addWidget(self.frame_21, 0, 0, 1, 1)


        self.gridLayout_38.addWidget(self.contentFramePstTestObs, 0, 0, 1, 1)

        self.contentStack.addWidget(self.postTestingObs)
        self.catalystPostAna = QWidget()
        self.catalystPostAna.setObjectName(u"catalystPostAna")
        self.gridLayout_27 = QGridLayout(self.catalystPostAna)
        self.gridLayout_27.setObjectName(u"gridLayout_27")
        self.gridLayout_27.setContentsMargins(0, -1, 0, -1)
        self.contentFrameCatPstAn = QFrame(self.catalystPostAna)
        self.contentFrameCatPstAn.setObjectName(u"contentFrameCatPstAn")
        self.contentFrameCatPstAn.setMinimumSize(QSize(1000, 600))
        self.contentFrameCatPstAn.setMaximumSize(QSize(1200, 600))
        self.contentFrameCatPstAn.setStyleSheet(u"")
        self.contentFrameCatPstAn.setFrameShape(QFrame.Shape.NoFrame)
        self.contentFrameCatPstAn.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_19 = QVBoxLayout(self.contentFrameCatPstAn)
        self.verticalLayout_19.setObjectName(u"verticalLayout_19")
        self.verticalLayout_19.setContentsMargins(5, 5, 5, -1)
        self.frame_25 = QFrame(self.contentFrameCatPstAn)
        self.frame_25.setObjectName(u"frame_25")
        self.frame_25.setMinimumSize(QSize(0, 50))
        self.frame_25.setMaximumSize(QSize(16777215, 50))
        self.frame_25.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_25.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_15 = QHBoxLayout(self.frame_25)
        self.horizontalLayout_15.setSpacing(0)
        self.horizontalLayout_15.setObjectName(u"horizontalLayout_15")
        self.horizontalLayout_15.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_37 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_15.addItem(self.horizontalSpacer_37)

        self.subSecCatPstAn = QLabel(self.frame_25)
        self.subSecCatPstAn.setObjectName(u"subSecCatPstAn")
        self.subSecCatPstAn.setMinimumSize(QSize(220, 45))
        self.subSecCatPstAn.setMaximumSize(QSize(16777215, 16777215))
        self.subSecCatPstAn.setFont(font3)
        self.subSecCatPstAn.setStyleSheet(u"background-color: #252525;\n"
"border-radius: 17px;\n"
"padding:5px;\n"
"border: 2px solid red;\n"
"")
        self.subSecCatPstAn.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_15.addWidget(self.subSecCatPstAn)

        self.horizontalSpacer_53 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_15.addItem(self.horizontalSpacer_53)


        self.verticalLayout_19.addWidget(self.frame_25)

        self.subFrameCatPstAn = QFrame(self.contentFrameCatPstAn)
        self.subFrameCatPstAn.setObjectName(u"subFrameCatPstAn")
        self.subFrameCatPstAn.setStyleSheet(u"QTabBar::tab:selected{\n"
"	background:#3E514B;\n"
"	border-radius:3px;\n"
"	width: 80px;\n"
"}\n"
"\n"
"QTabWidget::pane{\n"
"	border:none;\n"
"}\n"
"\n"
"QTabBar::tab{\n"
"	font-size: 17px;\n"
"	border: 5px solid #446699 ;\n"
"}\n"
"\n"
"\n"
"QLabel{\n"
"	background-color: #333333;\n"
"	border-radius:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QLineEdit{\n"
"	background-color: rgb(84, 103, 163);\n"
"	border:1px solid #9CAECA;\n"
"	border-radius: 5px;\n"
"	padding:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QTabBar::tab:selected{\n"
"	background:#3E514B;\n"
"	border-radius:3px;\n"
"	width: 80px;\n"
"}\n"
"\n"
"QTabWidget::pane{\n"
"	border:none;\n"
"}\n"
"\n"
"QTabBar::tab{\n"
"	font-size: 17px;\n"
"	border: 5px solid #446699 ;\n"
"}\n"
"\n"
"\n"
"QLabel{\n"
"	background-color: #333333;\n"
"	border-radius:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QLineEdit, QDoubleSpinBox{\n"
"	background-color: rgb(84, 103, 163);\n"
"	border:1px solid #9C"
                        "AECA;\n"
"	border-radius: 5px;\n"
"	padding:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"")
        self.subFrameCatPstAn.setFrameShape(QFrame.Shape.StyledPanel)
        self.subFrameCatPstAn.setFrameShadow(QFrame.Shadow.Raised)
        self.gridLayout_29 = QGridLayout(self.subFrameCatPstAn)
        self.gridLayout_29.setObjectName(u"gridLayout_29")
        self.gridLayout_29.setHorizontalSpacing(0)
        self.gridLayout_29.setVerticalSpacing(40)
        self.horizontalSpacer_72 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_29.addItem(self.horizontalSpacer_72, 0, 0, 1, 1)

        self.frame_56 = QFrame(self.subFrameCatPstAn)
        self.frame_56.setObjectName(u"frame_56")
        self.frame_56.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_56.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_69 = QVBoxLayout(self.frame_56)
        self.verticalLayout_69.setSpacing(20)
        self.verticalLayout_69.setObjectName(u"verticalLayout_69")
        self.subLblCatDet = QLabel(self.frame_56)
        self.subLblCatDet.setObjectName(u"subLblCatDet")
        self.subLblCatDet.setMinimumSize(QSize(450, 55))
        self.subLblCatDet.setMaximumSize(QSize(16777215, 55))
        self.subLblCatDet.setStyleSheet(u"")
        self.subLblCatDet.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_69.addWidget(self.subLblCatDet)

        self.subLblCatColBfr = QLabel(self.frame_56)
        self.subLblCatColBfr.setObjectName(u"subLblCatColBfr")
        self.subLblCatColBfr.setMinimumSize(QSize(450, 55))
        self.subLblCatColBfr.setMaximumSize(QSize(16777215, 55))
        self.subLblCatColBfr.setStyleSheet(u"")
        self.subLblCatColBfr.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_69.addWidget(self.subLblCatColBfr)

        self.subLblCatColAft = QLabel(self.frame_56)
        self.subLblCatColAft.setObjectName(u"subLblCatColAft")
        self.subLblCatColAft.setMinimumSize(QSize(450, 55))
        self.subLblCatColAft.setMaximumSize(QSize(16777215, 55))
        self.subLblCatColAft.setStyleSheet(u"")
        self.subLblCatColAft.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_69.addWidget(self.subLblCatColAft)

        self.subLblCatWghtFild = QLabel(self.frame_56)
        self.subLblCatWghtFild.setObjectName(u"subLblCatWghtFild")
        self.subLblCatWghtFild.setMinimumSize(QSize(450, 55))
        self.subLblCatWghtFild.setMaximumSize(QSize(16777215, 55))
        self.subLblCatWghtFild.setStyleSheet(u"")
        self.subLblCatWghtFild.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_69.addWidget(self.subLblCatWghtFild)

        self.subLblCatWghtRecvrd = QLabel(self.frame_56)
        self.subLblCatWghtRecvrd.setObjectName(u"subLblCatWghtRecvrd")
        self.subLblCatWghtRecvrd.setMinimumSize(QSize(450, 55))
        self.subLblCatWghtRecvrd.setMaximumSize(QSize(16777215, 55))
        self.subLblCatWghtRecvrd.setStyleSheet(u"")
        self.subLblCatWghtRecvrd.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_69.addWidget(self.subLblCatWghtRecvrd)

        self.subLblCatLosPerc = QLabel(self.frame_56)
        self.subLblCatLosPerc.setObjectName(u"subLblCatLosPerc")
        self.subLblCatLosPerc.setMinimumSize(QSize(450, 55))
        self.subLblCatLosPerc.setMaximumSize(QSize(16777215, 55))
        self.subLblCatLosPerc.setStyleSheet(u"")
        self.subLblCatLosPerc.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_69.addWidget(self.subLblCatLosPerc)


        self.gridLayout_29.addWidget(self.frame_56, 0, 1, 1, 1)

        self.frame_55 = QFrame(self.subFrameCatPstAn)
        self.frame_55.setObjectName(u"frame_55")
        self.frame_55.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_55.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_68 = QVBoxLayout(self.frame_55)
        self.verticalLayout_68.setSpacing(20)
        self.verticalLayout_68.setObjectName(u"verticalLayout_68")
        self.subLnEdtCatDet = QLineEdit(self.frame_55)
        self.subLnEdtCatDet.setObjectName(u"subLnEdtCatDet")
        self.subLnEdtCatDet.setMinimumSize(QSize(450, 55))
        self.subLnEdtCatDet.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtCatDet.setStyleSheet(u"")

        self.verticalLayout_68.addWidget(self.subLnEdtCatDet)

        self.subLnEdtCatColBfr = QLineEdit(self.frame_55)
        self.subLnEdtCatColBfr.setObjectName(u"subLnEdtCatColBfr")
        self.subLnEdtCatColBfr.setMinimumSize(QSize(450, 55))
        self.subLnEdtCatColBfr.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtCatColBfr.setStyleSheet(u"")

        self.verticalLayout_68.addWidget(self.subLnEdtCatColBfr)

        self.subLnEdtCatColAft = QLineEdit(self.frame_55)
        self.subLnEdtCatColAft.setObjectName(u"subLnEdtCatColAft")
        self.subLnEdtCatColAft.setMinimumSize(QSize(450, 55))
        self.subLnEdtCatColAft.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtCatColAft.setStyleSheet(u"")

        self.verticalLayout_68.addWidget(self.subLnEdtCatColAft)

        self.subLnEdtCatWghtFild = CustomDoubleSpinBox(self.frame_55)
        self.subLnEdtCatWghtFild.setObjectName(u"subLnEdtCatWghtFild")
        self.subLnEdtCatWghtFild.setMinimumSize(QSize(450, 55))
        self.subLnEdtCatWghtFild.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtCatWghtFild.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtCatWghtFild.setDecimals(6)
        self.subLnEdtCatWghtFild.setMaximum(10000000.000000000000000)
        self.subLnEdtCatWghtFild.setSingleStep(0.000000000000000)

        self.verticalLayout_68.addWidget(self.subLnEdtCatWghtFild)

        self.subLnEdtCatWghtRecvrd = CustomDoubleSpinBox(self.frame_55)
        self.subLnEdtCatWghtRecvrd.setObjectName(u"subLnEdtCatWghtRecvrd")
        self.subLnEdtCatWghtRecvrd.setMinimumSize(QSize(450, 55))
        self.subLnEdtCatWghtRecvrd.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtCatWghtRecvrd.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtCatWghtRecvrd.setDecimals(6)
        self.subLnEdtCatWghtRecvrd.setMaximum(10000000.000000000000000)
        self.subLnEdtCatWghtRecvrd.setSingleStep(0.000000000000000)

        self.verticalLayout_68.addWidget(self.subLnEdtCatWghtRecvrd)

        self.subLnEdtCatLosPerc = CustomDoubleSpinBox(self.frame_55)
        self.subLnEdtCatLosPerc.setObjectName(u"subLnEdtCatLosPerc")
        self.subLnEdtCatLosPerc.setMinimumSize(QSize(450, 55))
        self.subLnEdtCatLosPerc.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtCatLosPerc.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtCatLosPerc.setMinimum(-100.000000000000000)
        self.subLnEdtCatLosPerc.setMaximum(500.000000000000000)
        self.subLnEdtCatLosPerc.setSingleStep(0.000000000000000)

        self.verticalLayout_68.addWidget(self.subLnEdtCatLosPerc)


        self.gridLayout_29.addWidget(self.frame_55, 0, 2, 1, 1)

        self.horizontalSpacer_73 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_29.addItem(self.horizontalSpacer_73, 0, 3, 1, 1)


        self.verticalLayout_19.addWidget(self.subFrameCatPstAn)


        self.gridLayout_27.addWidget(self.contentFrameCatPstAn, 0, 0, 1, 1)

        self.contentStack.addWidget(self.catalystPostAna)
        self.propellantPostAn = QWidget()
        self.propellantPostAn.setObjectName(u"propellantPostAn")
        self.gridLayout_9 = QGridLayout(self.propellantPostAn)
        self.gridLayout_9.setObjectName(u"gridLayout_9")
        self.gridLayout_9.setContentsMargins(0, 0, 0, 0)
        self.contentFramePropPstAn = QFrame(self.propellantPostAn)
        self.contentFramePropPstAn.setObjectName(u"contentFramePropPstAn")
        self.contentFramePropPstAn.setMinimumSize(QSize(1000, 600))
        self.contentFramePropPstAn.setMaximumSize(QSize(1200, 600))
        self.contentFramePropPstAn.setStyleSheet(u"")
        self.contentFramePropPstAn.setFrameShape(QFrame.Shape.NoFrame)
        self.contentFramePropPstAn.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_65 = QVBoxLayout(self.contentFramePropPstAn)
        self.verticalLayout_65.setObjectName(u"verticalLayout_65")
        self.verticalLayout_65.setContentsMargins(0, 0, 0, 0)
        self.frame_52 = QFrame(self.contentFramePropPstAn)
        self.frame_52.setObjectName(u"frame_52")
        self.frame_52.setMinimumSize(QSize(0, 50))
        self.frame_52.setMaximumSize(QSize(16777215, 50))
        self.frame_52.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_52.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_16 = QHBoxLayout(self.frame_52)
        self.horizontalLayout_16.setSpacing(0)
        self.horizontalLayout_16.setObjectName(u"horizontalLayout_16")
        self.horizontalLayout_16.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_40 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_16.addItem(self.horizontalSpacer_40)

        self.subSecPropPstAn = QLabel(self.frame_52)
        self.subSecPropPstAn.setObjectName(u"subSecPropPstAn")
        self.subSecPropPstAn.setMinimumSize(QSize(240, 45))
        self.subSecPropPstAn.setMaximumSize(QSize(16777215, 16777215))
        self.subSecPropPstAn.setFont(font3)
        self.subSecPropPstAn.setStyleSheet(u"background-color: #252525;\n"
"border-radius: 17px;\n"
"padding:5px;\n"
"border: 2px solid red;\n"
"")
        self.subSecPropPstAn.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_16.addWidget(self.subSecPropPstAn)

        self.horizontalSpacer_54 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_16.addItem(self.horizontalSpacer_54)


        self.verticalLayout_65.addWidget(self.frame_52)

        self.scrollArea_2 = QScrollArea(self.contentFramePropPstAn)
        self.scrollArea_2.setObjectName(u"scrollArea_2")
        self.scrollArea_2.setStyleSheet(u"QTabBar::tab:selected{\n"
"	background:#3E514B;\n"
"	border-radius:3px;\n"
"	width: 80px;\n"
"}\n"
"\n"
"QTabWidget::pane{\n"
"	border:none;\n"
"}\n"
"\n"
"QTabBar::tab{\n"
"	font-size: 17px;\n"
"	border: 5px solid #446699 ;\n"
"}\n"
"\n"
"\n"
"QLabel{\n"
"	background-color: #333333;\n"
"	border-radius:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QLineEdit, QDoubleSpinBox{\n"
"	background-color: rgb(84, 103, 163);\n"
"	border:1px solid #9CAECA;\n"
"	border-radius: 5px;\n"
"	padding:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"/* Scrollbar styling */\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    width: 12px;\n"
"    margin: 0px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 4px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical,\n"
"QScrollBar::sub-line:vertical {\n"
"    heig"
                        "ht: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}")
        self.scrollArea_2.setWidgetResizable(True)
        self.scrollAreaWidgetContents_6 = QWidget()
        self.scrollAreaWidgetContents_6.setObjectName(u"scrollAreaWidgetContents_6")
        self.scrollAreaWidgetContents_6.setGeometry(QRect(0, 0, 1020, 1075))
        self.gridLayout_14 = QGridLayout(self.scrollAreaWidgetContents_6)
        self.gridLayout_14.setObjectName(u"gridLayout_14")
        self.frame_49 = QFrame(self.scrollAreaWidgetContents_6)
        self.frame_49.setObjectName(u"frame_49")
        self.frame_49.setStyleSheet(u"QLabel{\n"
"	background-color:#fefefa;\n"
"	color:black;\n"
"\n"
"}\n"
"")
        self.frame_49.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_49.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_42 = QHBoxLayout(self.frame_49)
        self.horizontalLayout_42.setObjectName(u"horizontalLayout_42")
        self.horizontalSpacer_39 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_42.addItem(self.horizontalSpacer_39)

        self.frame_69 = QFrame(self.frame_49)
        self.frame_69.setObjectName(u"frame_69")
        self.frame_69.setStyleSheet(u"QFrame#frame_69{\n"
"border:2px solid ivery\n"
"}")
        self.frame_69.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_69.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_44 = QHBoxLayout(self.frame_69)
        self.horizontalLayout_44.setObjectName(u"horizontalLayout_44")
        self.frame_67 = QFrame(self.frame_69)
        self.frame_67.setObjectName(u"frame_67")
        self.frame_67.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_67.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_22 = QVBoxLayout(self.frame_67)
        self.verticalLayout_22.setObjectName(u"verticalLayout_22")
        self.btnUpdateTable = QPushButton(self.frame_67)
        self.btnUpdateTable.setObjectName(u"btnUpdateTable")
        self.btnUpdateTable.setMinimumSize(QSize(150, 55))
        self.btnUpdateTable.setMaximumSize(QSize(16777215, 55))
        self.btnUpdateTable.setStyleSheet(u"QPushButton{\n"
"	background-color:green;\n"
"	border-radius: 22px;\n"
"	color:white;\n"
"	font-family: Arial;\n"
"	font-size:17px;\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	background-color:#a3a8ac;\n"
"}\n"
"\n"
"\n"
"QPushButton:pressed{\n"
"	background-color:#e60e70;\n"
"}")

        self.verticalLayout_22.addWidget(self.btnUpdateTable)

        self.subLblRITable = QLabel(self.frame_67)
        self.subLblRITable.setObjectName(u"subLblRITable")
        self.subLblRITable.setMinimumSize(QSize(100, 55))
        self.subLblRITable.setMaximumSize(QSize(200, 60))
        self.subLblRITable.setStyleSheet(u"QLabel{\n"
"	background-color:#e3dac9;\n"
"}")
        self.subLblRITable.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_22.addWidget(self.subLblRITable)

        self.subLblConcTable = QLabel(self.frame_67)
        self.subLblConcTable.setObjectName(u"subLblConcTable")
        self.subLblConcTable.setMinimumSize(QSize(100, 55))
        self.subLblConcTable.setMaximumSize(QSize(200, 60))
        self.subLblConcTable.setStyleSheet(u"QLabel{\n"
"	background-color:#e3dac9;\n"
"}")
        self.subLblConcTable.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_22.addWidget(self.subLblConcTable)


        self.horizontalLayout_44.addWidget(self.frame_67)

        self.frame_68 = QFrame(self.frame_69)
        self.frame_68.setObjectName(u"frame_68")
        self.frame_68.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_68.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_26 = QVBoxLayout(self.frame_68)
        self.verticalLayout_26.setObjectName(u"verticalLayout_26")
        self.subLblPropBefTable = QLabel(self.frame_68)
        self.subLblPropBefTable.setObjectName(u"subLblPropBefTable")
        self.subLblPropBefTable.setMinimumSize(QSize(200, 55))
        self.subLblPropBefTable.setMaximumSize(QSize(200, 60))
        self.subLblPropBefTable.setStyleSheet(u"QLabel{\n"
"	background-color:#e3dac9;\n"
"}")
        self.subLblPropBefTable.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_26.addWidget(self.subLblPropBefTable)

        self.subLblPropBefRITable = QLabel(self.frame_68)
        self.subLblPropBefRITable.setObjectName(u"subLblPropBefRITable")
        self.subLblPropBefRITable.setMinimumSize(QSize(100, 55))
        self.subLblPropBefRITable.setMaximumSize(QSize(200, 60))
        self.subLblPropBefRITable.setStyleSheet(u"")
        self.subLblPropBefRITable.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_26.addWidget(self.subLblPropBefRITable)

        self.subLblPropBefConcTable = QLabel(self.frame_68)
        self.subLblPropBefConcTable.setObjectName(u"subLblPropBefConcTable")
        self.subLblPropBefConcTable.setMinimumSize(QSize(100, 55))
        self.subLblPropBefConcTable.setMaximumSize(QSize(200, 60))
        self.subLblPropBefConcTable.setStyleSheet(u"")
        self.subLblPropBefConcTable.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_26.addWidget(self.subLblPropBefConcTable)


        self.horizontalLayout_44.addWidget(self.frame_68)

        self.frame_66 = QFrame(self.frame_69)
        self.frame_66.setObjectName(u"frame_66")
        self.frame_66.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_66.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_20 = QVBoxLayout(self.frame_66)
        self.verticalLayout_20.setObjectName(u"verticalLayout_20")
        self.subLblPropAftTable = QLabel(self.frame_66)
        self.subLblPropAftTable.setObjectName(u"subLblPropAftTable")
        self.subLblPropAftTable.setMinimumSize(QSize(200, 55))
        self.subLblPropAftTable.setMaximumSize(QSize(200, 60))
        self.subLblPropAftTable.setStyleSheet(u"QLabel{\n"
"	background-color:#e3dac9;\n"
"}")
        self.subLblPropAftTable.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_20.addWidget(self.subLblPropAftTable)

        self.subLblPropAftRITable = QLabel(self.frame_66)
        self.subLblPropAftRITable.setObjectName(u"subLblPropAftRITable")
        self.subLblPropAftRITable.setMinimumSize(QSize(100, 55))
        self.subLblPropAftRITable.setMaximumSize(QSize(200, 60))
        self.subLblPropAftRITable.setStyleSheet(u"")
        self.subLblPropAftRITable.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_20.addWidget(self.subLblPropAftRITable)

        self.subLblPropAftConcTable = QLabel(self.frame_66)
        self.subLblPropAftConcTable.setObjectName(u"subLblPropAftConcTable")
        self.subLblPropAftConcTable.setMinimumSize(QSize(100, 55))
        self.subLblPropAftConcTable.setMaximumSize(QSize(200, 60))
        self.subLblPropAftConcTable.setStyleSheet(u"")
        self.subLblPropAftConcTable.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_20.addWidget(self.subLblPropAftConcTable)


        self.horizontalLayout_44.addWidget(self.frame_66)


        self.horizontalLayout_42.addWidget(self.frame_69)

        self.horizontalSpacer_68 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_42.addItem(self.horizontalSpacer_68)


        self.gridLayout_14.addWidget(self.frame_49, 1, 0, 1, 1)

        self.frame_27 = QFrame(self.scrollAreaWidgetContents_6)
        self.frame_27.setObjectName(u"frame_27")
        self.frame_27.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_27.setFrameShadow(QFrame.Shadow.Raised)
        self.gridLayout_20 = QGridLayout(self.frame_27)
        self.gridLayout_20.setObjectName(u"gridLayout_20")
        self.frame_53 = QFrame(self.frame_27)
        self.frame_53.setObjectName(u"frame_53")
        self.frame_53.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_53.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_77 = QVBoxLayout(self.frame_53)
        self.verticalLayout_77.setSpacing(20)
        self.verticalLayout_77.setObjectName(u"verticalLayout_77")
        self.subLnEdtPropDet_2 = QLineEdit(self.frame_53)
        self.subLnEdtPropDet_2.setObjectName(u"subLnEdtPropDet_2")
        self.subLnEdtPropDet_2.setMinimumSize(QSize(450, 55))
        self.subLnEdtPropDet_2.setMaximumSize(QSize(16777215, 60))
        self.subLnEdtPropDet_2.setStyleSheet(u"")

        self.verticalLayout_77.addWidget(self.subLnEdtPropDet_2)

        self.subLnEdtPropColBef_2 = QLineEdit(self.frame_53)
        self.subLnEdtPropColBef_2.setObjectName(u"subLnEdtPropColBef_2")
        self.subLnEdtPropColBef_2.setMinimumSize(QSize(450, 55))
        self.subLnEdtPropColBef_2.setMaximumSize(QSize(16777215, 60))
        self.subLnEdtPropColBef_2.setStyleSheet(u"")

        self.verticalLayout_77.addWidget(self.subLnEdtPropColBef_2)

        self.subLnEdtPropColAft_2 = QLineEdit(self.frame_53)
        self.subLnEdtPropColAft_2.setObjectName(u"subLnEdtPropColAft_2")
        self.subLnEdtPropColAft_2.setMinimumSize(QSize(450, 55))
        self.subLnEdtPropColAft_2.setMaximumSize(QSize(16777215, 60))
        self.subLnEdtPropColAft_2.setStyleSheet(u"")

        self.verticalLayout_77.addWidget(self.subLnEdtPropColAft_2)

        self.subLnEdtPropWghtFild_2 = CustomDoubleSpinBox(self.frame_53)
        self.subLnEdtPropWghtFild_2.setObjectName(u"subLnEdtPropWghtFild_2")
        self.subLnEdtPropWghtFild_2.setMinimumSize(QSize(450, 55))
        self.subLnEdtPropWghtFild_2.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtPropWghtFild_2.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtPropWghtFild_2.setDecimals(6)
        self.subLnEdtPropWghtFild_2.setMaximum(10000000.000000000000000)
        self.subLnEdtPropWghtFild_2.setSingleStep(0.000000000000000)
        self.subLnEdtPropWghtFild_2.setStepType(QAbstractSpinBox.StepType.DefaultStepType)

        self.verticalLayout_77.addWidget(self.subLnEdtPropWghtFild_2)

        self.subLnEdtPropWghtRecvrd_2 = CustomDoubleSpinBox(self.frame_53)
        self.subLnEdtPropWghtRecvrd_2.setObjectName(u"subLnEdtPropWghtRecvrd_2")
        self.subLnEdtPropWghtRecvrd_2.setMinimumSize(QSize(450, 55))
        self.subLnEdtPropWghtRecvrd_2.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtPropWghtRecvrd_2.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtPropWghtRecvrd_2.setDecimals(6)
        self.subLnEdtPropWghtRecvrd_2.setMaximum(10000000.000000000000000)
        self.subLnEdtPropWghtRecvrd_2.setSingleStep(0.000000000000000)

        self.verticalLayout_77.addWidget(self.subLnEdtPropWghtRecvrd_2)

        self.subLnEdtPropUsedPerc_2 = CustomDoubleSpinBox(self.frame_53)
        self.subLnEdtPropUsedPerc_2.setObjectName(u"subLnEdtPropUsedPerc_2")
        self.subLnEdtPropUsedPerc_2.setMinimumSize(QSize(450, 55))
        self.subLnEdtPropUsedPerc_2.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtPropUsedPerc_2.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtPropUsedPerc_2.setMaximum(100.000000000000000)
        self.subLnEdtPropUsedPerc_2.setSingleStep(0.000000000000000)

        self.verticalLayout_77.addWidget(self.subLnEdtPropUsedPerc_2)

        self.subLnEdtPropRIBefFirg_2 = CustomDoubleSpinBox(self.frame_53)
        self.subLnEdtPropRIBefFirg_2.setObjectName(u"subLnEdtPropRIBefFirg_2")
        self.subLnEdtPropRIBefFirg_2.setMinimumSize(QSize(450, 55))
        self.subLnEdtPropRIBefFirg_2.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtPropRIBefFirg_2.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtPropRIBefFirg_2.setDecimals(6)
        self.subLnEdtPropRIBefFirg_2.setMaximum(10000000.000000000000000)
        self.subLnEdtPropRIBefFirg_2.setSingleStep(0.000000000000000)

        self.verticalLayout_77.addWidget(self.subLnEdtPropRIBefFirg_2)

        self.subLnEdtPropRIAftFirg_2 = CustomDoubleSpinBox(self.frame_53)
        self.subLnEdtPropRIAftFirg_2.setObjectName(u"subLnEdtPropRIAftFirg_2")
        self.subLnEdtPropRIAftFirg_2.setMinimumSize(QSize(450, 55))
        self.subLnEdtPropRIAftFirg_2.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtPropRIAftFirg_2.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtPropRIAftFirg_2.setDecimals(6)
        self.subLnEdtPropRIAftFirg_2.setMaximum(10000000.000000000000000)
        self.subLnEdtPropRIAftFirg_2.setSingleStep(0.000000000000000)

        self.verticalLayout_77.addWidget(self.subLnEdtPropRIAftFirg_2)

        self.subLnEdtFirgDur_2 = CustomDoubleSpinBox(self.frame_53)
        self.subLnEdtFirgDur_2.setObjectName(u"subLnEdtFirgDur_2")
        self.subLnEdtFirgDur_2.setMinimumSize(QSize(450, 55))
        self.subLnEdtFirgDur_2.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtFirgDur_2.setReadOnly(True)
        self.subLnEdtFirgDur_2.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtFirgDur_2.setDecimals(6)
        self.subLnEdtFirgDur_2.setMaximum(10000000.000000000000000)
        self.subLnEdtFirgDur_2.setSingleStep(0.000000000000000)

        self.verticalLayout_77.addWidget(self.subLnEdtFirgDur_2)

        self.subLnEdtApproxMassFlowRate_2 = CustomDoubleSpinBox(self.frame_53)
        self.subLnEdtApproxMassFlowRate_2.setObjectName(u"subLnEdtApproxMassFlowRate_2")
        self.subLnEdtApproxMassFlowRate_2.setMinimumSize(QSize(450, 55))
        self.subLnEdtApproxMassFlowRate_2.setMaximumSize(QSize(16777215, 55))
        self.subLnEdtApproxMassFlowRate_2.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.subLnEdtApproxMassFlowRate_2.setDecimals(6)
        self.subLnEdtApproxMassFlowRate_2.setMaximum(10000000.000000000000000)
        self.subLnEdtApproxMassFlowRate_2.setSingleStep(0.000000000000000)

        self.verticalLayout_77.addWidget(self.subLnEdtApproxMassFlowRate_2)


        self.gridLayout_20.addWidget(self.frame_53, 0, 2, 1, 1)

        self.frame_54 = QFrame(self.frame_27)
        self.frame_54.setObjectName(u"frame_54")
        self.frame_54.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_54.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_78 = QVBoxLayout(self.frame_54)
        self.verticalLayout_78.setSpacing(20)
        self.verticalLayout_78.setObjectName(u"verticalLayout_78")
        self.subLblPropDet_2 = QLabel(self.frame_54)
        self.subLblPropDet_2.setObjectName(u"subLblPropDet_2")
        self.subLblPropDet_2.setMinimumSize(QSize(450, 55))
        self.subLblPropDet_2.setMaximumSize(QSize(16777215, 60))
        self.subLblPropDet_2.setStyleSheet(u"")
        self.subLblPropDet_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_78.addWidget(self.subLblPropDet_2)

        self.subLblPropColBef_2 = QLabel(self.frame_54)
        self.subLblPropColBef_2.setObjectName(u"subLblPropColBef_2")
        self.subLblPropColBef_2.setMinimumSize(QSize(450, 55))
        self.subLblPropColBef_2.setMaximumSize(QSize(16777215, 60))
        self.subLblPropColBef_2.setStyleSheet(u"")
        self.subLblPropColBef_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_78.addWidget(self.subLblPropColBef_2)

        self.subLblPropColAft_2 = QLabel(self.frame_54)
        self.subLblPropColAft_2.setObjectName(u"subLblPropColAft_2")
        self.subLblPropColAft_2.setMinimumSize(QSize(450, 55))
        self.subLblPropColAft_2.setMaximumSize(QSize(16777215, 60))
        self.subLblPropColAft_2.setStyleSheet(u"")
        self.subLblPropColAft_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_78.addWidget(self.subLblPropColAft_2)

        self.subLblPropWghtFild_2 = QLabel(self.frame_54)
        self.subLblPropWghtFild_2.setObjectName(u"subLblPropWghtFild_2")
        self.subLblPropWghtFild_2.setMinimumSize(QSize(450, 55))
        self.subLblPropWghtFild_2.setMaximumSize(QSize(16777215, 60))
        self.subLblPropWghtFild_2.setStyleSheet(u"")
        self.subLblPropWghtFild_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_78.addWidget(self.subLblPropWghtFild_2)

        self.subLblPropWghtRecvrd_2 = QLabel(self.frame_54)
        self.subLblPropWghtRecvrd_2.setObjectName(u"subLblPropWghtRecvrd_2")
        self.subLblPropWghtRecvrd_2.setMinimumSize(QSize(450, 55))
        self.subLblPropWghtRecvrd_2.setMaximumSize(QSize(16777215, 60))
        self.subLblPropWghtRecvrd_2.setStyleSheet(u"")
        self.subLblPropWghtRecvrd_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_78.addWidget(self.subLblPropWghtRecvrd_2)

        self.subLblPropUsedPerc_2 = QLabel(self.frame_54)
        self.subLblPropUsedPerc_2.setObjectName(u"subLblPropUsedPerc_2")
        self.subLblPropUsedPerc_2.setMinimumSize(QSize(450, 55))
        self.subLblPropUsedPerc_2.setMaximumSize(QSize(16777215, 60))
        self.subLblPropUsedPerc_2.setStyleSheet(u"")
        self.subLblPropUsedPerc_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_78.addWidget(self.subLblPropUsedPerc_2)

        self.subLblPropRIBefFirg_2 = QLabel(self.frame_54)
        self.subLblPropRIBefFirg_2.setObjectName(u"subLblPropRIBefFirg_2")
        self.subLblPropRIBefFirg_2.setMinimumSize(QSize(450, 55))
        self.subLblPropRIBefFirg_2.setMaximumSize(QSize(16777215, 60))
        self.subLblPropRIBefFirg_2.setStyleSheet(u"")
        self.subLblPropRIBefFirg_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_78.addWidget(self.subLblPropRIBefFirg_2)

        self.subLblPropRIAftFirg_2 = QLabel(self.frame_54)
        self.subLblPropRIAftFirg_2.setObjectName(u"subLblPropRIAftFirg_2")
        self.subLblPropRIAftFirg_2.setMinimumSize(QSize(450, 55))
        self.subLblPropRIAftFirg_2.setMaximumSize(QSize(16777215, 60))
        self.subLblPropRIAftFirg_2.setStyleSheet(u"")
        self.subLblPropRIAftFirg_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_78.addWidget(self.subLblPropRIAftFirg_2)

        self.subLblFirgDur_2 = QLabel(self.frame_54)
        self.subLblFirgDur_2.setObjectName(u"subLblFirgDur_2")
        self.subLblFirgDur_2.setMinimumSize(QSize(450, 55))
        self.subLblFirgDur_2.setMaximumSize(QSize(16777215, 60))
        self.subLblFirgDur_2.setStyleSheet(u"")
        self.subLblFirgDur_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_78.addWidget(self.subLblFirgDur_2)

        self.subLblApproxMassFlowRate_2 = QLabel(self.frame_54)
        self.subLblApproxMassFlowRate_2.setObjectName(u"subLblApproxMassFlowRate_2")
        self.subLblApproxMassFlowRate_2.setMinimumSize(QSize(450, 55))
        self.subLblApproxMassFlowRate_2.setMaximumSize(QSize(16777215, 60))
        self.subLblApproxMassFlowRate_2.setStyleSheet(u"")
        self.subLblApproxMassFlowRate_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_78.addWidget(self.subLblApproxMassFlowRate_2)


        self.gridLayout_20.addWidget(self.frame_54, 0, 1, 1, 1)

        self.horizontalSpacer_32 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_20.addItem(self.horizontalSpacer_32, 0, 0, 1, 1)

        self.horizontalSpacer_33 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_20.addItem(self.horizontalSpacer_33, 0, 3, 1, 1)


        self.gridLayout_14.addWidget(self.frame_27, 0, 0, 1, 1)

        self.scrollArea_2.setWidget(self.scrollAreaWidgetContents_6)

        self.verticalLayout_65.addWidget(self.scrollArea_2)


        self.gridLayout_9.addWidget(self.contentFramePropPstAn, 0, 0, 1, 1)

        self.contentStack.addWidget(self.propellantPostAn)
        self.plots = QWidget()
        self.plots.setObjectName(u"plots")
        self.gridLayout_32 = QGridLayout(self.plots)
        self.gridLayout_32.setObjectName(u"gridLayout_32")
        self.gridLayout_32.setContentsMargins(0, 0, 0, 0)
        self.contentFramePlots = QFrame(self.plots)
        self.contentFramePlots.setObjectName(u"contentFramePlots")
        sizePolicy2 = QSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Minimum)
        sizePolicy2.setHorizontalStretch(0)
        sizePolicy2.setVerticalStretch(0)
        sizePolicy2.setHeightForWidth(self.contentFramePlots.sizePolicy().hasHeightForWidth())
        self.contentFramePlots.setSizePolicy(sizePolicy2)
        self.contentFramePlots.setMinimumSize(QSize(0, 0))
        self.contentFramePlots.setMaximumSize(QSize(16777215, 16777215))
        self.contentFramePlots.setStyleSheet(u"")
        self.contentFramePlots.setFrameShape(QFrame.Shape.NoFrame)
        self.contentFramePlots.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_21 = QVBoxLayout(self.contentFramePlots)
        self.verticalLayout_21.setObjectName(u"verticalLayout_21")
        self.verticalLayout_21.setContentsMargins(5, 0, 5, 5)
        self.plotTopBar = QFrame(self.contentFramePlots)
        self.plotTopBar.setObjectName(u"plotTopBar")
        self.plotTopBar.setMinimumSize(QSize(0, 40))
        self.plotTopBar.setMaximumSize(QSize(16777215, 40))
        self.plotTopBar.setFrameShape(QFrame.Shape.StyledPanel)
        self.plotTopBar.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_17 = QHBoxLayout(self.plotTopBar)
        self.horizontalLayout_17.setSpacing(0)
        self.horizontalLayout_17.setObjectName(u"horizontalLayout_17")
        self.horizontalLayout_17.setContentsMargins(0, 0, 0, 0)
        self.subSecPlot = QLabel(self.plotTopBar)
        self.subSecPlot.setObjectName(u"subSecPlot")
        self.subSecPlot.setMinimumSize(QSize(180, 40))
        self.subSecPlot.setMaximumSize(QSize(16777215, 16777215))
        self.subSecPlot.setFont(font3)
        self.subSecPlot.setStyleSheet(u"background-color: #252525;\n"
"border-radius: 15px;\n"
"padding:5px;\n"
"border: 2px solid red;\n"
"")
        self.subSecPlot.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_17.addWidget(self.subSecPlot)

        self.horizontalSpacer_41 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_17.addItem(self.horizontalSpacer_41)

        self.lnEditPlotTitle = QLineEdit(self.plotTopBar)
        self.lnEditPlotTitle.setObjectName(u"lnEditPlotTitle")
        self.lnEditPlotTitle.setMinimumSize(QSize(400, 40))
        self.lnEditPlotTitle.setMaximumSize(QSize(16777215, 16777215))
        self.lnEditPlotTitle.setStyleSheet(u"background-color: #2c3b52;\n"
"color: #94a3b8;\n"
"padding-left:20px;\n"
"font-size:16px;\n"
"\n"
"border-radius:14px;\n"
"font-family:Arial;")
        self.lnEditPlotTitle.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_17.addWidget(self.lnEditPlotTitle)

        self.horizontalSpacer_56 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_17.addItem(self.horizontalSpacer_56)

        self.btnIncludeInReport = QPushButton(self.plotTopBar)
        self.btnIncludeInReport.setObjectName(u"btnIncludeInReport")
        self.btnIncludeInReport.setMinimumSize(QSize(0, 40))
        self.btnIncludeInReport.setStyleSheet(u"QPushButton{\n"
"	\n"
"	background-color: #ecbd3c;\n"
"	border-radius:14px;\n"
"	padding:5px 10px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"	color:black;\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	\n"
"	background-color: qradialgradient(spread:pad, cx:0.474, cy:0.477, radius:2, fx:0.48, fy:0.479136, stop:0 rgba(255, 166, 153, 255), stop:1 rgba(255, 255, 255, 255));\n"
"	border:2px solid #18181b;\n"
"}\n"
"\n"
"QPushButton:pressed{\n"
"	\n"
"	background-color: qradialgradient(spread:pad, cx:0.474, cy:0.477, radius:2, fx:0.48, fy:0.479136, stop:0 rgba(181, 255, 153, 255), stop:1 rgba(255, 255, 255, 255));\n"
"}")

        self.horizontalLayout_17.addWidget(self.btnIncludeInReport)


        self.verticalLayout_21.addWidget(self.plotTopBar)

        self.plotFrame = QFrame(self.contentFramePlots)
        self.plotFrame.setObjectName(u"plotFrame")
        sizePolicy2.setHeightForWidth(self.plotFrame.sizePolicy().hasHeightForWidth())
        self.plotFrame.setSizePolicy(sizePolicy2)
        self.plotFrame.setMinimumSize(QSize(0, 0))
        self.plotFrame.setMaximumSize(QSize(16777215, 16777215))
        self.plotFrame.setStyleSheet(u"QFrame#plotFrame{\n"
"background-color: #fcf1ff;\n"
"border-radius: 10px;\n"
"}")
        self.plotFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.plotFrame.setFrameShadow(QFrame.Shadow.Raised)

        self.verticalLayout_21.addWidget(self.plotFrame)


        self.gridLayout_32.addWidget(self.contentFramePlots, 0, 0, 1, 1)

        self.contentStack.addWidget(self.plots)
        self.tableView = QWidget()
        self.tableView.setObjectName(u"tableView")
        self.gridLayout_46 = QGridLayout(self.tableView)
        self.gridLayout_46.setObjectName(u"gridLayout_46")
        self.gridLayout_46.setContentsMargins(0, 0, 0, 0)
        self.contentFrameCatPstAn_3 = QFrame(self.tableView)
        self.contentFrameCatPstAn_3.setObjectName(u"contentFrameCatPstAn_3")
        self.contentFrameCatPstAn_3.setMinimumSize(QSize(1000, 525))
        self.contentFrameCatPstAn_3.setMaximumSize(QSize(1200, 525))
        self.contentFrameCatPstAn_3.setStyleSheet(u"")
        self.contentFrameCatPstAn_3.setFrameShape(QFrame.Shape.NoFrame)
        self.contentFrameCatPstAn_3.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_37 = QVBoxLayout(self.contentFrameCatPstAn_3)
        self.verticalLayout_37.setObjectName(u"verticalLayout_37")
        self.verticalLayout_37.setContentsMargins(5, 5, 5, 5)
        self.frame_31 = QFrame(self.contentFrameCatPstAn_3)
        self.frame_31.setObjectName(u"frame_31")
        self.frame_31.setMinimumSize(QSize(0, 50))
        self.frame_31.setMaximumSize(QSize(16777215, 50))
        self.frame_31.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_31.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_27 = QHBoxLayout(self.frame_31)
        self.horizontalLayout_27.setSpacing(0)
        self.horizontalLayout_27.setObjectName(u"horizontalLayout_27")
        self.horizontalLayout_27.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_62 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_27.addItem(self.horizontalSpacer_62)

        self.subSecPerformance_2 = QLabel(self.frame_31)
        self.subSecPerformance_2.setObjectName(u"subSecPerformance_2")
        self.subSecPerformance_2.setMinimumSize(QSize(200, 45))
        self.subSecPerformance_2.setMaximumSize(QSize(16777215, 16777215))
        self.subSecPerformance_2.setFont(font3)
        self.subSecPerformance_2.setStyleSheet(u"background-color: #252525;\n"
"border-radius: 17px;\n"
"padding:5px;\n"
"border: 2px solid red;\n"
"\n"
"")
        self.subSecPerformance_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_27.addWidget(self.subSecPerformance_2)

        self.horizontalSpacer_63 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_27.addItem(self.horizontalSpacer_63)


        self.verticalLayout_37.addWidget(self.frame_31)

        self.tableTemperatureAnalysis = QTableWidget(self.contentFrameCatPstAn_3)
        self.tableTemperatureAnalysis.setObjectName(u"tableTemperatureAnalysis")
        self.tableTemperatureAnalysis.setStyleSheet(u"QTableWidget {\n"
"    background-color: #1e1e1e;\n"
"    border: 1px solid purple;\n"
"    border-radius: 8px;\n"
"    gridline-color: #f0f0f0;\n"
"}\n"
"\n"
"/* Header styling */\n"
"QHeaderView::section {\n"
"    background-color: #f5f5f5;\n"
"    padding: 2px;\n"
"    border: none;\n"
"    border-right: 1px solid #e0e0e0;\n"
"    border-bottom: 1px solid #e0e0e0;\n"
"    font-weight: bold;\n"
"    color: #424242;\n"
"	min-height: 27px;\n"
"	max-width: 50px;\n"
"}\n"
"\n"
"/* Horizontal scrollbar */\n"
"QScrollBar:horizontal {\n"
"    border: none;\n"
"    height: 12px;\n"
"    margin: 0px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 4px;\n"
"    min-width: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:horizontal,\n"
"QScrollBar::sub-line:horizontal {\n"
"    width: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:horizontal,\n"
"QScrollBar::sub-page:h"
                        "orizontal {\n"
"    background: none;\n"
"}\n"
"\n"
"/* Alternate row colors */\n"
"QTableWidget::item:alternate {\n"
"    background-color: #fafafa;\n"
"}\n"
"\n"
"QHeaderView::section:hover {\n"
"    background-color: #eeeeee;\n"
"}\n"
"\n"
"QHeaderView::section:checked {\n"
"    background-color: #e3f2fd;\n"
"}\n"
"\n"
"/* Row styling */\n"
"QTableWidget::item {\n"
"	color: white;\n"
"}\n"
"\n"
"QTableWidget::item:selected {\n"
"    background-color: #e3f2fd;\n"
"    color: #000000;\n"
"}\n"
"\n"
"QTableWidget::item:hover {\n"
"    background-color: rgba(251, 251, 251, 0.3)\n"
"}")
        self.tableTemperatureAnalysis.setDragEnabled(False)
        self.tableTemperatureAnalysis.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)

        self.verticalLayout_37.addWidget(self.tableTemperatureAnalysis)

        self.frame_70 = QFrame(self.contentFrameCatPstAn_3)
        self.frame_70.setObjectName(u"frame_70")
        self.frame_70.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_70.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_52 = QHBoxLayout(self.frame_70)
        self.horizontalLayout_52.setObjectName(u"horizontalLayout_52")
        self.btnBckToPlot = QPushButton(self.frame_70)
        self.btnBckToPlot.setObjectName(u"btnBckToPlot")
        self.btnBckToPlot.setMinimumSize(QSize(0, 40))
        self.btnBckToPlot.setMaximumSize(QSize(300, 16777215))
        self.btnBckToPlot.setStyleSheet(u"QPushButton{\n"
"		\n"
"	background-color: #e60e70;\n"
"	font-family: Arial;\n"
"	font-size: 15px;\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	background-color:#47a08e;\n"
"}\n"
"\n"
"\n"
"QPushButton:pressed{\n"
"	background-color:#e60e70;\n"
"}")

        self.horizontalLayout_52.addWidget(self.btnBckToPlot)

        self.btnMaxTempsPlot = QPushButton(self.frame_70)
        self.btnMaxTempsPlot.setObjectName(u"btnMaxTempsPlot")
        self.btnMaxTempsPlot.setMinimumSize(QSize(0, 40))
        self.btnMaxTempsPlot.setMaximumSize(QSize(300, 16777215))
        self.btnMaxTempsPlot.setStyleSheet(u"QPushButton{\n"
"		\n"
"	background-color: #47a08e;\n"
"	font-family: Arial;\n"
"	font-size: 15px;\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	background-color:#47a08e;\n"
"}\n"
"\n"
"\n"
"QPushButton:pressed{\n"
"	background-color:#e60e70;\n"
"}")

        self.horizontalLayout_52.addWidget(self.btnMaxTempsPlot)


        self.verticalLayout_37.addWidget(self.frame_70)


        self.gridLayout_46.addWidget(self.contentFrameCatPstAn_3, 0, 0, 1, 1)

        self.contentStack.addWidget(self.tableView)
        self.performance = QWidget()
        self.performance.setObjectName(u"performance")
        self.gridLayout_43 = QGridLayout(self.performance)
        self.gridLayout_43.setObjectName(u"gridLayout_43")
        self.gridLayout_43.setContentsMargins(0, 0, 0, 0)
        self.contentFrameCatPstAn_2 = QFrame(self.performance)
        self.contentFrameCatPstAn_2.setObjectName(u"contentFrameCatPstAn_2")
        self.contentFrameCatPstAn_2.setMinimumSize(QSize(0, 0))
        self.contentFrameCatPstAn_2.setMaximumSize(QSize(16777215, 16777215))
        self.contentFrameCatPstAn_2.setStyleSheet(u"")
        self.contentFrameCatPstAn_2.setFrameShape(QFrame.Shape.NoFrame)
        self.contentFrameCatPstAn_2.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_36 = QVBoxLayout(self.contentFrameCatPstAn_2)
        self.verticalLayout_36.setObjectName(u"verticalLayout_36")
        self.verticalLayout_36.setContentsMargins(5, 5, 5, -1)
        self.frame_28 = QFrame(self.contentFrameCatPstAn_2)
        self.frame_28.setObjectName(u"frame_28")
        self.frame_28.setMinimumSize(QSize(0, 50))
        self.frame_28.setMaximumSize(QSize(16777215, 50))
        self.frame_28.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_28.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_21 = QHBoxLayout(self.frame_28)
        self.horizontalLayout_21.setSpacing(0)
        self.horizontalLayout_21.setObjectName(u"horizontalLayout_21")
        self.horizontalLayout_21.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_57 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_21.addItem(self.horizontalSpacer_57)

        self.subSecPerformance = QLabel(self.frame_28)
        self.subSecPerformance.setObjectName(u"subSecPerformance")
        self.subSecPerformance.setMinimumSize(QSize(180, 45))
        self.subSecPerformance.setMaximumSize(QSize(16777215, 16777215))
        self.subSecPerformance.setFont(font3)
        self.subSecPerformance.setStyleSheet(u"background-color: #252525;\n"
"border-radius: 17px;\n"
"padding:5px;\n"
"border: 2px solid red;\n"
"")
        self.subSecPerformance.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_21.addWidget(self.subSecPerformance)

        self.horizontalSpacer_58 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_21.addItem(self.horizontalSpacer_58)


        self.verticalLayout_36.addWidget(self.frame_28)

        self.scrollArea_3 = QScrollArea(self.contentFrameCatPstAn_2)
        self.scrollArea_3.setObjectName(u"scrollArea_3")
        self.scrollArea_3.setStyleSheet(u"QLabel{\n"
"	background-color: #333333;\n"
"	border-radius:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QLineEdit{\n"
"	background-color: rgb(84, 103, 163);\n"
"	border:1px solid #9CAECA;\n"
"	border-radius: 5px;\n"
"	padding:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"/* Scrollbar styling */\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    width: 12px;\n"
"    margin: 0px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 4px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}")
        self.scrollArea_3.setWidgetResizable(True)
        self.scrollAreaWidgetContents_7 = QWidget()
        self.scrollAreaWidgetContents_7.setObjectName(u"scrollAreaWidgetContents_7")
        self.scrollAreaWidgetContents_7.setGeometry(QRect(0, 0, 992, 776))
        self.gridLayout_15 = QGridLayout(self.scrollAreaWidgetContents_7)
        self.gridLayout_15.setObjectName(u"gridLayout_15")
        self.horizontalSpacer_59 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_15.addItem(self.horizontalSpacer_59, 0, 0, 1, 1)

        self.frame_61 = QFrame(self.scrollAreaWidgetContents_7)
        self.frame_61.setObjectName(u"frame_61")
        self.frame_61.setMinimumSize(QSize(0, 0))
        self.frame_61.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_61.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_75 = QVBoxLayout(self.frame_61)
        self.verticalLayout_75.setSpacing(20)
        self.verticalLayout_75.setObjectName(u"verticalLayout_75")
        self.subLnEdtChambPressure = QLineEdit(self.frame_61)
        self.subLnEdtChambPressure.setObjectName(u"subLnEdtChambPressure")
        self.subLnEdtChambPressure.setMinimumSize(QSize(450, 55))
        self.subLnEdtChambPressure.setMaximumSize(QSize(16777215, 60))
        self.subLnEdtChambPressure.setStyleSheet(u"")
        self.subLnEdtChambPressure.setReadOnly(True)

        self.verticalLayout_75.addWidget(self.subLnEdtChambPressure)

        self.subLnEdtVacPressure = QLineEdit(self.frame_61)
        self.subLnEdtVacPressure.setObjectName(u"subLnEdtVacPressure")
        self.subLnEdtVacPressure.setMinimumSize(QSize(450, 55))
        self.subLnEdtVacPressure.setMaximumSize(QSize(16777215, 60))
        self.subLnEdtVacPressure.setStyleSheet(u"")
        self.subLnEdtVacPressure.setReadOnly(True)

        self.verticalLayout_75.addWidget(self.subLnEdtVacPressure)

        self.subLnEdtChambTemp = QLineEdit(self.frame_61)
        self.subLnEdtChambTemp.setObjectName(u"subLnEdtChambTemp")
        self.subLnEdtChambTemp.setMinimumSize(QSize(450, 55))
        self.subLnEdtChambTemp.setMaximumSize(QSize(16777215, 60))
        self.subLnEdtChambTemp.setStyleSheet(u"")
        self.subLnEdtChambTemp.setReadOnly(True)

        self.verticalLayout_75.addWidget(self.subLnEdtChambTemp)

        self.subLnEdtCharVelo = QLineEdit(self.frame_61)
        self.subLnEdtCharVelo.setObjectName(u"subLnEdtCharVelo")
        self.subLnEdtCharVelo.setMinimumSize(QSize(450, 55))
        self.subLnEdtCharVelo.setMaximumSize(QSize(16777215, 60))
        self.subLnEdtCharVelo.setStyleSheet(u"")
        self.subLnEdtCharVelo.setReadOnly(True)

        self.verticalLayout_75.addWidget(self.subLnEdtCharVelo)

        self.subLnEdtCoefOfThrust = QLineEdit(self.frame_61)
        self.subLnEdtCoefOfThrust.setObjectName(u"subLnEdtCoefOfThrust")
        self.subLnEdtCoefOfThrust.setMinimumSize(QSize(450, 55))
        self.subLnEdtCoefOfThrust.setMaximumSize(QSize(16777215, 60))
        self.subLnEdtCoefOfThrust.setStyleSheet(u"")
        self.subLnEdtCoefOfThrust.setReadOnly(True)

        self.verticalLayout_75.addWidget(self.subLnEdtCoefOfThrust)

        self.subLnEdtBurnTime = QLineEdit(self.frame_61)
        self.subLnEdtBurnTime.setObjectName(u"subLnEdtBurnTime")
        self.subLnEdtBurnTime.setMinimumSize(QSize(450, 55))
        self.subLnEdtBurnTime.setMaximumSize(QSize(16777215, 60))
        self.subLnEdtBurnTime.setStyleSheet(u"")
        self.subLnEdtBurnTime.setReadOnly(True)

        self.verticalLayout_75.addWidget(self.subLnEdtBurnTime)

        self.subLnEdtMassFlowRate = QLineEdit(self.frame_61)
        self.subLnEdtMassFlowRate.setObjectName(u"subLnEdtMassFlowRate")
        self.subLnEdtMassFlowRate.setMinimumSize(QSize(450, 55))
        self.subLnEdtMassFlowRate.setMaximumSize(QSize(16777215, 60))
        self.subLnEdtMassFlowRate.setStyleSheet(u"")
        self.subLnEdtMassFlowRate.setReadOnly(True)

        self.verticalLayout_75.addWidget(self.subLnEdtMassFlowRate)

        self.subLnEdtThrust = QLineEdit(self.frame_61)
        self.subLnEdtThrust.setObjectName(u"subLnEdtThrust")
        self.subLnEdtThrust.setMinimumSize(QSize(450, 55))
        self.subLnEdtThrust.setMaximumSize(QSize(16777215, 60))
        self.subLnEdtThrust.setStyleSheet(u"")
        self.subLnEdtThrust.setReadOnly(True)

        self.verticalLayout_75.addWidget(self.subLnEdtThrust)

        self.subLnEdtSpcImpulse = QLineEdit(self.frame_61)
        self.subLnEdtSpcImpulse.setObjectName(u"subLnEdtSpcImpulse")
        self.subLnEdtSpcImpulse.setMinimumSize(QSize(450, 55))
        self.subLnEdtSpcImpulse.setMaximumSize(QSize(16777215, 60))
        self.subLnEdtSpcImpulse.setStyleSheet(u"")
        self.subLnEdtSpcImpulse.setReadOnly(True)

        self.verticalLayout_75.addWidget(self.subLnEdtSpcImpulse)

        self.subLnEdtTotImpulse = QLineEdit(self.frame_61)
        self.subLnEdtTotImpulse.setObjectName(u"subLnEdtTotImpulse")
        self.subLnEdtTotImpulse.setMinimumSize(QSize(450, 55))
        self.subLnEdtTotImpulse.setMaximumSize(QSize(16777215, 60))
        self.subLnEdtTotImpulse.setStyleSheet(u"")
        self.subLnEdtTotImpulse.setReadOnly(True)

        self.verticalLayout_75.addWidget(self.subLnEdtTotImpulse)


        self.gridLayout_15.addWidget(self.frame_61, 0, 3, 1, 1)

        self.frame_60 = QFrame(self.scrollAreaWidgetContents_7)
        self.frame_60.setObjectName(u"frame_60")
        self.frame_60.setMinimumSize(QSize(0, 0))
        self.frame_60.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_60.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_74 = QVBoxLayout(self.frame_60)
        self.verticalLayout_74.setSpacing(20)
        self.verticalLayout_74.setObjectName(u"verticalLayout_74")
        self.subLblChambPressure = QLabel(self.frame_60)
        self.subLblChambPressure.setObjectName(u"subLblChambPressure")
        self.subLblChambPressure.setMinimumSize(QSize(450, 55))
        self.subLblChambPressure.setMaximumSize(QSize(16777215, 60))
        font6 = QFont()
        font6.setFamilies([u"Arial"])
        self.subLblChambPressure.setFont(font6)
        self.subLblChambPressure.setStyleSheet(u"")
        self.subLblChambPressure.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_74.addWidget(self.subLblChambPressure)

        self.subLblVacPressure = QLabel(self.frame_60)
        self.subLblVacPressure.setObjectName(u"subLblVacPressure")
        self.subLblVacPressure.setMinimumSize(QSize(450, 55))
        self.subLblVacPressure.setMaximumSize(QSize(16777215, 60))
        self.subLblVacPressure.setStyleSheet(u"")
        self.subLblVacPressure.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_74.addWidget(self.subLblVacPressure)

        self.subLblChambTemp = QLabel(self.frame_60)
        self.subLblChambTemp.setObjectName(u"subLblChambTemp")
        self.subLblChambTemp.setMinimumSize(QSize(450, 55))
        self.subLblChambTemp.setMaximumSize(QSize(16777215, 60))
        self.subLblChambTemp.setStyleSheet(u"")
        self.subLblChambTemp.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_74.addWidget(self.subLblChambTemp)

        self.subLblCharVelo = QLabel(self.frame_60)
        self.subLblCharVelo.setObjectName(u"subLblCharVelo")
        self.subLblCharVelo.setMinimumSize(QSize(450, 55))
        self.subLblCharVelo.setMaximumSize(QSize(16777215, 60))
        self.subLblCharVelo.setStyleSheet(u"")
        self.subLblCharVelo.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_74.addWidget(self.subLblCharVelo)

        self.subLblCoefOfThrust = QLabel(self.frame_60)
        self.subLblCoefOfThrust.setObjectName(u"subLblCoefOfThrust")
        self.subLblCoefOfThrust.setMinimumSize(QSize(450, 55))
        self.subLblCoefOfThrust.setMaximumSize(QSize(16777215, 60))
        self.subLblCoefOfThrust.setStyleSheet(u"")
        self.subLblCoefOfThrust.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_74.addWidget(self.subLblCoefOfThrust)

        self.subLblBurnTime = QLabel(self.frame_60)
        self.subLblBurnTime.setObjectName(u"subLblBurnTime")
        self.subLblBurnTime.setMinimumSize(QSize(450, 55))
        self.subLblBurnTime.setMaximumSize(QSize(16777215, 60))
        self.subLblBurnTime.setStyleSheet(u"")
        self.subLblBurnTime.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_74.addWidget(self.subLblBurnTime)

        self.subLblMassFlowRate = QLabel(self.frame_60)
        self.subLblMassFlowRate.setObjectName(u"subLblMassFlowRate")
        self.subLblMassFlowRate.setMinimumSize(QSize(450, 55))
        self.subLblMassFlowRate.setMaximumSize(QSize(16777215, 60))
        self.subLblMassFlowRate.setStyleSheet(u"")
        self.subLblMassFlowRate.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_74.addWidget(self.subLblMassFlowRate)

        self.subLblThrust = QLabel(self.frame_60)
        self.subLblThrust.setObjectName(u"subLblThrust")
        self.subLblThrust.setMinimumSize(QSize(450, 55))
        self.subLblThrust.setMaximumSize(QSize(16777215, 60))
        self.subLblThrust.setStyleSheet(u"")
        self.subLblThrust.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_74.addWidget(self.subLblThrust)

        self.subLblSpcImpulse = QLabel(self.frame_60)
        self.subLblSpcImpulse.setObjectName(u"subLblSpcImpulse")
        self.subLblSpcImpulse.setMinimumSize(QSize(450, 55))
        self.subLblSpcImpulse.setMaximumSize(QSize(16777215, 60))
        self.subLblSpcImpulse.setStyleSheet(u"")
        self.subLblSpcImpulse.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_74.addWidget(self.subLblSpcImpulse)

        self.subLblTotImpulse = QLabel(self.frame_60)
        self.subLblTotImpulse.setObjectName(u"subLblTotImpulse")
        self.subLblTotImpulse.setMinimumSize(QSize(450, 55))
        self.subLblTotImpulse.setMaximumSize(QSize(16777215, 60))
        self.subLblTotImpulse.setStyleSheet(u"")
        self.subLblTotImpulse.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_74.addWidget(self.subLblTotImpulse)


        self.gridLayout_15.addWidget(self.frame_60, 0, 2, 1, 1)

        self.horizontalSpacer_60 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_15.addItem(self.horizontalSpacer_60, 0, 4, 1, 1)

        self.scrollArea_3.setWidget(self.scrollAreaWidgetContents_7)

        self.verticalLayout_36.addWidget(self.scrollArea_3)


        self.gridLayout_43.addWidget(self.contentFrameCatPstAn_2, 0, 0, 1, 1)

        self.contentStack.addWidget(self.performance)
        self.testAuthorization = QWidget()
        self.testAuthorization.setObjectName(u"testAuthorization")
        self.gridLayout_37 = QGridLayout(self.testAuthorization)
        self.gridLayout_37.setObjectName(u"gridLayout_37")
        self.contentFrameTestAuth = QFrame(self.testAuthorization)
        self.contentFrameTestAuth.setObjectName(u"contentFrameTestAuth")
        self.contentFrameTestAuth.setMinimumSize(QSize(0, 0))
        self.contentFrameTestAuth.setMaximumSize(QSize(16777215, 16777215))
        self.contentFrameTestAuth.setStyleSheet(u"")
        self.contentFrameTestAuth.setFrameShape(QFrame.Shape.NoFrame)
        self.contentFrameTestAuth.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_73 = QVBoxLayout(self.contentFrameTestAuth)
        self.verticalLayout_73.setObjectName(u"verticalLayout_73")
        self.verticalLayout_73.setContentsMargins(5, 5, 5, -1)
        self.frame_47 = QFrame(self.contentFrameTestAuth)
        self.frame_47.setObjectName(u"frame_47")
        self.frame_47.setMinimumSize(QSize(0, 50))
        self.frame_47.setMaximumSize(QSize(16777215, 50))
        self.frame_47.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_47.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_32 = QHBoxLayout(self.frame_47)
        self.horizontalLayout_32.setSpacing(0)
        self.horizontalLayout_32.setObjectName(u"horizontalLayout_32")
        self.horizontalLayout_32.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_82 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_32.addItem(self.horizontalSpacer_82)

        self.subSecTestAuthorization = QLabel(self.frame_47)
        self.subSecTestAuthorization.setObjectName(u"subSecTestAuthorization")
        self.subSecTestAuthorization.setMinimumSize(QSize(200, 45))
        self.subSecTestAuthorization.setMaximumSize(QSize(16777215, 16777215))
        self.subSecTestAuthorization.setFont(font3)
        self.subSecTestAuthorization.setStyleSheet(u"background-color: #252525;\n"
"border-radius: 17px;\n"
"padding:5px;\n"
"border: 2px solid red;\n"
"")
        self.subSecTestAuthorization.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_32.addWidget(self.subSecTestAuthorization)

        self.horizontalSpacer_83 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_32.addItem(self.horizontalSpacer_83)


        self.verticalLayout_73.addWidget(self.frame_47)

        self.verticalSpacer = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_73.addItem(self.verticalSpacer)

        self.frame = QFrame(self.contentFrameTestAuth)
        self.frame.setObjectName(u"frame")
        self.frame.setStyleSheet(u"QLabel{\n"
"	background-color: #333333;\n"
"	border-radius:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QLineEdit{\n"
"	background-color: rgb(84, 103, 163);\n"
"	border:1px solid #9CAECA;\n"
"	border-radius: 5px;\n"
"	padding:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}")
        self.frame.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame.setFrameShadow(QFrame.Shadow.Raised)
        self.gridLayout_36 = QGridLayout(self.frame)
        self.gridLayout_36.setSpacing(6)
        self.gridLayout_36.setObjectName(u"gridLayout_36")
        self.frame_2 = QFrame(self.frame)
        self.frame_2.setObjectName(u"frame_2")
        self.frame_2.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_2.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_24 = QVBoxLayout(self.frame_2)
        self.verticalLayout_24.setSpacing(15)
        self.verticalLayout_24.setObjectName(u"verticalLayout_24")
        self.subLnEdtTestConductedBy = QLineEdit(self.frame_2)
        self.subLnEdtTestConductedBy.setObjectName(u"subLnEdtTestConductedBy")
        self.subLnEdtTestConductedBy.setMinimumSize(QSize(450, 55))
        self.subLnEdtTestConductedBy.setMaximumSize(QSize(16777215, 60))
        self.subLnEdtTestConductedBy.setStyleSheet(u"")
        self.subLnEdtTestConductedBy.setReadOnly(False)

        self.verticalLayout_24.addWidget(self.subLnEdtTestConductedBy)

        self.subLnEdtReportGeneratedBy = QLineEdit(self.frame_2)
        self.subLnEdtReportGeneratedBy.setObjectName(u"subLnEdtReportGeneratedBy")
        self.subLnEdtReportGeneratedBy.setMinimumSize(QSize(450, 55))
        self.subLnEdtReportGeneratedBy.setMaximumSize(QSize(16777215, 60))
        self.subLnEdtReportGeneratedBy.setStyleSheet(u"")
        self.subLnEdtReportGeneratedBy.setReadOnly(False)

        self.verticalLayout_24.addWidget(self.subLnEdtReportGeneratedBy)

        self.subLnEdtReportAuthorizedBy = QLineEdit(self.frame_2)
        self.subLnEdtReportAuthorizedBy.setObjectName(u"subLnEdtReportAuthorizedBy")
        self.subLnEdtReportAuthorizedBy.setMinimumSize(QSize(450, 55))
        self.subLnEdtReportAuthorizedBy.setMaximumSize(QSize(16777215, 60))
        self.subLnEdtReportAuthorizedBy.setStyleSheet(u"")
        self.subLnEdtReportAuthorizedBy.setReadOnly(False)

        self.verticalLayout_24.addWidget(self.subLnEdtReportAuthorizedBy)


        self.gridLayout_36.addWidget(self.frame_2, 0, 4, 1, 1)

        self.horizontalSpacer_44 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_36.addItem(self.horizontalSpacer_44, 0, 5, 1, 1)

        self.horizontalSpacer_36 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_36.addItem(self.horizontalSpacer_36, 0, 0, 1, 1)

        self.frame_20 = QFrame(self.frame)
        self.frame_20.setObjectName(u"frame_20")
        self.frame_20.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_20.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_60 = QVBoxLayout(self.frame_20)
        self.verticalLayout_60.setSpacing(15)
        self.verticalLayout_60.setObjectName(u"verticalLayout_60")
        self.verticalLayout_60.setContentsMargins(9, 9, 9, 9)
        self.subLblTestConductedBy = QLabel(self.frame_20)
        self.subLblTestConductedBy.setObjectName(u"subLblTestConductedBy")
        self.subLblTestConductedBy.setMinimumSize(QSize(450, 55))
        self.subLblTestConductedBy.setMaximumSize(QSize(16777215, 60))
        self.subLblTestConductedBy.setFont(font6)
        self.subLblTestConductedBy.setStyleSheet(u"")
        self.subLblTestConductedBy.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_60.addWidget(self.subLblTestConductedBy)

        self.subLblReportGeneratedBy = QLabel(self.frame_20)
        self.subLblReportGeneratedBy.setObjectName(u"subLblReportGeneratedBy")
        self.subLblReportGeneratedBy.setMinimumSize(QSize(450, 55))
        self.subLblReportGeneratedBy.setMaximumSize(QSize(16777215, 60))
        self.subLblReportGeneratedBy.setStyleSheet(u"")
        self.subLblReportGeneratedBy.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_60.addWidget(self.subLblReportGeneratedBy)

        self.subLblReportAuthorizedBy = QLabel(self.frame_20)
        self.subLblReportAuthorizedBy.setObjectName(u"subLblReportAuthorizedBy")
        self.subLblReportAuthorizedBy.setMinimumSize(QSize(450, 55))
        self.subLblReportAuthorizedBy.setMaximumSize(QSize(16777215, 60))
        self.subLblReportAuthorizedBy.setStyleSheet(u"")
        self.subLblReportAuthorizedBy.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_60.addWidget(self.subLblReportAuthorizedBy)


        self.gridLayout_36.addWidget(self.frame_20, 0, 1, 1, 1)


        self.verticalLayout_73.addWidget(self.frame)

        self.verticalSpacer_5 = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_73.addItem(self.verticalSpacer_5)


        self.gridLayout_37.addWidget(self.contentFrameTestAuth, 0, 0, 1, 1)

        self.contentStack.addWidget(self.testAuthorization)

        self.verticalLayout_3.addWidget(self.contentStack)

        self.frame_19 = QFrame(self.mainContentPanel)
        self.frame_19.setObjectName(u"frame_19")
        self.frame_19.setMinimumSize(QSize(0, 30))
        self.frame_19.setMaximumSize(QSize(16777215, 30))
        self.frame_19.setStyleSheet(u"QPushButton#backBtn{\n"
"	background-color: #374151;\n"
"	color: white;\n"
"	font-size:16px;\n"
"	border-top-left-radius: 15px;\n"
"    border-bottom-left-radius: 15px;\n"
"	border-top-right-radius: 5px;\n"
"    border-bottom-right-radius: 5px;\n"
"}\n"
"\n"
"QPushButton#nextBtn{\n"
"	background-color: #374151;\n"
"	color: white;\n"
"	font-size:16px;\n"
"    border-top-right-radius: 15px;\n"
"    border-bottom-right-radius: 15px;\n"
"	border-top-left-radius: 5px;\n"
"    border-bottom-left-radius: 5px;\n"
"}\n"
"\n"
"QPushButton:hover#nextBtn, QPushButton:hover#backBtn{\n"
"	background-color:#47a08e;\n"
"}\n"
"\n"
"QPushButton:pressed#nextBtn, QPushButton:pressed#backBtn{\n"
"	background-color:#5C5C5C;\n"
"}")
        self.frame_19.setFrameShape(QFrame.Shape.NoFrame)
        self.frame_19.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_25 = QHBoxLayout(self.frame_19)
        self.horizontalLayout_25.setSpacing(0)
        self.horizontalLayout_25.setObjectName(u"horizontalLayout_25")
        self.horizontalLayout_25.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_75 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_25.addItem(self.horizontalSpacer_75)

        self.backBtn = QPushButton(self.frame_19)
        self.backBtn.setObjectName(u"backBtn")
        self.backBtn.setMinimumSize(QSize(150, 30))
        self.backBtn.setMaximumSize(QSize(250, 30))
        self.backBtn.setStyleSheet(u"")

        self.horizontalLayout_25.addWidget(self.backBtn)

        self.horizontalSpacer_74 = QSpacerItem(40, 20, QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_25.addItem(self.horizontalSpacer_74)

        self.nextBtn = QPushButton(self.frame_19)
        self.nextBtn.setObjectName(u"nextBtn")
        self.nextBtn.setMinimumSize(QSize(150, 30))
        self.nextBtn.setMaximumSize(QSize(250, 30))
        self.nextBtn.setStyleSheet(u"")

        self.horizontalLayout_25.addWidget(self.nextBtn)

        self.horizontalSpacer_76 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_25.addItem(self.horizontalSpacer_76)


        self.verticalLayout_3.addWidget(self.frame_19)


        self.gridLayout_35.addWidget(self.mainContentPanel, 0, 1, 1, 1)

        self.leftPanel = QFrame(self.centralFrame)
        self.leftPanel.setObjectName(u"leftPanel")
        self.leftPanel.setEnabled(True)
        sizePolicy.setHeightForWidth(self.leftPanel.sizePolicy().hasHeightForWidth())
        self.leftPanel.setSizePolicy(sizePolicy)
        self.leftPanel.setMinimumSize(QSize(320, 0))
        self.leftPanel.setMaximumSize(QSize(320, 16777215))
        self.leftPanel.setStyleSheet(u"background-color:#18181b;\n"
"border-radius:10px;\n"
"margin:5px;")
        self.leftPanel.setFrameShape(QFrame.Shape.Panel)
        self.leftPanel.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout = QVBoxLayout(self.leftPanel)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.verticalLayout.setContentsMargins(0, 6, 0, 15)
        self.leftPanelScrollArea = QScrollArea(self.leftPanel)
        self.leftPanelScrollArea.setObjectName(u"leftPanelScrollArea")
        self.leftPanelScrollArea.setStyleSheet(u"QScrollBar{\n"
"	background-color:#171717;\n"
"}\n"
"\n"
"/* Scrollbar styling */\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}\n"
"\n"
"/* Horizontal bar */\n"
"QScrollBar:horizontal {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal:hover {\n"
"    backg"
                        "round-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:horizontal,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:horizontal,\n"
"QScrollBar::sub-page:horizontal {\n"
"    background: none;\n"
"}")
        self.leftPanelScrollArea.setWidgetResizable(True)
        self.reportSectionArea = QWidget()
        self.reportSectionArea.setObjectName(u"reportSectionArea")
        self.reportSectionArea.setGeometry(QRect(0, -1778, 298, 2629))
        self.reportSectionArea.setStyleSheet(u"QScrollArea{\n"
"	background-color:#2d2d2d;\n"
"}\n"
"\n"
"QFrame{\n"
"border-radius: 5px;\n"
"}\n"
"")
        self.verticalLayout_67 = QVBoxLayout(self.reportSectionArea)
        self.verticalLayout_67.setObjectName(u"verticalLayout_67")
        self.verticalLayout_67.setContentsMargins(9, -1, -1, 9)
        self.frame_59 = QFrame(self.reportSectionArea)
        self.frame_59.setObjectName(u"frame_59")
        self.frame_59.setMaximumSize(QSize(280, 50))
        self.frame_59.setStyleSheet(u"QFrame{\n"
"	background-color: rgba(244, 64, 95, 0.6);\n"
"	border-radius:6px;\n"
"	font-size: 14px;\n"
"	padding: 0 5px;\n"
"}")
        self.frame_59.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_59.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_31 = QHBoxLayout(self.frame_59)
        self.horizontalLayout_31.setObjectName(u"horizontalLayout_31")
        self.horizontalLayout_31.setContentsMargins(0, 0, 0, 0)
        self.label_5 = QLabel(self.frame_59)
        self.label_5.setObjectName(u"label_5")
        self.label_5.setStyleSheet(u"background-color: transparent;")
        self.label_5.setAlignment(Qt.AlignmentFlag.AlignRight|Qt.AlignmentFlag.AlignTrailing|Qt.AlignmentFlag.AlignVCenter)

        self.horizontalLayout_31.addWidget(self.label_5)

        self.lblFiringDuration = QLabel(self.frame_59)
        self.lblFiringDuration.setObjectName(u"lblFiringDuration")
        self.lblFiringDuration.setMinimumSize(QSize(0, 0))
        self.lblFiringDuration.setMaximumSize(QSize(16777215, 16777215))
        self.lblFiringDuration.setStyleSheet(u"background-color: transparent;")
        self.lblFiringDuration.setAlignment(Qt.AlignmentFlag.AlignLeading|Qt.AlignmentFlag.AlignLeft|Qt.AlignmentFlag.AlignVCenter)

        self.horizontalLayout_31.addWidget(self.lblFiringDuration)


        self.verticalLayout_67.addWidget(self.frame_59)

        self.frame_6 = QFrame(self.reportSectionArea)
        self.frame_6.setObjectName(u"frame_6")
        self.frame_6.setMinimumSize(QSize(0, 0))
        self.frame_6.setMaximumSize(QSize(280, 16777215))
        self.frame_6.setStyleSheet(u"QFrame#frame_6{\n"
"background-color:#18181b;\n"
"border: 1px solid green;\n"
"}")
        self.frame_6.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_6.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_70 = QVBoxLayout(self.frame_6)
        self.verticalLayout_70.setObjectName(u"verticalLayout_70")
        self.dataLoaderFrame = QFrame(self.frame_6)
        self.dataLoaderFrame.setObjectName(u"dataLoaderFrame")
        self.dataLoaderFrame.setMinimumSize(QSize(0, 80))
        self.dataLoaderFrame.setStyleSheet(u"QPushButton{\n"
"	border-radius:10px;\n"
"\n"
"}\n"
"\n"
"QFrame{\n"
"	background-color:#18181b\n"
"}")
        self.dataLoaderFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.dataLoaderFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_34 = QHBoxLayout(self.dataLoaderFrame)
        self.horizontalLayout_34.setObjectName(u"horizontalLayout_34")
        self.horizontalLayout_34.setContentsMargins(0, 0, 0, 0)
        self.btnTempDataInd = QPushButton(self.dataLoaderFrame)
        self.btnTempDataInd.setObjectName(u"btnTempDataInd")
        self.btnTempDataInd.setMinimumSize(QSize(100, 80))
        self.btnTempDataInd.setMaximumSize(QSize(16777215, 80))
        self.btnTempDataInd.setFont(font1)
        self.btnTempDataInd.setStyleSheet(u"QPushButton{\n"
"	background-color: rgba(4, 120, 87, 0.3);\n"
"	\n"
"	padding:5px;\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	background-color:#47a08e;\n"
"}\n"
"\n"
"QPushButton:pressed{\n"
"	background-color:black;\n"
"}")

        self.horizontalLayout_34.addWidget(self.btnTempDataInd)

        self.btnPressureDataInd = QPushButton(self.dataLoaderFrame)
        self.btnPressureDataInd.setObjectName(u"btnPressureDataInd")
        self.btnPressureDataInd.setMinimumSize(QSize(100, 80))
        self.btnPressureDataInd.setMaximumSize(QSize(16777215, 80))
        self.btnPressureDataInd.setFont(font1)
        self.btnPressureDataInd.setStyleSheet(u"\n"
"QPushButton{\n"
"	background-color:rgba(29, 78, 216, 0.3);\n"
"	\n"
"	padding:5px;\n"
"\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	background-color:#7b92d4;\n"
"}\n"
"\n"
"QPushButton:pressed{\n"
"	background-color:black;\n"
"}")

        self.horizontalLayout_34.addWidget(self.btnPressureDataInd)


        self.verticalLayout_70.addWidget(self.dataLoaderFrame)

        self.frame_30 = QFrame(self.frame_6)
        self.frame_30.setObjectName(u"frame_30")
        self.frame_30.setStyleSheet(u"QFrame{\n"
"background-color: #18181b;\n"
"}")
        self.frame_30.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_30.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_20 = QHBoxLayout(self.frame_30)
        self.horizontalLayout_20.setObjectName(u"horizontalLayout_20")
        self.horizontalLayout_20.setContentsMargins(0, 0, 0, 0)
        self.btnLoadData = QPushButton(self.frame_30)
        self.btnLoadData.setObjectName(u"btnLoadData")
        self.btnLoadData.setMinimumSize(QSize(0, 40))
        self.btnLoadData.setMaximumSize(QSize(150, 16777215))
        self.btnLoadData.setStyleSheet(u"QPushButton {\n"
"	\n"
"	\n"
"	background-color:  #276578; /* Darker blue background on hover */  \n"
"    border-radius:15px;\n"
"	padding:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color:   #11192c;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"   	\n"
"	background-color: qradialgradient(spread:pad, cx:0.502, cy:0.497727, radius:2, fx:0.5, fy:0.5, stop:0 rgba(69, 157, 66, 255), stop:1 rgba(255, 255, 255, 255));\n"
"    border: 2px solid #16a085; /* Darker green border on click */\n"
"}\n"
"")

        self.horizontalLayout_20.addWidget(self.btnLoadData)


        self.verticalLayout_70.addWidget(self.frame_30)


        self.verticalLayout_67.addWidget(self.frame_6)

        self.previewFrame = QFrame(self.reportSectionArea)
        self.previewFrame.setObjectName(u"previewFrame")
        self.previewFrame.setMinimumSize(QSize(0, 350))
        self.previewFrame.setMaximumSize(QSize(280, 350))
        self.previewFrame.setStyleSheet(u"QFrame#previewFrame{\n"
"	background-color:rgba(51, 51, 51, 0.7);\n"
"	border-radius:10px;\n"
"	margin:5px;\n"
"}")
        self.previewFrame.setFrameShape(QFrame.Shape.Box)
        self.previewFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_12 = QVBoxLayout(self.previewFrame)
        self.verticalLayout_12.setObjectName(u"verticalLayout_12")
        self.verticalLayout_12.setContentsMargins(6, 6, 6, 6)
        self.lblReportPreview = QLabel(self.previewFrame)
        self.lblReportPreview.setObjectName(u"lblReportPreview")
        self.lblReportPreview.setMinimumSize(QSize(0, 30))
        self.lblReportPreview.setMaximumSize(QSize(16777215, 30))
        self.lblReportPreview.setFont(font1)
        self.lblReportPreview.setStyleSheet(u"background-color:transparent;\n"
"font-size:18px;")
        self.lblReportPreview.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_12.addWidget(self.lblReportPreview)

        self.scrlAreaReportPreview = QScrollArea(self.previewFrame)
        self.scrlAreaReportPreview.setObjectName(u"scrlAreaReportPreview")
        self.scrlAreaReportPreview.setStyleSheet(u"QWidget{\n"
"background-color:black;\n"
"}")
        self.scrlAreaReportPreview.setWidgetResizable(True)
        self.scrollAreaWidgetContents = QWidget()
        self.scrollAreaWidgetContents.setObjectName(u"scrollAreaWidgetContents")
        self.scrollAreaWidgetContents.setGeometry(QRect(0, 0, 248, 282))
        self.scrlAreaReportPreview.setWidget(self.scrollAreaWidgetContents)

        self.verticalLayout_12.addWidget(self.scrlAreaReportPreview)


        self.verticalLayout_67.addWidget(self.previewFrame)

        self.line_2 = QFrame(self.reportSectionArea)
        self.line_2.setObjectName(u"line_2")
        self.line_2.setStyleSheet(u"background-color:#59779f")
        self.line_2.setFrameShape(QFrame.Shape.HLine)
        self.line_2.setFrameShadow(QFrame.Shadow.Sunken)

        self.verticalLayout_67.addWidget(self.line_2)

        self.lblReportSect = QLabel(self.reportSectionArea)
        self.lblReportSect.setObjectName(u"lblReportSect")
        self.lblReportSect.setMinimumSize(QSize(0, 35))
        self.lblReportSect.setMaximumSize(QSize(280, 30))
        self.lblReportSect.setFont(font2)
        self.lblReportSect.setStyleSheet(u"font-size:25px;\n"
"background-color:none;\n"
"color:rgb(178, 178, 178);\n"
"font-family: Arial;")
        self.lblReportSect.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_67.addWidget(self.lblReportSect)

        self.testPrereqFrame = QFrame(self.reportSectionArea)
        self.testPrereqFrame.setObjectName(u"testPrereqFrame")
        self.testPrereqFrame.setMaximumSize(QSize(280, 16777215))
        self.testPrereqFrame.setStyleSheet(u"")
        self.testPrereqFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.testPrereqFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_2 = QVBoxLayout(self.testPrereqFrame)
        self.verticalLayout_2.setSpacing(0)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.btnTestPrereq = QPushButton(self.testPrereqFrame)
        self.btnTestPrereq.setObjectName(u"btnTestPrereq")
        self.btnTestPrereq.setMinimumSize(QSize(0, 45))
        self.btnTestPrereq.setStyleSheet(u"QPushButton{\n"
"	background-color: #1e293c;\n"
"	border-radius: 17px;\n"
"	border: 1px solid #303030;\n"
"	padding:5px;\n"
"	font-size: 19px;\n"
"	font-family: Helvetica;\n"
"	font-weight: bold;\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	background-color:#47a08e;\n"
"}\n"
"\n"
"QPushButton:pressed{\n"
"	background-color:black;\n"
"}\n"
"\n"
"")

        self.verticalLayout_2.addWidget(self.btnTestPrereq)

        self.testPrereqSubSections = QWidget(self.testPrereqFrame)
        self.testPrereqSubSections.setObjectName(u"testPrereqSubSections")
        self.testPrereqSubSections.setMaximumSize(QSize(16777215, 16777215))
        self.testPrereqSubSections.setStyleSheet(u"QWidget{\n"
"background-color:#374151;\n"
"}\n"
"\n"
"QPushButton{\n"
"	background-color: #1e1e1e;\n"
"	color: white;\n"
"	padding:5px;\n"
"	font-size:17px;\n"
"	border:1px solid #446699;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	background-color:#47a08e;\n"
"}\n"
"\n"
"QPushButton:pressed{\n"
"	background-color:#5C5C5C;\n"
"}")
        self.verticalLayout_27 = QVBoxLayout(self.testPrereqSubSections)
        self.verticalLayout_27.setSpacing(0)
        self.verticalLayout_27.setObjectName(u"verticalLayout_27")
        self.btnBasicInfo = QPushButton(self.testPrereqSubSections)
        self.btnBasicInfo.setObjectName(u"btnBasicInfo")
        self.btnBasicInfo.setMinimumSize(QSize(0, 40))
        self.btnBasicInfo.setMaximumSize(QSize(16777215, 40))
        self.btnBasicInfo.setStyleSheet(u"")

        self.verticalLayout_27.addWidget(self.btnBasicInfo)

        self.btnSysSpec = QPushButton(self.testPrereqSubSections)
        self.btnSysSpec.setObjectName(u"btnSysSpec")
        self.btnSysSpec.setMinimumSize(QSize(0, 40))
        self.btnSysSpec.setMaximumSize(QSize(16777215, 40))
        self.btnSysSpec.setStyleSheet(u"")

        self.verticalLayout_27.addWidget(self.btnSysSpec)

        self.btnPropSpec = QPushButton(self.testPrereqSubSections)
        self.btnPropSpec.setObjectName(u"btnPropSpec")
        self.btnPropSpec.setMinimumSize(QSize(0, 40))
        self.btnPropSpec.setMaximumSize(QSize(16777215, 40))
        self.btnPropSpec.setStyleSheet(u"")

        self.verticalLayout_27.addWidget(self.btnPropSpec)

        self.btnCatSpec = QPushButton(self.testPrereqSubSections)
        self.btnCatSpec.setObjectName(u"btnCatSpec")
        self.btnCatSpec.setMinimumSize(QSize(0, 40))
        self.btnCatSpec.setMaximumSize(QSize(16777215, 40))
        self.btnCatSpec.setStyleSheet(u"")

        self.verticalLayout_27.addWidget(self.btnCatSpec)

        self.btnCompDet = QPushButton(self.testPrereqSubSections)
        self.btnCompDet.setObjectName(u"btnCompDet")
        self.btnCompDet.setMinimumSize(QSize(0, 40))
        self.btnCompDet.setMaximumSize(QSize(16777215, 40))
        self.btnCompDet.setStyleSheet(u"")

        self.verticalLayout_27.addWidget(self.btnCompDet)

        self.btnTestDet = QPushButton(self.testPrereqSubSections)
        self.btnTestDet.setObjectName(u"btnTestDet")
        self.btnTestDet.setMinimumSize(QSize(0, 40))
        self.btnTestDet.setMaximumSize(QSize(16777215, 40))
        self.btnTestDet.setStyleSheet(u"")

        self.verticalLayout_27.addWidget(self.btnTestDet)


        self.verticalLayout_2.addWidget(self.testPrereqSubSections)


        self.verticalLayout_67.addWidget(self.testPrereqFrame)

        self.line = QFrame(self.reportSectionArea)
        self.line.setObjectName(u"line")
        self.line.setStyleSheet(u"background-color:#34455d;")
        self.line.setFrameShape(QFrame.Shape.HLine)
        self.line.setFrameShadow(QFrame.Shadow.Sunken)

        self.verticalLayout_67.addWidget(self.line)

        self.htrOpFrame = QFrame(self.reportSectionArea)
        self.htrOpFrame.setObjectName(u"htrOpFrame")
        self.htrOpFrame.setMaximumSize(QSize(280, 16777215))
        self.htrOpFrame.setStyleSheet(u"font-size:15px;")
        self.htrOpFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.htrOpFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_18 = QVBoxLayout(self.htrOpFrame)
        self.verticalLayout_18.setSpacing(0)
        self.verticalLayout_18.setObjectName(u"verticalLayout_18")
        self.verticalLayout_18.setContentsMargins(0, 0, 0, 0)
        self.btnHtrOp = QPushButton(self.htrOpFrame)
        self.btnHtrOp.setObjectName(u"btnHtrOp")
        self.btnHtrOp.setMinimumSize(QSize(0, 45))
        self.btnHtrOp.setStyleSheet(u"QPushButton{\n"
"	background-color: #1e293c;\n"
"	border-radius: 17px;\n"
"	border: 1px solid #303030;\n"
"	padding:5px;\n"
"	font-size: 19px;\n"
"	font-family: Helvetica;\n"
"	font-weight: bold;\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	background-color:#47a08e;\n"
"}\n"
"\n"
"QPushButton:pressed{\n"
"	background-color:black;\n"
"}\n"
"\n"
"")

        self.verticalLayout_18.addWidget(self.btnHtrOp)

        self.htrOpSubSections = QWidget(self.htrOpFrame)
        self.htrOpSubSections.setObjectName(u"htrOpSubSections")
        self.htrOpSubSections.setMaximumSize(QSize(16777215, 16777215))
        self.htrOpSubSections.setStyleSheet(u"QWidget{\n"
"background-color:#374151\n"
"}\n"
"\n"
"QPushButton{\n"
"	background-color: #1e1e1e;\n"
"	color: white;\n"
"	padding:5px;\n"
"	font-size:17px;\n"
"	border:1px solid #446699;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	background-color:#47a08e;\n"
"}\n"
"\n"
"QPushButton:pressed{\n"
"	background-color:#5C5C5C;\n"
"}")
        self.verticalLayout_28 = QVBoxLayout(self.htrOpSubSections)
        self.verticalLayout_28.setSpacing(0)
        self.verticalLayout_28.setObjectName(u"verticalLayout_28")
        self.btnHtrInfo = QPushButton(self.htrOpSubSections)
        self.btnHtrInfo.setObjectName(u"btnHtrInfo")
        self.btnHtrInfo.setMinimumSize(QSize(0, 40))
        self.btnHtrInfo.setMaximumSize(QSize(16777215, 40))
        self.btnHtrInfo.setStyleSheet(u"")

        self.verticalLayout_28.addWidget(self.btnHtrInfo)

        self.btnHtrCyc = QPushButton(self.htrOpSubSections)
        self.btnHtrCyc.setObjectName(u"btnHtrCyc")
        self.btnHtrCyc.setMinimumSize(QSize(0, 40))
        self.btnHtrCyc.setMaximumSize(QSize(16777215, 40))
        self.btnHtrCyc.setStyleSheet(u"")

        self.verticalLayout_28.addWidget(self.btnHtrCyc)


        self.verticalLayout_18.addWidget(self.htrOpSubSections)


        self.verticalLayout_67.addWidget(self.htrOpFrame)

        self.line_5 = QFrame(self.reportSectionArea)
        self.line_5.setObjectName(u"line_5")
        self.line_5.setMaximumSize(QSize(280, 16777215))
        self.line_5.setStyleSheet(u"background-color:#34455d;")
        self.line_5.setFrameShape(QFrame.Shape.HLine)
        self.line_5.setFrameShadow(QFrame.Shadow.Sunken)

        self.verticalLayout_67.addWidget(self.line_5)

        self.pstTestAnFrame = QFrame(self.reportSectionArea)
        self.pstTestAnFrame.setObjectName(u"pstTestAnFrame")
        self.pstTestAnFrame.setMaximumSize(QSize(280, 16777215))
        self.pstTestAnFrame.setStyleSheet(u"font-size:15px;")
        self.pstTestAnFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.pstTestAnFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_23 = QVBoxLayout(self.pstTestAnFrame)
        self.verticalLayout_23.setSpacing(0)
        self.verticalLayout_23.setObjectName(u"verticalLayout_23")
        self.verticalLayout_23.setContentsMargins(0, 0, 0, 0)
        self.btnPstTestAn = QPushButton(self.pstTestAnFrame)
        self.btnPstTestAn.setObjectName(u"btnPstTestAn")
        self.btnPstTestAn.setMinimumSize(QSize(0, 45))
        self.btnPstTestAn.setStyleSheet(u"QPushButton{\n"
"	background-color: #1e293c;\n"
"	border-radius: 17px;\n"
"	border: 1px solid #303030;\n"
"	padding:5px;\n"
"	font-size: 19px;\n"
"	font-family: Helvetica;\n"
"	font-weight: bold;\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	background-color:#47a08e;\n"
"}\n"
"\n"
"QPushButton:pressed{\n"
"	background-color:black;\n"
"}\n"
"")

        self.verticalLayout_23.addWidget(self.btnPstTestAn)

        self.pstTestAnSubSections = QWidget(self.pstTestAnFrame)
        self.pstTestAnSubSections.setObjectName(u"pstTestAnSubSections")
        self.pstTestAnSubSections.setMaximumSize(QSize(16777215, 16777215))
        self.pstTestAnSubSections.setStyleSheet(u"QWidget{\n"
"background-color:#374151\n"
"}\n"
"\n"
"QPushButton{\n"
"	background-color: #1e1e1e;\n"
"	color: white;\n"
"	padding:5px;\n"
"	font-size:17px;\n"
"	border:1px solid #446699;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	background-color:#47a08e;\n"
"}\n"
"\n"
"QPushButton:pressed{\n"
"	background-color:#5C5C5C;\n"
"}")
        self.verticalLayout_29 = QVBoxLayout(self.pstTestAnSubSections)
        self.verticalLayout_29.setSpacing(0)
        self.verticalLayout_29.setObjectName(u"verticalLayout_29")
        self.btnPstTestObs = QPushButton(self.pstTestAnSubSections)
        self.btnPstTestObs.setObjectName(u"btnPstTestObs")
        self.btnPstTestObs.setMinimumSize(QSize(0, 40))
        self.btnPstTestObs.setMaximumSize(QSize(16777215, 40))
        self.btnPstTestObs.setStyleSheet(u"")

        self.verticalLayout_29.addWidget(self.btnPstTestObs)

        self.btnCatPostAn = QPushButton(self.pstTestAnSubSections)
        self.btnCatPostAn.setObjectName(u"btnCatPostAn")
        self.btnCatPostAn.setMinimumSize(QSize(0, 40))
        self.btnCatPostAn.setMaximumSize(QSize(16777215, 40))
        self.btnCatPostAn.setStyleSheet(u"")

        self.verticalLayout_29.addWidget(self.btnCatPostAn)

        self.btnPropPostAn = QPushButton(self.pstTestAnSubSections)
        self.btnPropPostAn.setObjectName(u"btnPropPostAn")
        self.btnPropPostAn.setMinimumSize(QSize(0, 40))
        self.btnPropPostAn.setMaximumSize(QSize(16777215, 40))
        self.btnPropPostAn.setStyleSheet(u"")

        self.verticalLayout_29.addWidget(self.btnPropPostAn)


        self.verticalLayout_23.addWidget(self.pstTestAnSubSections)


        self.verticalLayout_67.addWidget(self.pstTestAnFrame)

        self.line_6 = QFrame(self.reportSectionArea)
        self.line_6.setObjectName(u"line_6")
        self.line_6.setStyleSheet(u"background-color:#34455d;")
        self.line_6.setFrameShape(QFrame.Shape.HLine)
        self.line_6.setFrameShadow(QFrame.Shadow.Sunken)

        self.verticalLayout_67.addWidget(self.line_6)

        self.plotsFrame = QFrame(self.reportSectionArea)
        self.plotsFrame.setObjectName(u"plotsFrame")
        self.plotsFrame.setMaximumSize(QSize(280, 16777215))
        self.plotsFrame.setStyleSheet(u"font-size:15px;")
        self.plotsFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.plotsFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_25 = QVBoxLayout(self.plotsFrame)
        self.verticalLayout_25.setSpacing(0)
        self.verticalLayout_25.setObjectName(u"verticalLayout_25")
        self.verticalLayout_25.setContentsMargins(0, 0, 0, 0)
        self.btnPlots = QPushButton(self.plotsFrame)
        self.btnPlots.setObjectName(u"btnPlots")
        self.btnPlots.setMinimumSize(QSize(0, 45))
        self.btnPlots.setStyleSheet(u"QPushButton{\n"
"	background-color: #1e293c;\n"
"	border-radius: 17px;\n"
"	border: 1px solid #303030;\n"
"	padding:5px;\n"
"	font-size: 19px;\n"
"	font-family: Helvetica;\n"
"	font-weight: bold;\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	background-color:#47a08e;\n"
"}\n"
"\n"
"QPushButton:pressed{\n"
"	background-color:black;\n"
"}\n"
"")

        self.verticalLayout_25.addWidget(self.btnPlots)

        self.plotControlsFrame = QFrame(self.plotsFrame)
        self.plotControlsFrame.setObjectName(u"plotControlsFrame")
        self.plotControlsFrame.setEnabled(True)
        self.plotControlsFrame.setMinimumSize(QSize(0, 0))
        self.plotControlsFrame.setMaximumSize(QSize(16777215, 16777215))
        self.plotControlsFrame.setStyleSheet(u"QFrame{\n"
"	background-color: transparent;\n"
"}\n"
"\n"
"QPushButton{\n"
"	background-color: #1e1e1e;\n"
"	color: white;\n"
"	padding:5px;\n"
"	font-size:17px;\n"
"	border:1px solid #446699;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	background-color:#47a08e;\n"
"}\n"
"\n"
"QPushButton:pressed{\n"
"	background-color:#5C5C5C;\n"
"}")
        self.plotControlsFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.plotControlsFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_79 = QVBoxLayout(self.plotControlsFrame)
        self.verticalLayout_79.setSpacing(0)
        self.verticalLayout_79.setObjectName(u"verticalLayout_79")
        self.verticalLayout_79.setContentsMargins(0, 0, 0, 0)
        self.plotSettingsTabSlider = QWidget(self.plotControlsFrame)
        self.plotSettingsTabSlider.setObjectName(u"plotSettingsTabSlider")

        self.verticalLayout_79.addWidget(self.plotSettingsTabSlider)

        self.tabWidgetPlotSettings = QTabWidget(self.plotControlsFrame)
        self.tabWidgetPlotSettings.setObjectName(u"tabWidgetPlotSettings")
        self.tabWidgetPlotSettings.setStyleSheet(u"QTabWidget{\n"
"background-color:#374151;\n"
"border:none;\n"
"}\n"
"\n"
"QTabWidget::pane { \n"
"                border: 0px;\n"
"                border-top: 0px;\n"
"                border-bottom: 0px;\n"
"                border-left: 0px;\n"
"                border-right: 0px;\n"
"                margin: 0px;\n"
"                padding: 0px;\n"
"            }\n"
"\n"
"QFrame {\n"
"                border: none;\n"
"                margin: 2px;\n"
"                padding: 0px;\n"
"            }")
        self.tabWidgetPlotSettings.setTabPosition(QTabWidget.TabPosition.North)
        self.tabWidgetPlotSettings.setTabShape(QTabWidget.TabShape.Rounded)
        self.temperature_tab = QWidget()
        self.temperature_tab.setObjectName(u"temperature_tab")
        self.verticalLayout_17 = QVBoxLayout(self.temperature_tab)
        self.verticalLayout_17.setSpacing(0)
        self.verticalLayout_17.setObjectName(u"verticalLayout_17")
        self.verticalLayout_17.setContentsMargins(0, 0, 0, 0)
        self.tempPlotFrame = QFrame(self.temperature_tab)
        self.tempPlotFrame.setObjectName(u"tempPlotFrame")
        self.tempPlotFrame.setMinimumSize(QSize(0, 200))
        self.tempPlotFrame.setMaximumSize(QSize(16777215, 16777215))
        self.tempPlotFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.tempPlotFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_33 = QVBoxLayout(self.tempPlotFrame)
        self.verticalLayout_33.setSpacing(0)
        self.verticalLayout_33.setObjectName(u"verticalLayout_33")
        self.verticalLayout_33.setContentsMargins(0, 0, 0, 0)
        self.tempPlotSetti = QWidget(self.tempPlotFrame)
        self.tempPlotSetti.setObjectName(u"tempPlotSetti")
        self.tempPlotSetti.setMinimumSize(QSize(0, 0))
        self.tempPlotSetti.setMaximumSize(QSize(16777215, 16777215))
        self.tempPlotSetti.setStyleSheet(u"QWidget{\n"
"	background-color: #374151;\n"
"}\n"
"\n"
"QLineEdit{\n"
"	background-color: #1C1C1C;\n"
"	border-radius: 15px;\n"
"	border:1px solid #446699;\n"
"	color: white;\n"
"	padding:5px;\n"
"	font-size:16px;\n"
"}\n"
"\n"
"QLabel{\n"
"	font-size:15px;\n"
"}\n"
"\n"
"QComboBox {\n"
"    border:1px solid #446699;\n"
"    border-radius: 15px;\n"
"    padding: 4px 10px;\n"
"    background-color: #1C1C1C;\n"
"    color: #EEEEEE;\n"
"    min-width: 6em;\n"
"	font-size: 16px;\n"
"}\n"
"\n"
"QComboBox::drop-down {\n"
"    border: none;\n"
"    background: transparent;\n"
"	width: 20px;\n"
"	margin-right: 8px;\n"
"}\n"
"\n"
"QComboBox::down-arrow {\n"
"    image: url(\"assets/down-arrow.png\");  /* You can use a custom arrow image */\n"
"    width: 24px;\n"
"    height: 24px;\n"
"}\n"
"\n"
"\n"
"/* Dropdown list styling */\n"
"QComboBox QAbstractItemView {\n"
"    border: none;\n"
"	margin: 5px;\n"
"    border-radius: 10px;\n"
"    padding: 5px 0px;\n"
"    background-color: #2a2a2a;\n"
"    color: white;\n"
"   "
                        " selection-background-color: #3a3a3a;  /* Darker selection color */\n"
"    outline: 0px;  /* Remove focus border */\n"
"}\n"
"\n"
"/* Individual item styling */\n"
"QComboBox QAbstractItemView::item {\n"
"    padding: 8px 12px;  /* Vertical and horizontal padding for items */\n"
"    border: none;\n"
"    min-height: 24px;  /* Minimum height for items */\n"
"}\n"
"\n"
"/* Hover state for items */\n"
"QComboBox QAbstractItemView::item:hover {\n"
"    background-color: #3d3d3d;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"/* Selected item */\n"
"QComboBox QAbstractItemView::item:selected {\n"
"    background-color: #3d3d3d;\n"
"}\n"
"\n"
"/* Remove the default focus rectangle */\n"
"QComboBox:focus {\n"
"    border: none;\n"
"    outline: none;\n"
"}\n"
"\n"
"/* Hover state for the combobox */\n"
"QComboBox:hover {\n"
"    background-color: #252525;\n"
"}\n"
"\n"
"/* Scrollbar styling */\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
""
                        "QScrollBar::handle:vertical {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}\n"
"\n"
"/* Horizontal bar */\n"
"QScrollBar:horizontal {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:horizontal,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:horizontal,\n"
"QScrollBar::sub-page:horizontal {"
                        "\n"
"    background: none;\n"
"}\n"
"")
        self.horizontalLayout_22 = QHBoxLayout(self.tempPlotSetti)
        self.horizontalLayout_22.setSpacing(0)
        self.horizontalLayout_22.setObjectName(u"horizontalLayout_22")
        self.horizontalLayout_22.setContentsMargins(0, 0, 0, 0)
        self.labelWidgetContainer = QWidget(self.tempPlotSetti)
        self.labelWidgetContainer.setObjectName(u"labelWidgetContainer")
        self.verticalLayout_80 = QVBoxLayout(self.labelWidgetContainer)
        self.verticalLayout_80.setSpacing(9)
        self.verticalLayout_80.setObjectName(u"verticalLayout_80")
        self.verticalLayout_80.setContentsMargins(5, 0, 0, 0)
        self.lblXAxisTemp = QLabel(self.labelWidgetContainer)
        self.lblXAxisTemp.setObjectName(u"lblXAxisTemp")
        self.lblXAxisTemp.setMinimumSize(QSize(0, 30))
        self.lblXAxisTemp.setMaximumSize(QSize(16777215, 30))
        self.lblXAxisTemp.setFont(font1)
        self.lblXAxisTemp.setStyleSheet(u"")
        self.lblXAxisTemp.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_80.addWidget(self.lblXAxisTemp)

        self.lblYAxisTemp = QLabel(self.labelWidgetContainer)
        self.lblYAxisTemp.setObjectName(u"lblYAxisTemp")
        self.lblYAxisTemp.setMinimumSize(QSize(0, 30))
        self.lblYAxisTemp.setMaximumSize(QSize(16777215, 30))
        self.lblYAxisTemp.setFont(font1)
        self.lblYAxisTemp.setStyleSheet(u"")
        self.lblYAxisTemp.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_80.addWidget(self.lblYAxisTemp)

        self.lblXLabelTemp = QLabel(self.labelWidgetContainer)
        self.lblXLabelTemp.setObjectName(u"lblXLabelTemp")
        self.lblXLabelTemp.setMinimumSize(QSize(0, 30))
        self.lblXLabelTemp.setMaximumSize(QSize(16777215, 30))
        self.lblXLabelTemp.setFont(font1)
        self.lblXLabelTemp.setStyleSheet(u"")
        self.lblXLabelTemp.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_80.addWidget(self.lblXLabelTemp)

        self.lblYLabelTemp = QLabel(self.labelWidgetContainer)
        self.lblYLabelTemp.setObjectName(u"lblYLabelTemp")
        self.lblYLabelTemp.setMinimumSize(QSize(0, 30))
        self.lblYLabelTemp.setMaximumSize(QSize(16777215, 30))
        self.lblYLabelTemp.setFont(font1)
        self.lblYLabelTemp.setStyleSheet(u"")
        self.lblYLabelTemp.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_80.addWidget(self.lblYLabelTemp)


        self.horizontalLayout_22.addWidget(self.labelWidgetContainer)

        self.inputWidgetContainer = QWidget(self.tempPlotSetti)
        self.inputWidgetContainer.setObjectName(u"inputWidgetContainer")
        self.verticalLayout_81 = QVBoxLayout(self.inputWidgetContainer)
        self.verticalLayout_81.setSpacing(0)
        self.verticalLayout_81.setObjectName(u"verticalLayout_81")
        self.verticalLayout_81.setContentsMargins(0, 0, 5, 0)
        self.comboBoxXAxisTemp = CheckableComboBox(self.inputWidgetContainer)
        self.comboBoxXAxisTemp.addItem("")
        self.comboBoxXAxisTemp.setObjectName(u"comboBoxXAxisTemp")
        self.comboBoxXAxisTemp.setMinimumSize(QSize(158, 42))
        self.comboBoxXAxisTemp.setMaximumSize(QSize(16777215, 42))
        self.comboBoxXAxisTemp.setFocusPolicy(Qt.FocusPolicy.ClickFocus)
        self.comboBoxXAxisTemp.setStyleSheet(u"QFrame{\n"
"	background-color: #22324c\n"
"}")

        self.verticalLayout_81.addWidget(self.comboBoxXAxisTemp)

        self.comboBoxYAxisTemp = CheckableComboBox(self.inputWidgetContainer)
        self.comboBoxYAxisTemp.addItem("")
        self.comboBoxYAxisTemp.setObjectName(u"comboBoxYAxisTemp")
        self.comboBoxYAxisTemp.setMinimumSize(QSize(158, 42))
        self.comboBoxYAxisTemp.setMaximumSize(QSize(16777215, 42))
        self.comboBoxYAxisTemp.setFocusPolicy(Qt.FocusPolicy.ClickFocus)
        self.comboBoxYAxisTemp.setStyleSheet(u"")

        self.verticalLayout_81.addWidget(self.comboBoxYAxisTemp)

        self.lnEdtXLabelTemp = QLineEdit(self.inputWidgetContainer)
        self.lnEdtXLabelTemp.setObjectName(u"lnEdtXLabelTemp")
        self.lnEdtXLabelTemp.setMinimumSize(QSize(0, 42))
        self.lnEdtXLabelTemp.setMaximumSize(QSize(16777215, 42))

        self.verticalLayout_81.addWidget(self.lnEdtXLabelTemp)

        self.lnEdtYLabelTemp = QLineEdit(self.inputWidgetContainer)
        self.lnEdtYLabelTemp.setObjectName(u"lnEdtYLabelTemp")
        self.lnEdtYLabelTemp.setMinimumSize(QSize(0, 42))
        self.lnEdtYLabelTemp.setMaximumSize(QSize(16777215, 42))

        self.verticalLayout_81.addWidget(self.lnEdtYLabelTemp)


        self.horizontalLayout_22.addWidget(self.inputWidgetContainer)


        self.verticalLayout_33.addWidget(self.tempPlotSetti)


        self.verticalLayout_17.addWidget(self.tempPlotFrame)

        self.tempPlotBtnFrame = QFrame(self.temperature_tab)
        self.tempPlotBtnFrame.setObjectName(u"tempPlotBtnFrame")
        self.tempPlotBtnFrame.setMinimumSize(QSize(0, 40))
        self.tempPlotBtnFrame.setMaximumSize(QSize(16777215, 40))
        self.tempPlotBtnFrame.setStyleSheet(u"background-color: transparent;")
        self.tempPlotBtnFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.tempPlotBtnFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_66 = QHBoxLayout(self.tempPlotBtnFrame)
        self.horizontalLayout_66.setSpacing(0)
        self.horizontalLayout_66.setObjectName(u"horizontalLayout_66")
        self.horizontalLayout_66.setContentsMargins(0, 0, 0, 0)
        self.tempPlotBtn = QPushButton(self.tempPlotBtnFrame)
        self.tempPlotBtn.setObjectName(u"tempPlotBtn")
        self.tempPlotBtn.setMinimumSize(QSize(100, 33))
        self.tempPlotBtn.setMaximumSize(QSize(100, 33))
        self.tempPlotBtn.setStyleSheet(u"QPushButton {\n"
"   	background-color: rgba(23, 58, 141, 0.7);\n"
"    border:1px solid #446699;\n"
"	color: rgb(0, 0, 0);\n"
"    padding: 5px;\n"
"    text-align: center;\n"
"    text-decoration: none;\n"
"    display: inline-block;\n"
"    font-size: 16px;\n"
"    margin: 4px 2px;\n"
"    border-radius: 8px;\n"
"    transition-duration: 0.4s;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: rgb(23, 58, 141);\n"
"    color: white;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: rgba(23, 58, 141, 0.3); /* Even darker shade when pressed */\n"
"    color: white;\n"
"}\n"
"\n"
"")

        self.horizontalLayout_66.addWidget(self.tempPlotBtn)


        self.verticalLayout_17.addWidget(self.tempPlotBtnFrame)

        self.verticalSpacer_6 = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_17.addItem(self.verticalSpacer_6)

        self.tabWidgetPlotSettings.addTab(self.temperature_tab, "")
        self.pressure_tab = QWidget()
        self.pressure_tab.setObjectName(u"pressure_tab")
        self.verticalLayout_31 = QVBoxLayout(self.pressure_tab)
        self.verticalLayout_31.setObjectName(u"verticalLayout_31")
        self.verticalLayout_31.setContentsMargins(0, 0, 0, 0)
        self.pressurePlotFrame = QFrame(self.pressure_tab)
        self.pressurePlotFrame.setObjectName(u"pressurePlotFrame")
        self.pressurePlotFrame.setMinimumSize(QSize(0, 0))
        self.pressurePlotFrame.setMaximumSize(QSize(16777215, 16777215))
        self.pressurePlotFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.pressurePlotFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.pressurePlotFrame.setMidLineWidth(0)
        self.verticalLayout_32 = QVBoxLayout(self.pressurePlotFrame)
        self.verticalLayout_32.setSpacing(0)
        self.verticalLayout_32.setObjectName(u"verticalLayout_32")
        self.verticalLayout_32.setContentsMargins(0, 0, 0, 0)
        self.lnEdtY0PressureRelation = QLineEdit(self.pressurePlotFrame)
        self.lnEdtY0PressureRelation.setObjectName(u"lnEdtY0PressureRelation")
        self.lnEdtY0PressureRelation.setMinimumSize(QSize(0, 42))
        self.lnEdtY0PressureRelation.setMaximumSize(QSize(16777215, 42))
        self.lnEdtY0PressureRelation.setStyleSheet(u"background-color: white;\n"
"color: black;\n"
"font-size: 16px;\n"
"font-weight: bold;\n"
"font-family: arial;")
        self.lnEdtY0PressureRelation.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.lnEdtY0PressureRelation.setReadOnly(True)

        self.verticalLayout_32.addWidget(self.lnEdtY0PressureRelation)

        self.lnEdtY1PressureRelation = QLineEdit(self.pressurePlotFrame)
        self.lnEdtY1PressureRelation.setObjectName(u"lnEdtY1PressureRelation")
        self.lnEdtY1PressureRelation.setMinimumSize(QSize(0, 42))
        self.lnEdtY1PressureRelation.setMaximumSize(QSize(16777215, 42))
        self.lnEdtY1PressureRelation.setStyleSheet(u"background-color: white;\n"
"color: black;\n"
"font-size: 16px;\n"
"font-weight: bold;\n"
"font-family: arial;")
        self.lnEdtY1PressureRelation.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.lnEdtY1PressureRelation.setReadOnly(True)

        self.verticalLayout_32.addWidget(self.lnEdtY1PressureRelation)

        self.lnEdtY2PressureRelation = QLineEdit(self.pressurePlotFrame)
        self.lnEdtY2PressureRelation.setObjectName(u"lnEdtY2PressureRelation")
        self.lnEdtY2PressureRelation.setMinimumSize(QSize(0, 42))
        self.lnEdtY2PressureRelation.setMaximumSize(QSize(16777215, 42))
        self.lnEdtY2PressureRelation.setStyleSheet(u"background-color: white;\n"
"color: black;\n"
"font-size: 16px;\n"
"font-weight: bold;\n"
"font-family: arial;")
        self.lnEdtY2PressureRelation.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.lnEdtY2PressureRelation.setReadOnly(True)

        self.verticalLayout_32.addWidget(self.lnEdtY2PressureRelation)

        self.axisPressureFrame = QWidget(self.pressurePlotFrame)
        self.axisPressureFrame.setObjectName(u"axisPressureFrame")
        self.axisPressureFrame.setMinimumSize(QSize(0, 200))
        self.axisPressureFrame.setMaximumSize(QSize(16777215, 16777215))
        self.axisPressureFrame.setStyleSheet(u"QWidget{\n"
"	background-color: #374151;\n"
"}\n"
"\n"
"QLineEdit{\n"
"	background-color: #1C1C1C;\n"
"	border-radius: 15px;\n"
"	border:1px solid #446699;\n"
"	color: white;\n"
"	padding:5px;\n"
"	font-size:16px;\n"
"}\n"
"\n"
"QLabel{\n"
"	font-size:15px;\n"
"}\n"
"\n"
"QComboBox {\n"
"    border:1px solid #446699;\n"
"    border-radius: 15px;\n"
"    padding: 4px 10px;\n"
"    background-color: #1C1C1C;\n"
"    color: #EEEEEE;\n"
"    min-width: 6em;\n"
"	font-size: 16px;\n"
"}\n"
"\n"
"QComboBox::drop-down {\n"
"    border: none;\n"
"    background: transparent;\n"
"	width: 20px;\n"
"	margin-right: 8px;\n"
"}\n"
"\n"
"QComboBox::down-arrow {\n"
"    image: url(\"assets/down-arrow.png\");  /* You can use a custom arrow image */\n"
"    width: 24px;\n"
"    height: 24px;\n"
"}\n"
"\n"
"\n"
"/* Dropdown list styling */\n"
"QComboBox QAbstractItemView {\n"
"    border: none;\n"
"	margin: 5px;\n"
"    border-radius: 10px;\n"
"    padding: 5px 0px;\n"
"    background-color: #2a2a2a;\n"
"    color: white;\n"
"   "
                        " selection-background-color: #3a3a3a;  /* Darker selection color */\n"
"    outline: 0px;  /* Remove focus border */\n"
"}\n"
"\n"
"/* Individual item styling */\n"
"QComboBox QAbstractItemView::item {\n"
"    padding: 8px 12px;  /* Vertical and horizontal padding for items */\n"
"    border: none;\n"
"    min-height: 24px;  /* Minimum height for items */\n"
"}\n"
"\n"
"/* Hover state for items */\n"
"QComboBox QAbstractItemView::item:hover {\n"
"    background-color: #3d3d3d;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"/* Selected item */\n"
"QComboBox QAbstractItemView::item:selected {\n"
"    background-color: #3d3d3d;\n"
"}\n"
"\n"
"/* Remove the default focus rectangle */\n"
"QComboBox:focus {\n"
"    border: none;\n"
"    outline: none;\n"
"}\n"
"\n"
"/* Hover state for the combobox */\n"
"QComboBox:hover {\n"
"    background-color: #252525;\n"
"}\n"
"\n"
"/* Scrollbar styling */\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
""
                        "QScrollBar::handle:vertical {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}\n"
"\n"
"/* Horizontal bar */\n"
"QScrollBar:horizontal {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:horizontal,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:horizontal,\n"
"QScrollBar::sub-page:horizontal {"
                        "\n"
"    background: none;\n"
"}\n"
"")
        self.horizontalLayout_19 = QHBoxLayout(self.axisPressureFrame)
        self.horizontalLayout_19.setSpacing(0)
        self.horizontalLayout_19.setObjectName(u"horizontalLayout_19")
        self.horizontalLayout_19.setContentsMargins(12, 0, -1, 0)
        self.widget_7 = QWidget(self.axisPressureFrame)
        self.widget_7.setObjectName(u"widget_7")
        self.verticalLayout_82 = QVBoxLayout(self.widget_7)
        self.verticalLayout_82.setSpacing(9)
        self.verticalLayout_82.setObjectName(u"verticalLayout_82")
        self.verticalLayout_82.setContentsMargins(0, 0, 0, 0)
        self.lblXAxisPressure = QLabel(self.widget_7)
        self.lblXAxisPressure.setObjectName(u"lblXAxisPressure")
        self.lblXAxisPressure.setMinimumSize(QSize(0, 30))
        self.lblXAxisPressure.setMaximumSize(QSize(16777215, 30))
        self.lblXAxisPressure.setFont(font1)
        self.lblXAxisPressure.setStyleSheet(u"")
        self.lblXAxisPressure.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_82.addWidget(self.lblXAxisPressure)

        self.lblYAxisPressure = QLabel(self.widget_7)
        self.lblYAxisPressure.setObjectName(u"lblYAxisPressure")
        self.lblYAxisPressure.setMinimumSize(QSize(0, 30))
        self.lblYAxisPressure.setMaximumSize(QSize(16777215, 30))
        self.lblYAxisPressure.setFont(font1)
        self.lblYAxisPressure.setStyleSheet(u"")
        self.lblYAxisPressure.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_82.addWidget(self.lblYAxisPressure)

        self.lblXLabelPressure = QLabel(self.widget_7)
        self.lblXLabelPressure.setObjectName(u"lblXLabelPressure")
        self.lblXLabelPressure.setMinimumSize(QSize(0, 30))
        self.lblXLabelPressure.setMaximumSize(QSize(16777215, 30))
        self.lblXLabelPressure.setFont(font1)
        self.lblXLabelPressure.setStyleSheet(u"")
        self.lblXLabelPressure.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_82.addWidget(self.lblXLabelPressure)

        self.lblYLabelPressure = QLabel(self.widget_7)
        self.lblYLabelPressure.setObjectName(u"lblYLabelPressure")
        self.lblYLabelPressure.setMinimumSize(QSize(0, 30))
        self.lblYLabelPressure.setMaximumSize(QSize(16777215, 30))
        self.lblYLabelPressure.setFont(font1)
        self.lblYLabelPressure.setStyleSheet(u"")
        self.lblYLabelPressure.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_82.addWidget(self.lblYLabelPressure)


        self.horizontalLayout_19.addWidget(self.widget_7)

        self.widget_8 = QWidget(self.axisPressureFrame)
        self.widget_8.setObjectName(u"widget_8")
        self.verticalLayout_83 = QVBoxLayout(self.widget_8)
        self.verticalLayout_83.setSpacing(0)
        self.verticalLayout_83.setObjectName(u"verticalLayout_83")
        self.verticalLayout_83.setContentsMargins(0, 0, 0, 0)
        self.comboBoxXAxisPressure = CheckableComboBox(self.widget_8)
        self.comboBoxXAxisPressure.addItem("")
        self.comboBoxXAxisPressure.setObjectName(u"comboBoxXAxisPressure")
        self.comboBoxXAxisPressure.setMinimumSize(QSize(158, 42))
        self.comboBoxXAxisPressure.setMaximumSize(QSize(16677215, 42))
        self.comboBoxXAxisPressure.setFocusPolicy(Qt.FocusPolicy.ClickFocus)
        self.comboBoxXAxisPressure.setStyleSheet(u"QFrame{\n"
"	background-color: #22324c\n"
"}")

        self.verticalLayout_83.addWidget(self.comboBoxXAxisPressure)

        self.comboBoxYAxisPressure = CheckableComboBox(self.widget_8)
        self.comboBoxYAxisPressure.addItem("")
        self.comboBoxYAxisPressure.setObjectName(u"comboBoxYAxisPressure")
        self.comboBoxYAxisPressure.setMinimumSize(QSize(158, 42))
        self.comboBoxYAxisPressure.setMaximumSize(QSize(16677215, 42))
        self.comboBoxYAxisPressure.setFocusPolicy(Qt.FocusPolicy.ClickFocus)
        self.comboBoxYAxisPressure.setStyleSheet(u"")

        self.verticalLayout_83.addWidget(self.comboBoxYAxisPressure)

        self.lnEdtXLabelPressure = QLineEdit(self.widget_8)
        self.lnEdtXLabelPressure.setObjectName(u"lnEdtXLabelPressure")
        self.lnEdtXLabelPressure.setMinimumSize(QSize(0, 42))
        self.lnEdtXLabelPressure.setMaximumSize(QSize(16777215, 42))

        self.verticalLayout_83.addWidget(self.lnEdtXLabelPressure)

        self.lnEdtYLabelPressure = QLineEdit(self.widget_8)
        self.lnEdtYLabelPressure.setObjectName(u"lnEdtYLabelPressure")
        self.lnEdtYLabelPressure.setMinimumSize(QSize(0, 42))
        self.lnEdtYLabelPressure.setMaximumSize(QSize(16777215, 42))

        self.verticalLayout_83.addWidget(self.lnEdtYLabelPressure)


        self.horizontalLayout_19.addWidget(self.widget_8)


        self.verticalLayout_32.addWidget(self.axisPressureFrame)

        self.pressurePlotSetti = QWidget(self.pressurePlotFrame)
        self.pressurePlotSetti.setObjectName(u"pressurePlotSetti")
        self.pressurePlotSetti.setMinimumSize(QSize(0, 0))
        self.pressurePlotSetti.setMaximumSize(QSize(295, 16777215))
        self.pressurePlotSetti.setStyleSheet(u"QWidget{\n"
"	background-color: #374151;\n"
"}\n"
"\n"
"QSpinBox{\n"
"	background-color: #1C1C1C;\n"
"	border-radius: 15px;\n"
"	border:1px solid #446699;\n"
"	color: white;\n"
"	padding:5px;\n"
"	font-size:16px;\n"
"}\n"
"\n"
"QLabel{\n"
"	font-size:15px;\n"
"}\n"
"\n"
"QComboBox {\n"
"    border: 1px solid #303030;\n"
"    border-radius: 15px;\n"
"    padding: 4px 10px;\n"
"    background-color: #1C1C1C;\n"
"    color: #EEEEEE;\n"
"    min-width: 6em;\n"
"	font-size: 16px;\n"
"}\n"
"\n"
"QComboBox::drop-down {\n"
"    border: none;\n"
"    background: transparent;\n"
"	width: 20px;\n"
"	margin-right: 8px;\n"
"}\n"
"\n"
"QComboBox::down-arrow {\n"
"    image: url(\"assets/down-arrow.png\");  /* You can use a custom arrow image */\n"
"    width: 24px;\n"
"    height: 24px;\n"
"}\n"
"\n"
"\n"
"/* Dropdown list styling */\n"
"QComboBox QAbstractItemView {\n"
"    border: none;\n"
"	margin: 5px;\n"
"    border-radius: 10px;\n"
"    padding: 5px 0px;\n"
"    background-color: #2a2a2a;\n"
"    color: white;\n"
"   "
                        " selection-background-color: #3a3a3a;  /* Darker selection color */\n"
"    outline: 0px;  /* Remove focus border */\n"
"}\n"
"\n"
"/* Individual item styling */\n"
"QComboBox QAbstractItemView::item {\n"
"    padding: 8px 12px;  /* Vertical and horizontal padding for items */\n"
"    border: none;\n"
"    min-height: 24px;  /* Minimum height for items */\n"
"}\n"
"\n"
"/* Hover state for items */\n"
"QComboBox QAbstractItemView::item:hover {\n"
"    background-color: #3d3d3d;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"/* Selected item */\n"
"QComboBox QAbstractItemView::item:selected {\n"
"    background-color: #3d3d3d;\n"
"}\n"
"\n"
"/* Remove the default focus rectangle */\n"
"QComboBox:focus {\n"
"    border: none;\n"
"    outline: none;\n"
"}\n"
"\n"
"/* Hover state for the combobox */\n"
"QComboBox:hover {\n"
"    background-color: #252525;\n"
"}\n"
"\n"
"/* Scrollbar styling */\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
""
                        "QScrollBar::handle:vertical {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}\n"
"\n"
"/* Horizontal bar */\n"
"QScrollBar:horizontal {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:horizontal,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:horizontal,\n"
"QScrollBar::sub-page:horizontal {"
                        "\n"
"    background: none;\n"
"}\n"
"")
        self.verticalLayout_34 = QVBoxLayout(self.pressurePlotSetti)
        self.verticalLayout_34.setSpacing(0)
        self.verticalLayout_34.setObjectName(u"verticalLayout_34")
        self.verticalLayout_34.setContentsMargins(5, 0, 5, 5)
        self.lblRangePressure = QLabel(self.pressurePlotSetti)
        self.lblRangePressure.setObjectName(u"lblRangePressure")
        self.lblRangePressure.setMinimumSize(QSize(0, 28))
        self.lblRangePressure.setMaximumSize(QSize(16777215, 28))
        self.lblRangePressure.setFont(font1)
        self.lblRangePressure.setStyleSheet(u"")
        self.lblRangePressure.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_34.addWidget(self.lblRangePressure)

        self.frame_12 = QFrame(self.pressurePlotSetti)
        self.frame_12.setObjectName(u"frame_12")
        self.frame_12.setMinimumSize(QSize(0, 42))
        self.frame_12.setMaximumSize(QSize(16777215, 42))
        self.frame_12.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_12.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_24 = QHBoxLayout(self.frame_12)
        self.horizontalLayout_24.setSpacing(9)
        self.horizontalLayout_24.setObjectName(u"horizontalLayout_24")
        self.horizontalLayout_24.setContentsMargins(0, 0, 0, 0)
        self.lnEdtRangeMinPressure = QSpinBox(self.frame_12)
        self.lnEdtRangeMinPressure.setObjectName(u"lnEdtRangeMinPressure")
        self.lnEdtRangeMinPressure.setMinimumSize(QSize(0, 42))
        self.lnEdtRangeMinPressure.setMaximumSize(QSize(16777215, 42))
        self.lnEdtRangeMinPressure.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.lnEdtRangeMinPressure.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.lnEdtRangeMinPressure.setMaximum(10000000)
        self.lnEdtRangeMinPressure.setSingleStep(0)

        self.horizontalLayout_24.addWidget(self.lnEdtRangeMinPressure)

        self.lnEdtRangeMaxPressure = QSpinBox(self.frame_12)
        self.lnEdtRangeMaxPressure.setObjectName(u"lnEdtRangeMaxPressure")
        self.lnEdtRangeMaxPressure.setMinimumSize(QSize(0, 42))
        self.lnEdtRangeMaxPressure.setMaximumSize(QSize(16777215, 42))
        self.lnEdtRangeMaxPressure.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.lnEdtRangeMaxPressure.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.lnEdtRangeMaxPressure.setMaximum(10000000)
        self.lnEdtRangeMaxPressure.setSingleStep(0)

        self.horizontalLayout_24.addWidget(self.lnEdtRangeMaxPressure)


        self.verticalLayout_34.addWidget(self.frame_12)


        self.verticalLayout_32.addWidget(self.pressurePlotSetti)


        self.verticalLayout_31.addWidget(self.pressurePlotFrame)

        self.pressurePlotBtnFrame = QFrame(self.pressure_tab)
        self.pressurePlotBtnFrame.setObjectName(u"pressurePlotBtnFrame")
        self.pressurePlotBtnFrame.setMinimumSize(QSize(0, 40))
        self.pressurePlotBtnFrame.setMaximumSize(QSize(16777215, 40))
        self.pressurePlotBtnFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.pressurePlotBtnFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_26 = QHBoxLayout(self.pressurePlotBtnFrame)
        self.horizontalLayout_26.setSpacing(0)
        self.horizontalLayout_26.setObjectName(u"horizontalLayout_26")
        self.horizontalLayout_26.setContentsMargins(0, 0, 0, 0)
        self.pressurePlotBtn = QPushButton(self.pressurePlotBtnFrame)
        self.pressurePlotBtn.setObjectName(u"pressurePlotBtn")
        self.pressurePlotBtn.setMinimumSize(QSize(100, 33))
        self.pressurePlotBtn.setMaximumSize(QSize(100, 33))
        self.pressurePlotBtn.setStyleSheet(u"QPushButton {\n"
"   	background-color: rgba(23, 58, 141, 0.7);\n"
"    border:1px solid #446699;\n"
"	color: rgb(0, 0, 0);\n"
"    padding: 5px;\n"
"    text-align: center;\n"
"    text-decoration: none;\n"
"    display: inline-block;\n"
"    font-size: 16px;\n"
"    margin: 4px 2px;\n"
"    border-radius: 8px;\n"
"    transition-duration: 0.4s;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: rgb(23, 58, 141);\n"
"    color: white;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: rgba(23, 58, 141, 0.3); /* Even darker shade when pressed */\n"
"    color: white;\n"
"}\n"
"\n"
"")

        self.horizontalLayout_26.addWidget(self.pressurePlotBtn)


        self.verticalLayout_31.addWidget(self.pressurePlotBtnFrame)

        self.tabWidgetPlotSettings.addTab(self.pressure_tab, "")

        self.verticalLayout_79.addWidget(self.tabWidgetPlotSettings)

        self.verticalSpacer_59 = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_79.addItem(self.verticalSpacer_59)


        self.verticalLayout_25.addWidget(self.plotControlsFrame)


        self.verticalLayout_67.addWidget(self.plotsFrame)

        self.line_7 = QFrame(self.reportSectionArea)
        self.line_7.setObjectName(u"line_7")
        self.line_7.setStyleSheet(u"background-color:#34455d;")
        self.line_7.setFrameShape(QFrame.Shape.HLine)
        self.line_7.setFrameShadow(QFrame.Shadow.Sunken)

        self.verticalLayout_67.addWidget(self.line_7)

        self.performanceFrame = QFrame(self.reportSectionArea)
        self.performanceFrame.setObjectName(u"performanceFrame")
        self.performanceFrame.setStyleSheet(u"font-size:15px;")
        self.performanceFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.performanceFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_35 = QVBoxLayout(self.performanceFrame)
        self.verticalLayout_35.setSpacing(0)
        self.verticalLayout_35.setObjectName(u"verticalLayout_35")
        self.verticalLayout_35.setContentsMargins(0, 0, 0, 0)
        self.btnPerformance = QPushButton(self.performanceFrame)
        self.btnPerformance.setObjectName(u"btnPerformance")
        self.btnPerformance.setMinimumSize(QSize(0, 45))
        self.btnPerformance.setStyleSheet(u"QPushButton{\n"
"	background-color: #1e293c;\n"
"	border-radius: 17px;\n"
"	border: 1px solid #303030;\n"
"	padding:5px;\n"
"	font-size: 19px;\n"
"	font-family: Helvetica;\n"
"	font-weight: bold;\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	background-color:#47a08e;\n"
"}\n"
"\n"
"QPushButton:pressed{\n"
"	background-color:black;\n"
"}\n"
"")

        self.verticalLayout_35.addWidget(self.btnPerformance)

        self.perforSubSections = QWidget(self.performanceFrame)
        self.perforSubSections.setObjectName(u"perforSubSections")
        self.perforSubSections.setMinimumSize(QSize(0, 0))
        self.perforSubSections.setMaximumSize(QSize(16777215, 16777215))
        self.perforSubSections.setStyleSheet(u"QWidget{\n"
"background-color:#374151}\n"
"\n"
"QLineEdit, QDoubleSpinBox{\n"
"	background-color:rgb(11, 27, 65);\n"
"	color: white;\n"
"	padding:1px;\n"
"	font-size:15px;\n"
"	border:1px solid #446699;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QLabel{\n"
"	font-size:15px;\n"
"	color:white;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QFrame{\n"
"	background-color:#000000;\n"
"}")
        self.verticalLayout_11 = QVBoxLayout(self.perforSubSections)
        self.verticalLayout_11.setSpacing(0)
        self.verticalLayout_11.setObjectName(u"verticalLayout_11")
        self.frame_14 = QFrame(self.perforSubSections)
        self.frame_14.setObjectName(u"frame_14")
        self.frame_14.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_14.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_9 = QVBoxLayout(self.frame_14)
        self.verticalLayout_9.setObjectName(u"verticalLayout_9")
        self.verticalLayout_9.setContentsMargins(0, 0, 0, 0)
        self.frame_77 = QFrame(self.frame_14)
        self.frame_77.setObjectName(u"frame_77")
        self.frame_77.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_77.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_14 = QHBoxLayout(self.frame_77)
        self.horizontalLayout_14.setSpacing(6)
        self.horizontalLayout_14.setObjectName(u"horizontalLayout_14")
        self.horizontalLayout_14.setContentsMargins(0, 0, 0, 0)
        self.btnTempMatrix = QPushButton(self.frame_77)
        self.btnTempMatrix.setObjectName(u"btnTempMatrix")
        self.btnTempMatrix.setMinimumSize(QSize(0, 40))
        self.btnTempMatrix.setStyleSheet(u"QPushButton{\n"
"	background-color:rgba(86, 175, 95, 0.7);\n"
"	border-radius:6px;\n"
"	padding:5px;\n"
"	font-size: 15px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	background-color:#b089a1;\n"
"}\n"
"\n"
"QPushButton:pressed{\n"
"	background-color:b03781;\n"
"}")

        self.horizontalLayout_14.addWidget(self.btnTempMatrix)

        self.tempMatrixIndicator = QPushButton(self.frame_77)
        self.tempMatrixIndicator.setObjectName(u"tempMatrixIndicator")
        self.tempMatrixIndicator.setMinimumSize(QSize(40, 30))
        self.tempMatrixIndicator.setMaximumSize(QSize(40, 30))
        self.tempMatrixIndicator.setStyleSheet(u"background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(255, 0, 0, 255), stop:1 rgba(255, 67, 0, 255));\n"
"border-radius: 8px;\n"
"\n"
"")

        self.horizontalLayout_14.addWidget(self.tempMatrixIndicator)


        self.verticalLayout_9.addWidget(self.frame_77)

        self.tempMatrixDataSelection = QFrame(self.frame_14)
        self.tempMatrixDataSelection.setObjectName(u"tempMatrixDataSelection")
        self.tempMatrixDataSelection.setMinimumSize(QSize(0, 0))
        self.tempMatrixDataSelection.setFrameShape(QFrame.Shape.StyledPanel)
        self.tempMatrixDataSelection.setFrameShadow(QFrame.Shadow.Raised)
        self.gridLayout_18 = QGridLayout(self.tempMatrixDataSelection)
        self.gridLayout_18.setObjectName(u"gridLayout_18")
        self.gridLayout_18.setContentsMargins(0, 0, 0, 0)

        self.verticalLayout_9.addWidget(self.tempMatrixDataSelection)


        self.verticalLayout_11.addWidget(self.frame_14)

        self.gammaFrame = QFrame(self.perforSubSections)
        self.gammaFrame.setObjectName(u"gammaFrame")
        self.gammaFrame.setMinimumSize(QSize(0, 55))
        self.gammaFrame.setMaximumSize(QSize(16777215, 55))
        self.gammaFrame.setStyleSheet(u"")
        self.gammaFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.gammaFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.gridLayout_40 = QGridLayout(self.gammaFrame)
        self.gridLayout_40.setSpacing(0)
        self.gridLayout_40.setObjectName(u"gridLayout_40")
        self.gridLayout_40.setContentsMargins(20, 0, 20, 0)
        self.lnEdtGamma = CustomDoubleSpinBox(self.gammaFrame)
        self.lnEdtGamma.setObjectName(u"lnEdtGamma")
        self.lnEdtGamma.setMinimumSize(QSize(100, 35))
        self.lnEdtGamma.setMaximumSize(QSize(100, 35))
        self.lnEdtGamma.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.lnEdtGamma.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.lnEdtGamma.setDecimals(4)
        self.lnEdtGamma.setMinimum(0.000000000000000)
        self.lnEdtGamma.setMaximum(10000000.000000000000000)
        self.lnEdtGamma.setSingleStep(0.000000000000000)
        self.lnEdtGamma.setStepType(QAbstractSpinBox.StepType.DefaultStepType)
        self.lnEdtGamma.setValue(1.660000000000000)

        self.gridLayout_40.addWidget(self.lnEdtGamma, 0, 2, 1, 1)

        self.horizontalSpacer_38 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_40.addItem(self.horizontalSpacer_38, 0, 1, 1, 1)

        self.lblGamma = QLabel(self.gammaFrame)
        self.lblGamma.setObjectName(u"lblGamma")
        self.lblGamma.setMinimumSize(QSize(100, 30))
        self.lblGamma.setMaximumSize(QSize(100, 30))

        self.gridLayout_40.addWidget(self.lblGamma, 0, 0, 1, 1)


        self.verticalLayout_11.addWidget(self.gammaFrame)

        self.gasConstFrame = QFrame(self.perforSubSections)
        self.gasConstFrame.setObjectName(u"gasConstFrame")
        self.gasConstFrame.setMinimumSize(QSize(0, 90))
        self.gasConstFrame.setMaximumSize(QSize(16777215, 90))
        self.gasConstFrame.setStyleSheet(u"")
        self.gasConstFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.gasConstFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.gridLayout_41 = QGridLayout(self.gasConstFrame)
        self.gridLayout_41.setSpacing(0)
        self.gridLayout_41.setObjectName(u"gridLayout_41")
        self.gridLayout_41.setContentsMargins(3, 0, 20, 0)
        self.horizontalSpacer_61 = QSpacerItem(100, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_41.addItem(self.horizontalSpacer_61, 1, 0, 1, 1)

        self.lblGasConst = QLabel(self.gasConstFrame)
        self.lblGasConst.setObjectName(u"lblGasConst")
        self.lblGasConst.setMinimumSize(QSize(100, 30))
        self.lblGasConst.setMaximumSize(QSize(16777215, 30))

        self.gridLayout_41.addWidget(self.lblGasConst, 0, 0, 1, 1)

        self.lblGasConstUnit = QLabel(self.gasConstFrame)
        self.lblGasConstUnit.setObjectName(u"lblGasConstUnit")
        self.lblGasConstUnit.setMinimumSize(QSize(100, 25))
        self.lblGasConstUnit.setMaximumSize(QSize(100, 25))
        self.lblGasConstUnit.setFont(font2)
        self.lblGasConstUnit.setStyleSheet(u"padding:1px;\n"
"font-size:12px;")
        self.lblGasConstUnit.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.lblGasConstUnit.setIndent(-1)

        self.gridLayout_41.addWidget(self.lblGasConstUnit, 1, 2, 1, 1)

        self.lnEdtGasConst = CustomDoubleSpinBox(self.gasConstFrame)
        self.lnEdtGasConst.setObjectName(u"lnEdtGasConst")
        self.lnEdtGasConst.setMinimumSize(QSize(100, 35))
        self.lnEdtGasConst.setMaximumSize(QSize(100, 35))
        self.lnEdtGasConst.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.lnEdtGasConst.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.lnEdtGasConst.setDecimals(4)
        self.lnEdtGasConst.setMaximum(10000000.000000000000000)
        self.lnEdtGasConst.setSingleStep(0.000000000000000)
        self.lnEdtGasConst.setValue(372.100000000000023)

        self.gridLayout_41.addWidget(self.lnEdtGasConst, 0, 2, 1, 1)


        self.verticalLayout_11.addWidget(self.gasConstFrame)

        self.initialPropMassFrame = QFrame(self.perforSubSections)
        self.initialPropMassFrame.setObjectName(u"initialPropMassFrame")
        self.initialPropMassFrame.setMinimumSize(QSize(0, 55))
        self.initialPropMassFrame.setMaximumSize(QSize(16777215, 55))
        self.initialPropMassFrame.setStyleSheet(u"")
        self.initialPropMassFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.initialPropMassFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.gridLayout_44 = QGridLayout(self.initialPropMassFrame)
        self.gridLayout_44.setSpacing(0)
        self.gridLayout_44.setObjectName(u"gridLayout_44")
        self.gridLayout_44.setContentsMargins(3, 0, 20, 0)
        self.lnEdtInitialPropMass = CustomDoubleSpinBox(self.initialPropMassFrame)
        self.lnEdtInitialPropMass.setObjectName(u"lnEdtInitialPropMass")
        self.lnEdtInitialPropMass.setMinimumSize(QSize(100, 35))
        self.lnEdtInitialPropMass.setMaximumSize(QSize(100, 35))
        self.lnEdtInitialPropMass.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.lnEdtInitialPropMass.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.lnEdtInitialPropMass.setDecimals(6)
        self.lnEdtInitialPropMass.setMaximum(10000000.000000000000000)
        self.lnEdtInitialPropMass.setSingleStep(0.000000000000000)

        self.gridLayout_44.addWidget(self.lnEdtInitialPropMass, 0, 1, 1, 1)

        self.lblInitialPropMass = QLabel(self.initialPropMassFrame)
        self.lblInitialPropMass.setObjectName(u"lblInitialPropMass")
        self.lblInitialPropMass.setMinimumSize(QSize(100, 30))
        self.lblInitialPropMass.setMaximumSize(QSize(16777215, 30))

        self.gridLayout_44.addWidget(self.lblInitialPropMass, 0, 0, 1, 1)


        self.verticalLayout_11.addWidget(self.initialPropMassFrame)

        self.finalPropMassFrame = QFrame(self.perforSubSections)
        self.finalPropMassFrame.setObjectName(u"finalPropMassFrame")
        self.finalPropMassFrame.setMinimumSize(QSize(0, 55))
        self.finalPropMassFrame.setMaximumSize(QSize(16777215, 55))
        self.finalPropMassFrame.setStyleSheet(u"")
        self.finalPropMassFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.finalPropMassFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.gridLayout_47 = QGridLayout(self.finalPropMassFrame)
        self.gridLayout_47.setSpacing(0)
        self.gridLayout_47.setObjectName(u"gridLayout_47")
        self.gridLayout_47.setContentsMargins(3, 0, 20, 0)
        self.lblFinalPropMass = QLabel(self.finalPropMassFrame)
        self.lblFinalPropMass.setObjectName(u"lblFinalPropMass")
        self.lblFinalPropMass.setMinimumSize(QSize(100, 35))
        self.lblFinalPropMass.setMaximumSize(QSize(16777215, 35))

        self.gridLayout_47.addWidget(self.lblFinalPropMass, 0, 0, 1, 1)

        self.lnEdtFinalPropMass = CustomDoubleSpinBox(self.finalPropMassFrame)
        self.lnEdtFinalPropMass.setObjectName(u"lnEdtFinalPropMass")
        self.lnEdtFinalPropMass.setMinimumSize(QSize(100, 35))
        self.lnEdtFinalPropMass.setMaximumSize(QSize(100, 35))
        self.lnEdtFinalPropMass.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.lnEdtFinalPropMass.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.lnEdtFinalPropMass.setDecimals(6)
        self.lnEdtFinalPropMass.setMaximum(10000000.000000000000000)
        self.lnEdtFinalPropMass.setSingleStep(0.000000000000000)

        self.gridLayout_47.addWidget(self.lnEdtFinalPropMass, 0, 1, 1, 1)


        self.verticalLayout_11.addWidget(self.finalPropMassFrame)

        self.chamberPressureRangeFrame = QFrame(self.perforSubSections)
        self.chamberPressureRangeFrame.setObjectName(u"chamberPressureRangeFrame")
        self.chamberPressureRangeFrame.setMinimumSize(QSize(0, 90))
        self.chamberPressureRangeFrame.setMaximumSize(QSize(16777215, 90))
        self.chamberPressureRangeFrame.setStyleSheet(u"")
        self.chamberPressureRangeFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.chamberPressureRangeFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_30 = QVBoxLayout(self.chamberPressureRangeFrame)
        self.verticalLayout_30.setSpacing(0)
        self.verticalLayout_30.setObjectName(u"verticalLayout_30")
        self.verticalLayout_30.setContentsMargins(0, 0, 0, 0)
        self.lblChamberPressureRange = QLabel(self.chamberPressureRangeFrame)
        self.lblChamberPressureRange.setObjectName(u"lblChamberPressureRange")
        self.lblChamberPressureRange.setMinimumSize(QSize(0, 25))
        self.lblChamberPressureRange.setMaximumSize(QSize(16777215, 25))
        self.lblChamberPressureRange.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_30.addWidget(self.lblChamberPressureRange)

        self.frame_3 = QFrame(self.chamberPressureRangeFrame)
        self.frame_3.setObjectName(u"frame_3")
        self.frame_3.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_3.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_28 = QHBoxLayout(self.frame_3)
        self.horizontalLayout_28.setSpacing(0)
        self.horizontalLayout_28.setObjectName(u"horizontalLayout_28")
        self.horizontalLayout_28.setContentsMargins(0, 0, 0, 0)
        self.lnEdtChambPressRangeMin = CustomDoubleSpinBox(self.frame_3)
        self.lnEdtChambPressRangeMin.setObjectName(u"lnEdtChambPressRangeMin")
        self.lnEdtChambPressRangeMin.setMinimumSize(QSize(100, 35))
        self.lnEdtChambPressRangeMin.setMaximumSize(QSize(100, 35))
        self.lnEdtChambPressRangeMin.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.lnEdtChambPressRangeMin.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.lnEdtChambPressRangeMin.setMaximum(10000000.000000000000000)
        self.lnEdtChambPressRangeMin.setSingleStep(0.000000000000000)

        self.horizontalLayout_28.addWidget(self.lnEdtChambPressRangeMin)

        self.lnEdtChambPressRangeMax = CustomDoubleSpinBox(self.frame_3)
        self.lnEdtChambPressRangeMax.setObjectName(u"lnEdtChambPressRangeMax")
        self.lnEdtChambPressRangeMax.setMinimumSize(QSize(100, 35))
        self.lnEdtChambPressRangeMax.setMaximumSize(QSize(100, 35))
        self.lnEdtChambPressRangeMax.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.lnEdtChambPressRangeMax.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.lnEdtChambPressRangeMax.setMaximum(10000000.000000000000000)
        self.lnEdtChambPressRangeMax.setSingleStep(0.000000000000000)

        self.horizontalLayout_28.addWidget(self.lnEdtChambPressRangeMax)


        self.verticalLayout_30.addWidget(self.frame_3)


        self.verticalLayout_11.addWidget(self.chamberPressureRangeFrame)

        self.vacuumPressureRangeFrame = QFrame(self.perforSubSections)
        self.vacuumPressureRangeFrame.setObjectName(u"vacuumPressureRangeFrame")
        self.vacuumPressureRangeFrame.setMinimumSize(QSize(0, 90))
        self.vacuumPressureRangeFrame.setMaximumSize(QSize(16777215, 90))
        self.vacuumPressureRangeFrame.setStyleSheet(u"")
        self.vacuumPressureRangeFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.vacuumPressureRangeFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_38 = QVBoxLayout(self.vacuumPressureRangeFrame)
        self.verticalLayout_38.setSpacing(0)
        self.verticalLayout_38.setObjectName(u"verticalLayout_38")
        self.verticalLayout_38.setContentsMargins(0, 0, 0, 0)
        self.lblVacuumPressureRange = QLabel(self.vacuumPressureRangeFrame)
        self.lblVacuumPressureRange.setObjectName(u"lblVacuumPressureRange")
        self.lblVacuumPressureRange.setMinimumSize(QSize(0, 25))
        self.lblVacuumPressureRange.setMaximumSize(QSize(16777215, 25))
        self.lblVacuumPressureRange.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_38.addWidget(self.lblVacuumPressureRange)

        self.frame_4 = QFrame(self.vacuumPressureRangeFrame)
        self.frame_4.setObjectName(u"frame_4")
        self.frame_4.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_4.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_29 = QHBoxLayout(self.frame_4)
        self.horizontalLayout_29.setSpacing(0)
        self.horizontalLayout_29.setObjectName(u"horizontalLayout_29")
        self.horizontalLayout_29.setContentsMargins(0, 0, 0, 0)
        self.lnEdtVacPressRangeMin = CustomDoubleSpinBox(self.frame_4)
        self.lnEdtVacPressRangeMin.setObjectName(u"lnEdtVacPressRangeMin")
        self.lnEdtVacPressRangeMin.setMinimumSize(QSize(100, 35))
        self.lnEdtVacPressRangeMin.setMaximumSize(QSize(100, 35))
        self.lnEdtVacPressRangeMin.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.lnEdtVacPressRangeMin.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.lnEdtVacPressRangeMin.setMaximum(10000000.000000000000000)
        self.lnEdtVacPressRangeMin.setSingleStep(0.000000000000000)

        self.horizontalLayout_29.addWidget(self.lnEdtVacPressRangeMin)

        self.lnEdtVacPressRangeMax = CustomDoubleSpinBox(self.frame_4)
        self.lnEdtVacPressRangeMax.setObjectName(u"lnEdtVacPressRangeMax")
        self.lnEdtVacPressRangeMax.setMinimumSize(QSize(100, 35))
        self.lnEdtVacPressRangeMax.setMaximumSize(QSize(100, 35))
        self.lnEdtVacPressRangeMax.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.lnEdtVacPressRangeMax.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.lnEdtVacPressRangeMax.setMaximum(10000000.000000000000000)
        self.lnEdtVacPressRangeMax.setSingleStep(0.000000000000000)

        self.horizontalLayout_29.addWidget(self.lnEdtVacPressRangeMax)


        self.verticalLayout_38.addWidget(self.frame_4)


        self.verticalLayout_11.addWidget(self.vacuumPressureRangeFrame)

        self.calculateBtnFrame = QFrame(self.perforSubSections)
        self.calculateBtnFrame.setObjectName(u"calculateBtnFrame")
        self.calculateBtnFrame.setMinimumSize(QSize(0, 45))
        self.calculateBtnFrame.setMaximumSize(QSize(16777215, 45))
        self.calculateBtnFrame.setStyleSheet(u"background-color:transparent;")
        self.calculateBtnFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.calculateBtnFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.gridLayout_45 = QGridLayout(self.calculateBtnFrame)
        self.gridLayout_45.setSpacing(0)
        self.gridLayout_45.setObjectName(u"gridLayout_45")
        self.gridLayout_45.setContentsMargins(0, 0, 0, 0)
        self.btnCalculate = QPushButton(self.calculateBtnFrame)
        self.btnCalculate.setObjectName(u"btnCalculate")
        self.btnCalculate.setMinimumSize(QSize(0, 40))
        self.btnCalculate.setMaximumSize(QSize(150, 40))
        self.btnCalculate.setStyleSheet(u"QPushButton{\n"
"	background-color: #fe236b;\n"
"	border-radius:10px;\n"
"	 padding:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	background-color:#47a08e;\n"
"}\n"
"\n"
"QPushButton:pressed{\n"
"	background-color:black;\n"
"}\n"
"")

        self.gridLayout_45.addWidget(self.btnCalculate, 0, 0, 1, 1)


        self.verticalLayout_11.addWidget(self.calculateBtnFrame)


        self.verticalLayout_35.addWidget(self.perforSubSections)


        self.verticalLayout_67.addWidget(self.performanceFrame)

        self.line_8 = QFrame(self.reportSectionArea)
        self.line_8.setObjectName(u"line_8")
        self.line_8.setStyleSheet(u"background-color:#34455d;")
        self.line_8.setFrameShape(QFrame.Shape.HLine)
        self.line_8.setFrameShadow(QFrame.Shadow.Sunken)

        self.verticalLayout_67.addWidget(self.line_8)

        self.frame_29 = QFrame(self.reportSectionArea)
        self.frame_29.setObjectName(u"frame_29")
        self.frame_29.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_29.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_14 = QVBoxLayout(self.frame_29)
        self.verticalLayout_14.setObjectName(u"verticalLayout_14")
        self.verticalLayout_14.setContentsMargins(0, 0, 0, 0)
        self.btnTestAuthorization = QPushButton(self.frame_29)
        self.btnTestAuthorization.setObjectName(u"btnTestAuthorization")
        self.btnTestAuthorization.setMinimumSize(QSize(0, 45))
        self.btnTestAuthorization.setMaximumSize(QSize(280, 16777215))
        self.btnTestAuthorization.setStyleSheet(u"QPushButton{\n"
"	background-color: #1e293c;\n"
"	border-radius: 17px;\n"
"	border: 1px solid #303030;\n"
"	padding:5px;\n"
"	font-size: 19px;\n"
"	font-family: Helvetica;\n"
"	font-weight: bold;\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	background-color:#47a08e;\n"
"}\n"
"\n"
"QPushButton:pressed{\n"
"	background-color:black;\n"
"}\n"
"")

        self.verticalLayout_14.addWidget(self.btnTestAuthorization)


        self.verticalLayout_67.addWidget(self.frame_29)

        self.verticalSpacer_58 = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_67.addItem(self.verticalSpacer_58)

        self.leftPanelScrollArea.setWidget(self.reportSectionArea)

        self.verticalLayout.addWidget(self.leftPanelScrollArea)


        self.gridLayout_35.addWidget(self.leftPanel, 0, 0, 1, 1)


        self.gridLayout_24.addWidget(self.centralFrame, 0, 0, 1, 1)

        self.centralWidgetScrollArea.setWidget(self.centralContentScrollArea)

        self.gridLayout_28.addWidget(self.centralWidgetScrollArea, 0, 0, 1, 1)

        self.tabWidget.addTab(self.mainTab, "")
        self.filterTab = QWidget()
        self.filterTab.setObjectName(u"filterTab")
        self.verticalLayout_61 = QVBoxLayout(self.filterTab)
        self.verticalLayout_61.setObjectName(u"verticalLayout_61")
        self.label_4 = QLabel(self.filterTab)
        self.label_4.setObjectName(u"label_4")
        self.label_4.setStyleSheet(u"font-family: Arial;\n"
"font-size: 30px;\n"
"font-weight: bold;\n"
"color:white;\n"
"")
        self.label_4.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_61.addWidget(self.label_4)

        self.frame_76 = QFrame(self.filterTab)
        self.frame_76.setObjectName(u"frame_76")
        self.frame_76.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_76.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_76 = QVBoxLayout(self.frame_76)
        self.verticalLayout_76.setSpacing(6)
        self.verticalLayout_76.setObjectName(u"verticalLayout_76")
        self.frame_78 = QFrame(self.frame_76)
        self.frame_78.setObjectName(u"frame_78")
        self.frame_78.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_78.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_23 = QHBoxLayout(self.frame_78)
        self.horizontalLayout_23.setSpacing(10)
        self.horizontalLayout_23.setObjectName(u"horizontalLayout_23")
        self.databaseViewer = QFrame(self.frame_78)
        self.databaseViewer.setObjectName(u"databaseViewer")
        self.databaseViewer.setStyleSheet(u"background-color:#18181b;\n"
"border-radius: 10px;")
        self.databaseViewer.setFrameShape(QFrame.Shape.StyledPanel)
        self.databaseViewer.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_63 = QHBoxLayout(self.databaseViewer)
        self.horizontalLayout_63.setObjectName(u"horizontalLayout_63")

        self.horizontalLayout_23.addWidget(self.databaseViewer)

        self.inputFrame = QFrame(self.frame_78)
        self.inputFrame.setObjectName(u"inputFrame")
        self.inputFrame.setMinimumSize(QSize(600, 0))
        self.inputFrame.setMaximumSize(QSize(600, 16777215))
        self.inputFrame.setStyleSheet(u"QFrame#inputFrame{\n"
"background-color:#18181b;\n"
"border-radius: 10px;\n"
"}\n"
"\n"
"QLabel{\n"
"font-size: 13px;\n"
"font-family: Arial;\n"
"color: #d2d6dc;\n"
"font-weight: bold;\n"
"}\n"
"\n"
"\n"
"")
        self.inputFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.inputFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_84 = QVBoxLayout(self.inputFrame)
        self.verticalLayout_84.setSpacing(10)
        self.verticalLayout_84.setObjectName(u"verticalLayout_84")
        self.frame_80 = QFrame(self.inputFrame)
        self.frame_80.setObjectName(u"frame_80")
        self.frame_80.setStyleSheet(u"border-radius: 10px;")
        self.frame_80.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_80.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_68 = QHBoxLayout(self.frame_80)
        self.horizontalLayout_68.setObjectName(u"horizontalLayout_68")
        self.horizontalSpacer_85 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_68.addItem(self.horizontalSpacer_85)

        self.tableFrame = QFrame(self.frame_80)
        self.tableFrame.setObjectName(u"tableFrame")
        self.tableFrame.setStyleSheet(u"background-color:#18181b;\n"
"border-radius: 10px;")
        self.tableFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.tableFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_62 = QHBoxLayout(self.tableFrame)
        self.horizontalLayout_62.setObjectName(u"horizontalLayout_62")
        self.results_table_2 = QTableWidget(self.tableFrame)
        if (self.results_table_2.columnCount() < 5):
            self.results_table_2.setColumnCount(5)
        __qtablewidgetitem = QTableWidgetItem()
        self.results_table_2.setHorizontalHeaderItem(0, __qtablewidgetitem)
        __qtablewidgetitem1 = QTableWidgetItem()
        self.results_table_2.setHorizontalHeaderItem(1, __qtablewidgetitem1)
        __qtablewidgetitem2 = QTableWidgetItem()
        self.results_table_2.setHorizontalHeaderItem(2, __qtablewidgetitem2)
        __qtablewidgetitem3 = QTableWidgetItem()
        self.results_table_2.setHorizontalHeaderItem(3, __qtablewidgetitem3)
        __qtablewidgetitem4 = QTableWidgetItem()
        self.results_table_2.setHorizontalHeaderItem(4, __qtablewidgetitem4)
        self.results_table_2.setObjectName(u"results_table_2")
        self.results_table_2.setMinimumSize(QSize(1300, 0))
        self.results_table_2.setStyleSheet(u"QTableWidget {\n"
"                    background-color: #18181b;\n"
"                    color: white;\n"
"                    gridline-color: #446699;\n"
"                   border-radius: 10px;\n"
"					border:none;\n"
"                }\n"
"                QHeaderView::section {\n"
"                    background-color: #1e1e1e;\n"
"                    color: white;\n"
"                    padding: 5px;\n"
"                    border: 2px solid #0c9486;\n"
"					 border-radius:5px;\n"
"					\n"
"                }\n"
"                QTableWidget::item {\n"
"                    padding: 5px;\n"
"                }\n"
"                QTableWidget::item:selected {\n"
"                    background-color: #47a08e;\n"
"                }\n"
"\n"
"")
        self.results_table_2.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.results_table_2.setSortingEnabled(False)
        self.results_table_2.setWordWrap(True)
        self.results_table_2.setColumnCount(5)

        self.horizontalLayout_62.addWidget(self.results_table_2)


        self.horizontalLayout_68.addWidget(self.tableFrame)

        self.horizontalSpacer_86 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_68.addItem(self.horizontalSpacer_86)


        self.verticalLayout_84.addWidget(self.frame_80)

        self.frame_48 = QFrame(self.inputFrame)
        self.frame_48.setObjectName(u"frame_48")
        self.frame_48.setStyleSheet(u"QFrame{\n"
"background-color:#18181b;\n"
"border-radius: 10px;\n"
"}")
        self.frame_48.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_48.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_67 = QHBoxLayout(self.frame_48)
        self.horizontalLayout_67.setObjectName(u"horizontalLayout_67")
        self.horizontalSpacer_46 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_67.addItem(self.horizontalSpacer_46)

        self.frame_79 = QFrame(self.frame_48)
        self.frame_79.setObjectName(u"frame_79")
        self.frame_79.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_79.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_86 = QVBoxLayout(self.frame_79)
        self.verticalLayout_86.setObjectName(u"verticalLayout_86")
        self.widget_5 = QWidget(self.frame_79)
        self.widget_5.setObjectName(u"widget_5")
        self.widget_5.setMinimumSize(QSize(0, 50))
        self.widget_5.setMaximumSize(QSize(500, 16777215))
        self.widget_5.setStyleSheet(u"QWidget{\n"
"	background-color: #374151;\n"
"	border-radius: 10px;\n"
"}\n"
"\n"
"QLineEdit{\n"
"	background-color: #1C1C1C;\n"
"	border-radius: 15px;\n"
"	border:1px solid #446699;\n"
"	color: white;\n"
"	padding:5px;\n"
"	font-size:16px;\n"
"}\n"
"\n"
"QLabel{\n"
"	font-size:15px;\n"
"}\n"
"\n"
"QComboBox {\n"
"    border:1px solid #446699;\n"
"    border-radius: 15px;\n"
"    padding: 4px 10px;\n"
"    background-color: #1C1C1C;\n"
"    color: #EEEEEE;\n"
"    min-width: 6em;\n"
"	font-size: 16px;\n"
"}\n"
"\n"
"QComboBox::drop-down {\n"
"    border: none;\n"
"    background: transparent;\n"
"	width: 20px;\n"
"	margin-right: 8px;\n"
"}\n"
"\n"
"QComboBox::down-arrow {\n"
"    image: url(\"assets/down-arrow.png\");  /* You can use a custom arrow image */\n"
"    width: 24px;\n"
"    height: 24px;\n"
"}\n"
"\n"
"\n"
"/* Dropdown list styling */\n"
"QComboBox QAbstractItemView {\n"
"    border: none;\n"
"	margin: 5px;\n"
"    border-radius: 10px;\n"
"    padding: 5px 0px;\n"
"    background-color: #2a2a2a;\n"
""
                        "    color: white;\n"
"    selection-background-color: #3a3a3a;  /* Darker selection color */\n"
"    outline: 0px;  /* Remove focus border */\n"
"}\n"
"\n"
"/* Individual item styling */\n"
"QComboBox QAbstractItemView::item {\n"
"    padding: 8px 12px;  /* Vertical and horizontal padding for items */\n"
"    border: none;\n"
"    min-height: 24px;  /* Minimum height for items */\n"
"}\n"
"\n"
"/* Hover state for items */\n"
"QComboBox QAbstractItemView::item:hover {\n"
"    background-color: #3d3d3d;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"/* Selected item */\n"
"QComboBox QAbstractItemView::item:selected {\n"
"    background-color: #3d3d3d;\n"
"}\n"
"\n"
"/* Remove the default focus rectangle */\n"
"QComboBox:focus {\n"
"    border: none;\n"
"    outline: none;\n"
"}\n"
"\n"
"/* Hover state for the combobox */\n"
"QComboBox:hover {\n"
"    background-color: #252525;\n"
"}\n"
"\n"
"/* Scrollbar styling */\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-rad"
                        "ius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}\n"
"\n"
"/* Horizontal bar */\n"
"QScrollBar:horizontal {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:horizontal,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:horizontal,\n"
"QScrollB"
                        "ar::sub-page:horizontal {\n"
"    background: none;\n"
"}\n"
"")
        self.horizontalLayout_64 = QHBoxLayout(self.widget_5)
        self.horizontalLayout_64.setObjectName(u"horizontalLayout_64")
        self.lblCatalystName = QLabel(self.widget_5)
        self.lblCatalystName.setObjectName(u"lblCatalystName")
        self.lblCatalystName.setMinimumSize(QSize(200, 0))
        self.lblCatalystName.setMaximumSize(QSize(200, 16777215))

        self.horizontalLayout_64.addWidget(self.lblCatalystName)

        self.catalyst_name = QLineEdit(self.widget_5)
        self.catalyst_name.setObjectName(u"catalyst_name")
        self.catalyst_name.setMinimumSize(QSize(170, 35))
        self.catalyst_name.setMaximumSize(QSize(270, 16777215))
        self.catalyst_name.setStyleSheet(u"")
        self.catalyst_name.setFrame(False)

        self.horizontalLayout_64.addWidget(self.catalyst_name)


        self.verticalLayout_86.addWidget(self.widget_5)

        self.widget_6 = QWidget(self.frame_79)
        self.widget_6.setObjectName(u"widget_6")
        self.widget_6.setMinimumSize(QSize(0, 50))
        self.widget_6.setMaximumSize(QSize(500, 16777215))
        self.widget_6.setStyleSheet(u"QWidget{\n"
"	background-color: #374151;\n"
"	border-radius: 10px;\n"
"}\n"
"\n"
"QLineEdit{\n"
"	background-color: #1C1C1C;\n"
"	border-radius: 15px;\n"
"	border:1px solid #446699;\n"
"	color: white;\n"
"	padding:5px;\n"
"	font-size:16px;\n"
"}\n"
"\n"
"\n"
"QLabel{\n"
"	font-size:15px;\n"
"}\n"
"\n"
"QComboBox, QDoubleSpinBox{\n"
"    border:1px solid #446699;\n"
"    border-radius: 15px;\n"
"    padding: 4px 10px;\n"
"    background-color: #1C1C1C;\n"
"    color: #EEEEEE;\n"
"    min-width: 6em;\n"
"	font-size: 16px;\n"
"}\n"
"\n"
"QComboBox::drop-down {\n"
"    border: none;\n"
"    background: transparent;\n"
"	width: 20px;\n"
"	margin-right: 8px;\n"
"}\n"
"\n"
"QComboBox::down-arrow {\n"
"    image: url(\"assets/down-arrow.png\");  /* You can use a custom arrow image */\n"
"    width: 24px;\n"
"    height: 24px;\n"
"}\n"
"\n"
"\n"
"/* Dropdown list styling */\n"
"QComboBox QAbstractItemView {\n"
"    border: none;\n"
"	margin: 5px;\n"
"    border-radius: 10px;\n"
"    padding: 5px 0px;\n"
"    background"
                        "-color: #2a2a2a;\n"
"    color: white;\n"
"    selection-background-color: #3a3a3a;  /* Darker selection color */\n"
"    outline: 0px;  /* Remove focus border */\n"
"}\n"
"\n"
"/* Individual item styling */\n"
"QComboBox QAbstractItemView::item {\n"
"    padding: 8px 12px;  /* Vertical and horizontal padding for items */\n"
"    border: none;\n"
"    min-height: 24px;  /* Minimum height for items */\n"
"}\n"
"\n"
"/* Hover state for items */\n"
"QComboBox QAbstractItemView::item:hover {\n"
"    background-color: #3d3d3d;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"/* Selected item */\n"
"QComboBox QAbstractItemView::item:selected {\n"
"    background-color: #3d3d3d;\n"
"}\n"
"\n"
"/* Remove the default focus rectangle */\n"
"QComboBox:focus {\n"
"    border: none;\n"
"    outline: none;\n"
"}\n"
"\n"
"/* Hover state for the combobox */\n"
"QComboBox:hover {\n"
"    background-color: #252525;\n"
"}\n"
"\n"
"/* Scrollbar styling */\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin:"
                        " 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}\n"
"\n"
"/* Horizontal bar */\n"
"QScrollBar:horizontal {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:horizontal,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:hor"
                        "izontal,\n"
"QScrollBar::sub-page:horizontal {\n"
"    background: none;\n"
"}\n"
"")
        self.horizontalLayout_65 = QHBoxLayout(self.widget_6)
        self.horizontalLayout_65.setObjectName(u"horizontalLayout_65")
        self.lblTankBottomTemperature = QLabel(self.widget_6)
        self.lblTankBottomTemperature.setObjectName(u"lblTankBottomTemperature")
        self.lblTankBottomTemperature.setMinimumSize(QSize(200, 0))
        self.lblTankBottomTemperature.setMaximumSize(QSize(200, 16777215))

        self.horizontalLayout_65.addWidget(self.lblTankBottomTemperature)

        self.tank_temp = QDoubleSpinBox(self.widget_6)
        self.tank_temp.setObjectName(u"tank_temp")
        self.tank_temp.setMinimumSize(QSize(148, 35))
        self.tank_temp.setMaximumSize(QSize(270, 16777215))
        self.tank_temp.setStyleSheet(u"")
        self.tank_temp.setFrame(False)
        self.tank_temp.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.tank_temp.setDecimals(6)
        self.tank_temp.setSingleStep(0.000000000000000)

        self.horizontalLayout_65.addWidget(self.tank_temp)


        self.verticalLayout_86.addWidget(self.widget_6)

        self.widget_3 = QWidget(self.frame_79)
        self.widget_3.setObjectName(u"widget_3")
        self.widget_3.setMinimumSize(QSize(0, 50))
        self.widget_3.setMaximumSize(QSize(500, 16777215))
        self.widget_3.setStyleSheet(u"QWidget{\n"
"	background-color: #374151;\n"
"	border-radius: 10px;\n"
"}\n"
"\n"
"QLineEdit{\n"
"	background-color: #1C1C1C;\n"
"	border-radius: 15px;\n"
"	border:1px solid #446699;\n"
"	color: white;\n"
"	padding:5px;\n"
"	font-size:16px;\n"
"}\n"
"\n"
"QLabel{\n"
"	font-size:15px;\n"
"}\n"
"\n"
"QComboBox {\n"
"    border:1px solid #446699;\n"
"    border-radius: 15px;\n"
"    padding: 4px 10px;\n"
"    background-color: #1C1C1C;\n"
"    color: #EEEEEE;\n"
"    min-width: 6em;\n"
"	font-size: 16px;\n"
"}\n"
"\n"
"QComboBox::drop-down {\n"
"    border: none;\n"
"    background: transparent;\n"
"	width: 20px;\n"
"	margin-right: 8px;\n"
"}\n"
"\n"
"QComboBox::down-arrow {\n"
"    image: url(\"assets/down-arrow.png\");  /* You can use a custom arrow image */\n"
"    width: 24px;\n"
"    height: 24px;\n"
"}\n"
"\n"
"\n"
"/* Dropdown list styling */\n"
"QComboBox QAbstractItemView {\n"
"    border: none;\n"
"	margin: 5px;\n"
"    border-radius: 10px;\n"
"    padding: 5px 0px;\n"
"    background-color: #2a2a2a;\n"
""
                        "    color: white;\n"
"    selection-background-color: #3a3a3a;  /* Darker selection color */\n"
"    outline: 0px;  /* Remove focus border */\n"
"}\n"
"\n"
"/* Individual item styling */\n"
"QComboBox QAbstractItemView::item {\n"
"    padding: 8px 12px;  /* Vertical and horizontal padding for items */\n"
"    border: none;\n"
"    min-height: 24px;  /* Minimum height for items */\n"
"}\n"
"\n"
"/* Hover state for items */\n"
"QComboBox QAbstractItemView::item:hover {\n"
"    background-color: #3d3d3d;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"/* Selected item */\n"
"QComboBox QAbstractItemView::item:selected {\n"
"    background-color: #3d3d3d;\n"
"}\n"
"\n"
"/* Remove the default focus rectangle */\n"
"QComboBox:focus {\n"
"    border: none;\n"
"    outline: none;\n"
"}\n"
"\n"
"/* Hover state for the combobox */\n"
"QComboBox:hover {\n"
"    background-color: #252525;\n"
"}\n"
"\n"
"/* Scrollbar styling */\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-rad"
                        "ius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}\n"
"\n"
"/* Horizontal bar */\n"
"QScrollBar:horizontal {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:horizontal,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:horizontal,\n"
"QScrollB"
                        "ar::sub-page:horizontal {\n"
"    background: none;\n"
"}\n"
"")
        self.horizontalLayout_33 = QHBoxLayout(self.widget_3)
        self.horizontalLayout_33.setObjectName(u"horizontalLayout_33")
        self.lblTestNumber_2 = QLabel(self.widget_3)
        self.lblTestNumber_2.setObjectName(u"lblTestNumber_2")
        self.lblTestNumber_2.setMinimumSize(QSize(200, 0))
        self.lblTestNumber_2.setMaximumSize(QSize(200, 16777215))

        self.horizontalLayout_33.addWidget(self.lblTestNumber_2)

        self.test_no = QLineEdit(self.widget_3)
        self.test_no.setObjectName(u"test_no")
        self.test_no.setMinimumSize(QSize(170, 35))
        self.test_no.setMaximumSize(QSize(270, 16777215))
        self.test_no.setStyleSheet(u"")
        self.test_no.setFrame(False)

        self.horizontalLayout_33.addWidget(self.test_no)


        self.verticalLayout_86.addWidget(self.widget_3)

        self.widget_4 = QWidget(self.frame_79)
        self.widget_4.setObjectName(u"widget_4")
        self.widget_4.setMinimumSize(QSize(0, 50))
        self.widget_4.setMaximumSize(QSize(500, 16777215))
        self.widget_4.setStyleSheet(u"QWidget{\n"
"	background-color: #374151;\n"
"	border-radius: 10px;\n"
"}\n"
"\n"
"QLineEdit{\n"
"	background-color: #1C1C1C;\n"
"	border-radius: 15px;\n"
"	border:1px solid #446699;\n"
"	color: white;\n"
"	padding:5px;\n"
"	font-size:16px;\n"
"}\n"
"\n"
"\n"
"QLabel{\n"
"	font-size:15px;\n"
"}\n"
"\n"
"QComboBox, QDoubleSpinBox{\n"
"    border:1px solid #446699;\n"
"    border-radius: 15px;\n"
"    padding: 4px 10px;\n"
"    background-color: #1C1C1C;\n"
"    color: #EEEEEE;\n"
"    min-width: 6em;\n"
"	font-size: 16px;\n"
"}\n"
"\n"
"QComboBox::drop-down {\n"
"    border: none;\n"
"    background: transparent;\n"
"	width: 20px;\n"
"	margin-right: 8px;\n"
"}\n"
"\n"
"QComboBox::down-arrow {\n"
"    image: url(\"assets/down-arrow.png\");  /* You can use a custom arrow image */\n"
"    width: 24px;\n"
"    height: 24px;\n"
"}\n"
"\n"
"\n"
"/* Dropdown list styling */\n"
"QComboBox QAbstractItemView {\n"
"    border: none;\n"
"	margin: 5px;\n"
"    border-radius: 10px;\n"
"    padding: 5px 0px;\n"
"    background"
                        "-color: #2a2a2a;\n"
"    color: white;\n"
"    selection-background-color: #3a3a3a;  /* Darker selection color */\n"
"    outline: 0px;  /* Remove focus border */\n"
"}\n"
"\n"
"/* Individual item styling */\n"
"QComboBox QAbstractItemView::item {\n"
"    padding: 8px 12px;  /* Vertical and horizontal padding for items */\n"
"    border: none;\n"
"    min-height: 24px;  /* Minimum height for items */\n"
"}\n"
"\n"
"/* Hover state for items */\n"
"QComboBox QAbstractItemView::item:hover {\n"
"    background-color: #3d3d3d;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"/* Selected item */\n"
"QComboBox QAbstractItemView::item:selected {\n"
"    background-color: #3d3d3d;\n"
"}\n"
"\n"
"/* Remove the default focus rectangle */\n"
"QComboBox:focus {\n"
"    border: none;\n"
"    outline: none;\n"
"}\n"
"\n"
"/* Hover state for the combobox */\n"
"QComboBox:hover {\n"
"    background-color: #252525;\n"
"}\n"
"\n"
"/* Scrollbar styling */\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin:"
                        " 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}\n"
"\n"
"/* Horizontal bar */\n"
"QScrollBar:horizontal {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:horizontal,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:hor"
                        "izontal,\n"
"QScrollBar::sub-page:horizontal {\n"
"    background: none;\n"
"}\n"
"")
        self.horizontalLayout_60 = QHBoxLayout(self.widget_4)
        self.horizontalLayout_60.setSpacing(6)
        self.horizontalLayout_60.setObjectName(u"horizontalLayout_60")
        self.lblPropRI = QLabel(self.widget_4)
        self.lblPropRI.setObjectName(u"lblPropRI")
        self.lblPropRI.setMinimumSize(QSize(200, 0))
        self.lblPropRI.setMaximumSize(QSize(200, 16777215))

        self.horizontalLayout_60.addWidget(self.lblPropRI)

        self.propellant_ri = CustomDoubleSpinBox(self.widget_4)
        self.propellant_ri.setObjectName(u"propellant_ri")
        self.propellant_ri.setMinimumSize(QSize(148, 35))
        self.propellant_ri.setMaximumSize(QSize(270, 16777215))
        self.propellant_ri.setStyleSheet(u"")
        self.propellant_ri.setFrame(False)
        self.propellant_ri.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.propellant_ri.setDecimals(6)
        self.propellant_ri.setMaximum(1000.000000000000000)
        self.propellant_ri.setSingleStep(0.000000000000000)

        self.horizontalLayout_60.addWidget(self.propellant_ri)


        self.verticalLayout_86.addWidget(self.widget_4)


        self.horizontalLayout_67.addWidget(self.frame_79)

        self.horizontalSpacer_84 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_67.addItem(self.horizontalSpacer_84)


        self.verticalLayout_84.addWidget(self.frame_48)


        self.horizontalLayout_23.addWidget(self.inputFrame)


        self.verticalLayout_76.addWidget(self.frame_78)

        self.frame_26 = QFrame(self.frame_76)
        self.frame_26.setObjectName(u"frame_26")
        self.frame_26.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_26.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_61 = QHBoxLayout(self.frame_26)
        self.horizontalLayout_61.setObjectName(u"horizontalLayout_61")

        self.verticalLayout_76.addWidget(self.frame_26)


        self.verticalLayout_61.addWidget(self.frame_76)

        self.tabWidget.addTab(self.filterTab, "")

        self.verticalLayout_85.addWidget(self.tabWidget)

        self.bottomFrame = QFrame(self.centralwidget)
        self.bottomFrame.setObjectName(u"bottomFrame")
        self.bottomFrame.setMinimumSize(QSize(0, 40))
        self.bottomFrame.setMaximumSize(QSize(16777215, 40))
        self.bottomFrame.setFrameShape(QFrame.Shape.NoFrame)
        self.bottomFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.gridLayout_10 = QGridLayout(self.bottomFrame)
        self.gridLayout_10.setObjectName(u"gridLayout_10")
        self.gridLayout_10.setHorizontalSpacing(15)
        self.gridLayout_10.setContentsMargins(6, 0, 6, 0)
        self.logFrame = QFrame(self.bottomFrame)
        self.logFrame.setObjectName(u"logFrame")
        self.logFrame.setMinimumSize(QSize(830, 38))
        self.logFrame.setMaximumSize(QSize(16777215, 16777215))
        self.logFrame.setStyleSheet(u"background-color: #2b2b2c;\n"
"border-radius:10px;\n"
"\n"
"\n"
"")
        self.logFrame.setFrameShape(QFrame.Shape.StyledPanel)
        self.logFrame.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_11 = QHBoxLayout(self.logFrame)
        self.horizontalLayout_11.setSpacing(8)
        self.horizontalLayout_11.setObjectName(u"horizontalLayout_11")
        self.horizontalLayout_11.setContentsMargins(8, 5, 8, 5)
        self.lblLog = QLabel(self.logFrame)
        self.lblLog.setObjectName(u"lblLog")
        self.lblLog.setMinimumSize(QSize(0, 25))
        self.lblLog.setMaximumSize(QSize(70, 25))
        font7 = QFont()
        font7.setBold(True)
        font7.setItalic(False)
        self.lblLog.setFont(font7)
        self.lblLog.setStyleSheet(u"background-color: #c1ffff;\n"
"border-radius: 10px;\n"
"font-size:12px;\n"
"color:black;\n"
"font: bold;\n"
"\n"
"")
        self.lblLog.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_11.addWidget(self.lblLog)

        self.lblLogInfo = QLabel(self.logFrame)
        self.lblLogInfo.setObjectName(u"lblLogInfo")
        self.lblLogInfo.setStyleSheet(u"color:#18ab81;\n"
"font-size:14px;\n"
"background-color: #18181b;\n"
"border-radius:10px;\n"
"border:1px solid #446699;")
        self.lblLogInfo.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.lblLogInfo.setWordWrap(True)

        self.horizontalLayout_11.addWidget(self.lblLogInfo)


        self.gridLayout_10.addWidget(self.logFrame, 0, 1, 1, 1)

        self.showReport = QPushButton(self.bottomFrame)
        self.showReport.setObjectName(u"showReport")
        self.showReport.setMinimumSize(QSize(150, 30))
        self.showReport.setMaximumSize(QSize(150, 30))
        self.showReport.setStyleSheet(u"QPushButton{\n"
"	background-color:rgba(86, 175, 95, 0.7);\n"
"	border-radius:6px;\n"
"	padding:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	background-color:#b089a1;\n"
"}\n"
"\n"
"QPushButton:pressed{\n"
"	background-color:b03781;\n"
"}")

        self.gridLayout_10.addWidget(self.showReport, 0, 2, 1, 1)

        self.saveDataToDatabase = QPushButton(self.bottomFrame)
        self.saveDataToDatabase.setObjectName(u"saveDataToDatabase")
        self.saveDataToDatabase.setMinimumSize(QSize(150, 30))
        self.saveDataToDatabase.setMaximumSize(QSize(150, 30))
        self.saveDataToDatabase.setStyleSheet(u"QPushButton{\n"
"	background-color:rgb(255, 52, 20);\n"
"	border-radius:6px;\n"
"	padding:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	background-color:#b089a1;\n"
"}\n"
"\n"
"QPushButton:pressed{\n"
"	background-color:b03781;\n"
"}")

        self.gridLayout_10.addWidget(self.saveDataToDatabase, 0, 3, 1, 1)

        self.label_7 = QLabel(self.bottomFrame)
        self.label_7.setObjectName(u"label_7")
        self.label_7.setMaximumSize(QSize(100, 20))
        self.label_7.setStyleSheet(u"background-color:#872341;\n"
"border-radius:10px;\n"
"border:1px solid #C890A7;")
        self.label_7.setFrameShape(QFrame.Shape.NoFrame)
        self.label_7.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout_10.addWidget(self.label_7, 0, 0, 1, 1)


        self.verticalLayout_85.addWidget(self.bottomFrame)

        VaprIdexMainWindow.setCentralWidget(self.centralwidget)

        self.retranslateUi(VaprIdexMainWindow)

        self.tabWidget.setCurrentIndex(0)
        self.contentStack.setCurrentIndex(0)
        self.cycleTabWidgetPressureSensor.setCurrentIndex(0)
        self.cycleTabWidget.setCurrentIndex(0)
        self.tabWidgetPlotSettings.setCurrentIndex(1)


        QMetaObject.connectSlotsByName(VaprIdexMainWindow)
    # setupUi

    def retranslateUi(self, VaprIdexMainWindow):
        VaprIdexMainWindow.setWindowTitle(QCoreApplication.translate("VaprIdexMainWindow", u"VAPR-iDEX Thruster Analysis System", None))
        self.projectName.setText(QCoreApplication.translate("VaprIdexMainWindow", u"VAPR-iDEX", None))
        self.lblCurentSection.setText("")
        self.lblAim.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Aim: ", None))
        self.lblPropellant.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Propellant", None))
        self.lblCatalyst.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Catalyst", None))
        self.label.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Test No.:", None))
        self.lblTestNumber.setText("")
        self.subSecLoadData.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Load Data", None))
        self.pressure_icon.setText("")
        self.btnPressureDataLoad.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Pressure", None))
        self.temp_icon.setText("")
        self.btnTempDataLoad.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Temperature", None))
        self.subSecBasicInfo.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Basic Information", None))
        self.subLblAim.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Aim of the Test", None))
        self.subLblProp.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Propellant", None))
        self.subLblPropRI.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Propellant RI Before Test", None))
        self.subLblCat.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Catalyst", None))
        self.subLblTestNo.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Test Number", None))
        self.subLblTestDate.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Test conducted date", None))
        self.subLnEdtTestDate.setDisplayFormat(QCoreApplication.translate("VaprIdexMainWindow", u"dd/MM/yyyy", None))
        self.subSecSystmSpeci.setText(QCoreApplication.translate("VaprIdexMainWindow", u"System Specification", None))
        self.subLblChmbrNo_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Chamber number", None))
        self.subLblChmbrMat.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Chamber material", None))
        self.subLblChmbrDept.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Chamber depth (mm)", None))
        self.subLblChmbrDia.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Chamber diameter (mm)", None))
        self.subLblNozlThrtDime.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Nozzle throat dimension (mm)", None))
        self.subLblRetainerPltOrfcDia.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Retainer plate orifice diameter (mm)", None))
        self.subLblMeshMat.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Mesh material", None))
        self.subLblMeshSize.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Mesh size", None))
        self.label_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Internal", None))
        self.label_3.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Outer", None))
        self.subSecPropSpec.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Propellant Specification", None))
        self.subLblTypeOfProp.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Type of Propellant", None))
        self.subLblConcBefTest.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Concentration (Before testing) (%)", None))
        self.subLblStability.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Stability (Old/New -MIL)", None))
        self.subLblWghtOfPropBefTest.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Weight of Propellant (before the test) (g)", None))
        self.subLblWghtOfPropAftTest.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Weight of Propellant (after the test) (g)", None))
        self.subSecCatSpec.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Catalyst Specification", None))
        self.subLblCatType.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Catalyst Type", None))
        self.subLblCatGrade.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Grade/Composition", None))
        self.subLblCatSize.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Size of Catalyst (mm)", None))
        self.subLblCatWghtBefTest.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Weight of the Catalyst (before the test) (g)", None))
        self.subSecCompDet.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Component details", None))
        self.Vac_Chamb_Pressure_Sensr_type.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Pressure sensor type", None))
        self.Vac_Chamb__Pressure_Snsr_No_Slope_Eqn.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Pressure Sensor Number & Slope equation", None))
        self.Vac_Chamb_Pressure_Snsr_range.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Pressure sensor range", None))
        self.Vac_Chamb__Pressure_Snsr_IO.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Pressure sensor input and output", None))
        self.cycleTabWidgetPressureSensor.setTabText(self.cycleTabWidgetPressureSensor.indexOf(self.cycle1_2), QCoreApplication.translate("VaprIdexMainWindow", u"Vacuum Chamber Pressure", None))
        self.Prop_Tank_Pressure_Sensr_type.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Pressure sensor type", None))
        self.Prop_Tank__Pressure_Snsr_No_Slope_Eqn.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Pressure Sensor Number & Slope equation", None))
        self.Prop_Tank_Pressure_Snsr_range.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Pressure sensor range", None))
        self.Prop_Tank__Pressure_Snsr_IO.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Pressure sensor input and output", None))
        self.cycleTabWidgetPressureSensor.setTabText(self.cycleTabWidgetPressureSensor.indexOf(self.cycle2_2), QCoreApplication.translate("VaprIdexMainWindow", u"Propellant Tank Pressure", None))
        self.Thruster_Pressure_Sensr_type.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Pressure sensor type", None))
        self.Thruster_Pressure_Snsr_No_Slope_Eqn.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Pressure Sensor Number & Slope equation", None))
        self.Thruster_Pressure_Snsr_range.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Pressure sensor range", None))
        self.Thruster_Pressure_Snsr_IO.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Pressure sensor input and output", None))
        self.cycleTabWidgetPressureSensor.setTabText(self.cycleTabWidgetPressureSensor.indexOf(self.cycle3_2), QCoreApplication.translate("VaprIdexMainWindow", u"Thruster Pressure", None))
        self.subLblHtrType.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Heater type", None))
        self.subLblHtrInpPower.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Heater input power (W)", None))
        self.subLnEdtHtrType_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Silicon Patch heater", None))
        self.subSectnHderTestDet.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Test Details", None))
        self.subLblPropTnkHtrCtOffTemp.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Propellant tank heater cut-off temperature (\u00b0C)", None))
        self.subLblPropTnkHtrRstTmp.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Propellant tank heater reset temperature (\u00b0C)", None))
        self.subLblTestProc.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Test procedure", None))
        self.subLblTestProcValue.setPlainText(QCoreApplication.translate("VaprIdexMainWindow", u"Switch on the heaters on the propellant tank. Let the heater be turned on till the cut-off temperature of the tank bottom is reached. Switch-off the heater when the cut-off temperature is reached. Restart the heater when the temperature falls below the reset temperature (2\u00b0C below the cut-off temperature). Repeat this process for 4 cycles. Each cycle is defined as a tank reaching its cut-off temperature and then re-heating.", None))
        self.subSecHtrInfo.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Heater Information", None))
        self.subLblHtrTyp.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Heater Type", None))
        self.subLblHtrInp.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Heater input", None))
        self.subLblHtrCtOfTemp.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Heater cut-off temperature (\u00b0C)", None))
        self.subLblHtrRstTemp.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Heater reset temperature (\u00b0C)", None))
        self.subLblHtrInpVoltage.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Voltage", None))
        self.subLblHtrInpCurrent.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Current", None))
        self.subLblHtrInpWattage.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Wattage", None))
        self.subSecHtrCyc.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Heater Cycles", None))
        self.cyc1SubLblHtrSwtOnTime.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Heater Switch on time [HH:MM] (24H)", None))
        self.cyc1SubLblHtrSwtONCorspgTankPressure.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Corresponding Tank Pressure (Bar) ", None))
        self.cyc1SubLblHtrSwtONCorspgThrusterPressure.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Corresponding Thruster Pressure (Bar) ", None))
        self.cyc1SubLblHtrSwtOffTime.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Heater Switch off time [HH:MM] (24H)", None))
        self.cyc1SubLblHtrSwtOFFCorspgTankPressure.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Corresponding Tank Pressure (Bar)", None))
        self.cyc1SubLblHtrSwtOFFCorspgThrusterPressure.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Corresponding Thruster Pressure (Bar)", None))
        self.cyc1SubLblMaxTemp.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Tank Bottom Temperature (\u00b0C)", None))
        self.cyc1SubLblLoc.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Location", None))
        self.cyc1SubLblCorrespgTemp.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Corresponding Max temperature (\u00b0C) in the system", None))
        self.cyc1SubLnEdtHtrSwtOnTime.setDisplayFormat(QCoreApplication.translate("VaprIdexMainWindow", u"hh:mm", None))
        self.cyc1SubLnEdtHtrSwtOffTime.setDisplayFormat(QCoreApplication.translate("VaprIdexMainWindow", u"hh:mm", None))
        self.cycleTabWidget.setTabText(self.cycleTabWidget.indexOf(self.cycle1), QCoreApplication.translate("VaprIdexMainWindow", u"Cycle-1", None))
        self.cyc2SubLblHtrSwtOnTime.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Heater Switch on time [HH:MM] (24H)", None))
        self.cyc2SubLblHtrSwtONCorspgTankPressure.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Corresponding Tank Pressure (Bar) ", None))
        self.cyc2SubLblHtrSwtONCorspgThrusterPressure.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Corresponding Thruster Pressure (Bar) ", None))
        self.cyc2SubLblHtrSwtOffTime.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Heater Switch off time [HH:MM] (24H)", None))
        self.cyc2SubLblHtrSwtOFFCorspgTankPressure.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Corresponding Tank Pressure (Bar) ", None))
        self.cyc2SubLblHtrSwtOFFCorspgThrusterPressure.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Corresponding Thruster Pressure (Bar) ", None))
        self.cyc2SubLblMaxTemp.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Tank Bottom Temperature (\u00b0C)", None))
        self.cyc2SubLblLoc.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Location", None))
        self.cyc2SubLblCorrespgTemp.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Corresponding tank bottom temperature (\u00b0C)", None))
        self.cyc2SubLnEdtHtrSwtOnTime.setDisplayFormat(QCoreApplication.translate("VaprIdexMainWindow", u"hh:mm", None))
        self.cyc2SubLnEdtHtrSwtOffTime.setDisplayFormat(QCoreApplication.translate("VaprIdexMainWindow", u"hh:mm", None))
        self.cycleTabWidget.setTabText(self.cycleTabWidget.indexOf(self.cycle2), QCoreApplication.translate("VaprIdexMainWindow", u"Cycle-2", None))
        self.cyc3SubLblHtrSwtOnTime.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Heater Switch on time [HH:MM] (24H)", None))
        self.cyc3SubLblHtrSwtONCorspgTankPressure.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Corresponding Tank Pressure (Bar) ", None))
        self.cyc3SubLblHtrSwtONCorspgThrusterPressure.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Corresponding Thrustser Pressure (Bar) ", None))
        self.cyc3SubLblHtrSwtOffTime.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Heater Switch off time [HH:MM] (24H)", None))
        self.cyc3SubLblHtrSwtOFFCorspgTankPressure.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Corresponding Tank Pressure (Bar) ", None))
        self.cyc3SubLblHtrSwtOFFCorspgThrusterPressure.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Corresponding Thruster Pressure (Bar) ", None))
        self.cyc3SubLblMaxTemp.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Tank Bottom Temperature (\u00b0C)", None))
        self.cyc3SubLblLoc.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Location", None))
        self.cyc3SubLblCorrespgTemp.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Corresponding tank bottom temperature (\u00b0C)", None))
        self.cyc3SubLnEdtHtrSwtOnTime.setDisplayFormat(QCoreApplication.translate("VaprIdexMainWindow", u"hh:mm", None))
        self.cyc3SubLnEdtHtrSwtOffTime.setDisplayFormat(QCoreApplication.translate("VaprIdexMainWindow", u"hh:mm", None))
        self.cycleTabWidget.setTabText(self.cycleTabWidget.indexOf(self.cycle3), QCoreApplication.translate("VaprIdexMainWindow", u"Cycle-3", None))
        self.cyc4SubLblHtrSwtOnTime.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Heater Switch on time [HH:MM] (24H)", None))
        self.cyc4SubLblHtrSwtONCorspgTankPressure.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Corresponding Tank Pressure (Bar) ", None))
        self.cyc4SubLblHtrSwtONCorspgThrusterPressure.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Corresponding Thruster Pressure (Bar) ", None))
        self.cyc4SubLblHtrSwtOffTime.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Heater Switch off time [HH:MM] (24H)", None))
        self.cyc4SubLblHtrSwtOFFCorspgTankPressure.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Corresponding Tank Pressure (Bar) ", None))
        self.cyc4SubLblHtrSwtOFFCorspgThrusterPressure.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Corresponding Thruster Pressure (Bar) ", None))
        self.cyc4SubLblMaxTemp.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Tank Bottom Temperature (\u00b0C)", None))
        self.cyc4SubLblLoc.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Location", None))
        self.cyc4SubLblCorrespgTemp.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Corresponding tank bottom temperature (\u00b0C)", None))
        self.cyc4SubLnEdtHtrSwtOnTime.setDisplayFormat(QCoreApplication.translate("VaprIdexMainWindow", u"hh:mm", None))
        self.cyc4SubLnEdtHtrSwtOffTime.setDisplayFormat(QCoreApplication.translate("VaprIdexMainWindow", u"hh:mm", None))
        self.cycleTabWidget.setTabText(self.cycleTabWidget.indexOf(self.cycle4), QCoreApplication.translate("VaprIdexMainWindow", u"Cycle-4", None))
        self.btnCycleBack.setText("")
        self.btnCycleNext.setText("")
        self.subLblChmbrNo_3.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Chamber number", None))
        self.subLblChmbrLen_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Chamber Length (mm)", None))
        self.subLblChmbrIntDia_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Chamber internal Diameter (mm)", None))
        self.subLblChmbrExtDia_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Chamber external Diameter (mm)", None))
        self.subLblMeshCond_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Mesh Condition", None))
        self.subLblRetainerPltCond_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Retainer Plate condition", None))
        self.subLblCatPhtoBfrFirg_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Catalyst photo before firing", None))
        self.subLblCatPhtoAftFirg_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Catalyst photo after firing", None))
        self.subLblPropPhtoBfr_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Propellant photo before firing", None))
        self.subLblPropPhtoAft_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Propellant photo after firing", None))
        self.subLnEdtCatPhtoBfr_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"No Photo Selected", None))
        self.btnCatPhotoBef_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Select Photo", None))
        self.lblCatPhotoPrevBef_2.setText("")
        self.subLnEdtCatPhtoAft_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"No Photo Selected", None))
        self.btnCatPhotoAft_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Select Photo", None))
        self.lblCatPhotoPrevAft_2.setText("")
        self.subLnEdtPropPhtoBfr_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"No Photo Selected", None))
        self.btnPropPhotoBef_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Select Photo", None))
        self.lblPropPhotoPrevBef_2.setText("")
        self.subLnEdtPropPhtoAft_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"No Photo Selected", None))
        self.btnPropPhotoAft_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Select Photo", None))
        self.lblPropPhotoPrevAft_2.setText("")
        self.lineEdit.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Observations", None))
        self.subSecPostTestObs.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Post Testing Observations", None))
        self.subSecCatPstAn.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Catalyst post-analysis", None))
        self.subLblCatDet.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Catalyst details/specification", None))
        self.subLblCatColBfr.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Catalyst color before", None))
        self.subLblCatColAft.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Catalyst color after", None))
        self.subLblCatWghtFild.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Catalyst weight filled (g)", None))
        self.subLblCatWghtRecvrd.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Catalyst weight recovered (g)", None))
        self.subLblCatLosPerc.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Catalyst change percentage (%)", None))
        self.subSecPropPstAn.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Propellant post-analysis", None))
        self.btnUpdateTable.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Update table", None))
        self.subLblRITable.setText(QCoreApplication.translate("VaprIdexMainWindow", u"RI", None))
        self.subLblConcTable.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Concentration (%)", None))
        self.subLblPropBefTable.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Propellant (Before test)", None))
        self.subLblPropBefRITable.setText("")
        self.subLblPropBefConcTable.setText("")
        self.subLblPropAftTable.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Propellant (After test)", None))
        self.subLblPropAftRITable.setText("")
        self.subLblPropAftConcTable.setText("")
        self.subLnEdtPropWghtFild_2.setPrefix("")
        self.subLnEdtPropWghtFild_2.setSuffix("")
        self.subLblPropDet_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Propellant details/specification", None))
        self.subLblPropColBef_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Propellant color before", None))
        self.subLblPropColAft_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Propellant color after", None))
        self.subLblPropWghtFild_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Propellant weight filled (g)", None))
        self.subLblPropWghtRecvrd_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Propellant weight recovered (g)", None))
        self.subLblPropUsedPerc_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Propellant used percentage (%)", None))
        self.subLblPropRIBefFirg_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Propellant RI (before firing)", None))
        self.subLblPropRIAftFirg_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Propellant RI (after firing)", None))
        self.subLblFirgDur_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Firing duration (s)", None))
        self.subLblApproxMassFlowRate_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Approximate mass flow rate (mg/s)", None))
        self.subSecPlot.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Plot", None))
        self.lnEditPlotTitle.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Enter the Plot Title", None))
        self.btnIncludeInReport.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Include in Report", None))
        self.subSecPerformance_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Max temp Matrix", None))
        self.btnBckToPlot.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Back to Plot", None))
        self.btnMaxTempsPlot.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Generate Max Temperatures Plot", None))
        self.subSecPerformance.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Performance", None))
        self.subLblChambPressure.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Chamber Pressure (mbar)", None))
        self.subLblVacPressure.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Vacuum Pressure (mbar)", None))
        self.subLblChambTemp.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Maximum Temperature (K)", None))
        self.subLblCharVelo.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Characteristic Velocity (m/s)", None))
        self.subLblCoefOfThrust.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Coefficient of Thrust", None))
        self.subLblBurnTime.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Burn Time (s)", None))
        self.subLblMassFlowRate.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Mass flow Rate (mg/s)", None))
        self.subLblThrust.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Thrust (mN)", None))
        self.subLblSpcImpulse.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Specific Impulse (s)", None))
        self.subLblTotImpulse.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Total Impulse (Ns)", None))
        self.subSecTestAuthorization.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Test Authorization", None))
        self.subLblTestConductedBy.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Test Conducted by", None))
        self.subLblReportGeneratedBy.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Report Generated by", None))
        self.subLblReportAuthorizedBy.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Report Authorized by", None))
#if QT_CONFIG(tooltip)
        self.backBtn.setToolTip(QCoreApplication.translate("VaprIdexMainWindow", u"<html><head/><body><p>Press &quot;<span style=\" font-weight:700;\">Left arrow</span>&quot; on keyboard</p></body></html>", None))
#endif // QT_CONFIG(tooltip)
        self.backBtn.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Back", None))
#if QT_CONFIG(tooltip)
        self.nextBtn.setToolTip(QCoreApplication.translate("VaprIdexMainWindow", u"<html><head/><body><p>Press &quot;<span style=\" font-weight:700;\">Right arrow</span>&quot; on keyboard</p></body></html>", None))
#endif // QT_CONFIG(tooltip)
        self.nextBtn.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Next", None))
        self.label_5.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Firing Duration: ", None))
        self.lblFiringDuration.setText("")
        self.btnTempDataInd.setText("")
        self.btnPressureDataInd.setText("")
        self.btnLoadData.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Clear all Data", None))
        self.lblReportPreview.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Plots Included", None))
        self.lblReportSect.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Report Sections", None))
        self.btnTestPrereq.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Test Prerequisite", None))
        self.btnBasicInfo.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Basic Information", None))
        self.btnSysSpec.setText(QCoreApplication.translate("VaprIdexMainWindow", u"System Specification", None))
        self.btnPropSpec.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Propellant Specification", None))
        self.btnCatSpec.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Catalyst Specification", None))
        self.btnCompDet.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Component details", None))
        self.btnTestDet.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Test details", None))
        self.btnHtrOp.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Heater Operation", None))
        self.btnHtrInfo.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Heater Information", None))
        self.btnHtrCyc.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Heater Cycles", None))
        self.btnPstTestAn.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Post Test analysis", None))
        self.btnPstTestObs.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Post Test Observations", None))
        self.btnCatPostAn.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Catalyst Post-Analysis", None))
        self.btnPropPostAn.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Propellant Post-Analysis", None))
        self.btnPlots.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Plots", None))
        self.lblXAxisTemp.setText(QCoreApplication.translate("VaprIdexMainWindow", u"X-axis:", None))
        self.lblYAxisTemp.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Y-axis:", None))
        self.lblXLabelTemp.setText(QCoreApplication.translate("VaprIdexMainWindow", u" X-label:", None))
        self.lblYLabelTemp.setText(QCoreApplication.translate("VaprIdexMainWindow", u" Y-label:", None))
        self.comboBoxXAxisTemp.setItemText(0, "")

        self.comboBoxYAxisTemp.setItemText(0, "")

        self.tempPlotBtn.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Plot", None))
        self.tabWidgetPlotSettings.setTabText(self.tabWidgetPlotSettings.indexOf(self.temperature_tab), QCoreApplication.translate("VaprIdexMainWindow", u"Temperature", None))
        self.lnEdtY0PressureRelation.setText("")
        self.lblXAxisPressure.setText(QCoreApplication.translate("VaprIdexMainWindow", u"X-axis:", None))
        self.lblYAxisPressure.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Y-axis:", None))
        self.lblXLabelPressure.setText(QCoreApplication.translate("VaprIdexMainWindow", u" X-label:", None))
        self.lblYLabelPressure.setText(QCoreApplication.translate("VaprIdexMainWindow", u" Y-label:", None))
        self.comboBoxXAxisPressure.setItemText(0, "")

        self.comboBoxYAxisPressure.setItemText(0, "")

        self.lblRangePressure.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Range:", None))
        self.pressurePlotBtn.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Plot", None))
        self.tabWidgetPlotSettings.setTabText(self.tabWidgetPlotSettings.indexOf(self.pressure_tab), QCoreApplication.translate("VaprIdexMainWindow", u"Pressure", None))
        self.btnPerformance.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Performance", None))
        self.btnTempMatrix.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Temperature Matrix", None))
        self.tempMatrixIndicator.setText("")
        self.lblGamma.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Gamma", None))
        self.lblGasConst.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Gas Constant", None))
        self.lblGasConstUnit.setText(QCoreApplication.translate("VaprIdexMainWindow", u"J/KgK", None))
        self.lblInitialPropMass.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Ini-Prop mass", None))
        self.lblFinalPropMass.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Fin-Prop mass", None))
        self.lblChamberPressureRange.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Chamber Pressure range", None))
        self.lblVacuumPressureRange.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Vacuum Pressure range", None))
        self.btnCalculate.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Calculate", None))
        self.btnTestAuthorization.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Test Authorization", None))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.mainTab), QCoreApplication.translate("VaprIdexMainWindow", u"Tab 1", None))
        self.label_4.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Search Test Data", None))
        ___qtablewidgetitem = self.results_table_2.horizontalHeaderItem(0)
        ___qtablewidgetitem.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Test ID", None));
        ___qtablewidgetitem1 = self.results_table_2.horizontalHeaderItem(1)
        ___qtablewidgetitem1.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Test Number", None));
        ___qtablewidgetitem2 = self.results_table_2.horizontalHeaderItem(2)
        ___qtablewidgetitem2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Test Date", None));
        ___qtablewidgetitem3 = self.results_table_2.horizontalHeaderItem(3)
        ___qtablewidgetitem3.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Catalyst", None));
        ___qtablewidgetitem4 = self.results_table_2.horizontalHeaderItem(4)
        ___qtablewidgetitem4.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Propellant Conc.", None));
        self.lblCatalystName.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Catalyst Name:", None))
        self.catalyst_name.setText("")
        self.lblTankBottomTemperature.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Tank Bottom Temperature:", None))
        self.lblTestNumber_2.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Test Number:", None))
        self.test_no.setText("")
        self.lblPropRI.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Propellant Concentration:", None))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.filterTab), QCoreApplication.translate("VaprIdexMainWindow", u"Tab 2", None))
        self.lblLog.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Log:", None))
#if QT_CONFIG(tooltip)
        self.lblLogInfo.setToolTip(QCoreApplication.translate("VaprIdexMainWindow", u"<html><head/><body><p>Shows live application status</p></body></html>", None))
#endif // QT_CONFIG(tooltip)
        self.lblLogInfo.setText("")
        self.showReport.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Generate Report", None))
        self.saveDataToDatabase.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Save to Database", None))
#if QT_CONFIG(tooltip)
        self.label_7.setToolTip(QCoreApplication.translate("VaprIdexMainWindow", u"<html><head/><body><p>Current application version</p></body></html>", None))
#endif // QT_CONFIG(tooltip)
        self.label_7.setText(QCoreApplication.translate("VaprIdexMainWindow", u"Version: 1.2.4", None))
    # retranslateUi

