from PySide6.QtCore import Qt, QPropertyAnimation, QRect, Signal, Property
from PySide6.QtGui import QP<PERSON>ter, QPainterPath, QColor, QFont
from PySide6.QtWidgets import QWidget


class MultiStateToggleSwitch(QWidget):
    # Signal emitted when mode changes, sends the mode name and index
    modeChanged = Signal(str, int)

    def __init__(self, modes=None, parent=None):
        super().__init__(parent)

        # Default modes if none provided
        self._modes = modes or ["Mode 1", "Mode 2", "Mode 3"]
        self._current_index = 0
        self._handle_position = 0

        # Calculate width based on number of modes
        self._mode_width = 100  # Width for each mode
        total_width = self._mode_width * len(self._modes)

        # Set fixed size for the widget
        self.setFixedSize(total_width, 30)

        # Colors
        self.background_color = QColor(30, 30, 30)
        self.handle_color = QColor(14, 82, 28)
        self.text_color = QColor(255, 255, 255)
        # self.border_color = QColor(30, 30, 30)

        @property
        def background_color(self):
            return self.background_color

        @background_color.setter
        def background_color(self, color):
            self.background_color = color

        @property
        def handle_color(self):
            return self.handle_color

        @handle_color.setter
        def handle_color(self, color):
            self.handle_color = color

        @property
        def text_color(self):
            return self.text_color

        @text_color.setter
        def text_color(self, color):
            self.text_color = color

        # @property
        # def border_color(self):
        #     return self.border_color
        #
        # @text_color.setter
        # def border_color(self, color):
        #     self.border_color = color

        # Create animation
        self._animation = QPropertyAnimation(self, b"handle_position", self)
        self._animation.setDuration(300)

        # Enable mouse tracking
        self.setMouseTracking(True)

    @Property(float)
    def handle_position(self):
        return self._handle_position

    @handle_position.setter
    def handle_position(self, pos):
        self._handle_position = pos
        self.update()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Calculate dimensions
        width = self.width()
        height = self.height()
        handle_width = self._mode_width - 4

        # Draw main background
        path = QPainterPath()
        path.addRoundedRect(0, 0, width, height, height // 2, height // 2)

        # # Draw border
        # painter.setPen(self.border_color)
        # painter.drawPath(path)

        # Fill background
        painter.fillPath(path, self.background_color)

        # Draw handle
        handle_path = QPainterPath()
        handle_path.addRoundedRect(
            self._handle_position + 2, 2,
            handle_width, height - 4,
            (height - 4) // 2, (height - 4) // 2
        )
        painter.fillPath(handle_path, self.handle_color)

        # Draw mode texts
        painter.setPen(self.text_color)
        font = QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setFamily("Inter")
        painter.setFont(font)

        # Draw all mode names
        for i, mode in enumerate(self._modes):
            x = i * self._mode_width
            mode_rect = QRect(x, 0, self._mode_width, height)

            # Set opacity based on whether this is the current mode
            if i == self._current_index:
                painter.setOpacity(1.0)
            else:
                painter.setOpacity(0.5)

            painter.drawText(mode_rect, Qt.AlignCenter, mode)

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            # Calculate which section was clicked
            click_x = event.position().x()
            clicked_index = int(click_x // self._mode_width)

            if 0 <= clicked_index < len(self._modes):
                self.set_mode(clicked_index)

    def next_mode(self):
        # Calculate next index
        next_index = (self._current_index + 1) % len(self._modes)
        self.set_mode(next_index)

    def set_mode(self, index):
        if 0 <= index < len(self._modes):
            # Save previous position for animation
            start_pos = self._handle_position

            # Update current index
            self._current_index = index

            # Calculate new position
            end_pos = index * self._mode_width

            # Set up and start animation
            self._animation.setStartValue(start_pos)
            self._animation.setEndValue(end_pos)
            self._animation.start()

            # Emit signal with mode name and index
            self.modeChanged.emit(self._modes[index], index)

    def get_current_mode(self):
        return self._modes[self._current_index]

    def get_current_index(self):
        return self._current_index

    def get_mode_index(self, mode_name):
        """
        Returns the index of the specified mode name.
        Returns -1 if the mode name is not found.
        """
        try:
            return self._modes.index(mode_name)
        except ValueError:
            return -1

    def update_modes(self, new_modes):
        """
        Update the toggle switch with new modes.
        
        Args:
            new_modes (list): List of strings representing the new modes
        """
        if not new_modes:
            return
        
        # Store current mode name to maintain state if possible
        current_mode = self._modes[self._current_index]
        
        # Update modes
        self._modes = new_modes
        
        # Recalculate width
        total_width = self._mode_width * len(self._modes)
        self.setFixedSize(total_width, 30)
        
        # Try to maintain the same mode if it exists in new modes
        try:
            new_index = self._modes.index(current_mode)
            self._current_index = new_index
            self._handle_position = new_index * self._mode_width
        except ValueError:
            # If previous mode doesn't exist, reset to first mode
            self._current_index = 0
            self._handle_position = 0
            # Emit signal for mode change
            self.modeChanged.emit(self._modes[0], 0)
        
        # Force a repaint
        self.update()


# Example usage:
if __name__ == "__main__":
    from PySide6.QtWidgets import QApplication
    import sys

    app = QApplication(sys.argv)

    # Create toggle switch with custom modes
    toggle = MultiStateToggleSwitch(["Light", "Dark", "System"])
    toggle.show()


    # Connecting to mode changed signal
    def on_mode_change(mode_name, mode_index):
        print(f"Switched to {mode_name} mode (index: {mode_index})")


    toggle.modeChanged.connect(on_mode_change)

    sys.exit(app.exec())