<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>dialog</class>
 <widget class="QDialog" name="dialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>604</width>
    <height>544</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>600</width>
    <height>500</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Filter Database</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QDialog{
	background-color:#1f2937;
	border-radius: 10px;
}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="leftMargin">
    <number>12</number>
   </property>
   <property name="topMargin">
    <number>12</number>
   </property>
   <property name="rightMargin">
    <number>12</number>
   </property>
   <property name="bottomMargin">
    <number>12</number>
   </property>
   <item>
    <widget class="QLabel" name="title">
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>30</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">font-family: Arial;
font-size: 18px;
font-weight: bold;
color:white;
</string>
     </property>
     <property name="text">
      <string>Search Test Data</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="inputFrame">
     <property name="styleSheet">
      <string notr="true">font-size: 13px;
font-family: Arial;
color: #d2d6dc;
background-color: transparent;</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <layout class="QGridLayout" name="gridLayout">
      <item row="0" column="1">
       <widget class="QLineEdit" name="test_no">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>30</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">background-color:#374151;
color:#989fab;
border-radius:5px;
padding: 5px 5px;
</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="frame">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="lblCatalystName">
        <property name="text">
         <string>Catalyst Name:</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QLineEdit" name="catalyst_name">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>30</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">background-color:#374151;
color:#989fab;
border-radius:5px;
padding: 5px 5px;
</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="frame">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="lblPropRI">
        <property name="text">
         <string>Propellant Concentration:</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="CustomDoubleSpinBox" name="propellant_ri">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>30</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">background-color:#374151;
color:#989fab;
border-radius:5px;
border: 1px solid #374151;</string>
        </property>
        <property name="frame">
         <bool>false</bool>
        </property>
        <property name="buttonSymbols">
         <enum>QAbstractSpinBox::ButtonSymbols::NoButtons</enum>
        </property>
        <property name="decimals">
         <number>6</number>
        </property>
        <property name="maximum">
         <double>1000.000000000000000</double>
        </property>
        <property name="singleStep">
         <double>0.000000000000000</double>
        </property>
       </widget>
      </item>
      <item row="4" column="0">
       <widget class="QLabel" name="lblTankBottomTemperature">
        <property name="text">
         <string>Tank Bottom Temperature:</string>
        </property>
       </widget>
      </item>
      <item row="4" column="1">
       <widget class="CustomDoubleSpinBox" name="tank_temp">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>30</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">background-color:#374151;
color:#989fab;
border-radius:5px;
border: 1px solid #374151;</string>
        </property>
        <property name="frame">
         <bool>false</bool>
        </property>
        <property name="buttonSymbols">
         <enum>QAbstractSpinBox::ButtonSymbols::NoButtons</enum>
        </property>
        <property name="decimals">
         <number>6</number>
        </property>
        <property name="singleStep">
         <double>0.000000000000000</double>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="lblTestNumber">
        <property name="text">
         <string>Test Number:</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="tableFrame">
     <property name="styleSheet">
      <string notr="true">background-color: rgba(188, 191, 195, 0.639);
border-radius: 5px;</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QTableWidget" name="results_table">
        <property name="minimumSize">
         <size>
          <width>530</width>
          <height>0</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">QTableWidget {
                    background-color: #2d2d2d;
                    color: white;
                    gridline-color: #446699;
                   border-radius: 10px;
					border:none;
                }
                QHeaderView::section {
                    background-color: #1e1e1e;
                    color: white;
                    padding: 5px;
                    border: 1px solid #446699;
					 border-radius:5px;
					
                }
                QTableWidget::item {
                    padding: 5px;
                }
                QTableWidget::item:selected {
                    background-color: #47a08e;
                }

</string>
        </property>
        <property name="selectionBehavior">
         <enum>QAbstractItemView::SelectionBehavior::SelectRows</enum>
        </property>
        <property name="sortingEnabled">
         <bool>false</bool>
        </property>
        <property name="wordWrap">
         <bool>true</bool>
        </property>
        <property name="columnCount">
         <number>5</number>
        </property>
        <column>
         <property name="text">
          <string>Test ID</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>Test Number</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>Test Date</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>Catalyst</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>Propellant Conc.</string>
         </property>
        </column>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="frame_3">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color:transparent;
font-size: 15px;
color: white;
font-weight: bold;</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="spacing">
       <number>20</number>
      </property>
      <property name="leftMargin">
       <number>20</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>20</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <spacer name="horizontalSpacer_3">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="load_button">
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>30</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">background-color:#446699;
</string>
        </property>
        <property name="text">
         <string>Load Selected</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_4">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>CustomDoubleSpinBox</class>
   <extends>QDoubleSpinBox</extends>
   <header location="global">..custom_double_spin_box.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
