# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'plot_settingszNqXqz.ui'
##
## Created by: Qt User Interface Compiler version 6.8.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, QConicalGradient, QCursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QAbstractSpinBox, QApplication, QCheckBox, QComboBox,
    QDialog, QFontComboBox, QFrame, QGridLayout,
    QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QSizePolicy, QSpacerItem, QTabWidget, QVBoxLayout,
    QWidget)

from gui.custom_double_spin_box import CustomDoubleSpinBox

class Ui_Plot_settings_Dialog(object):
    def setupUi(self, Plot_settings_Dialog):
        if not Plot_settings_Dialog.objectName():
            Plot_settings_Dialog.setObjectName(u"Plot_settings_Dialog")
        Plot_settings_Dialog.resize(618, 732)
        Plot_settings_Dialog.setMaximumSize(QSize(618, 16777215))
        Plot_settings_Dialog.setStyleSheet(u"QDialog{\n"
"	background-color: #171717;\n"
"}")
        self.gridLayout = QGridLayout(Plot_settings_Dialog)
        self.gridLayout.setObjectName(u"gridLayout")
        self.gridLayout.setContentsMargins(-1, -1, -1, 0)
        self.tabWidget = QTabWidget(Plot_settings_Dialog)
        self.tabWidget.setObjectName(u"tabWidget")
        self.tabWidget.setStyleSheet(u"QTabWidget{\n"
"		background-color: transparent;\n"
"	\n"
"}\n"
"\n"
"QWidget{\n"
"	background-color: transparent;\n"
"	\n"
"}	\n"
"\n"
"QTabWidget::tab-bar{\n"
"	alignment: center;\n"
"}\n"
"\n"
"QTabBar::tab{\n"
"	border-radius: 5px;\n"
"	width: 60px;\n"
"	height: 20px;\n"
"	color: white;\n"
"	font-size: 16px;\n"
"	font-family: inter;\n"
"	padding: 2px;\n"
"}\n"
"\n"
"QTabBar::tab:selected{\n"
"\n"
"	background: white;\n"
"	color: black;\n"
"	\n"
"}\n"
"\n"
"QTabBar::tab:hover {\n"
"                    background: #787878;\n"
"                }")
        self.styleTab = QWidget()
        self.styleTab.setObjectName(u"styleTab")
        self.verticalLayout = QVBoxLayout(self.styleTab)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.line_2 = QFrame(self.styleTab)
        self.line_2.setObjectName(u"line_2")
        self.line_2.setMinimumSize(QSize(0, 11))
        self.line_2.setMaximumSize(QSize(16777215, 11))
        font = QFont()
        font.setPointSize(25)
        self.line_2.setFont(font)
        self.line_2.setStyleSheet(u"QFrame{\n"
"	background-color: #212121;\n"
"	color: #212121;\n"
"}")
        self.line_2.setFrameShadow(QFrame.Shadow.Plain)
        self.line_2.setLineWidth(2)
        self.line_2.setFrameShape(QFrame.Shape.HLine)

        self.verticalLayout.addWidget(self.line_2)

        self.label = QLabel(self.styleTab)
        self.label.setObjectName(u"label")
        self.label.setMaximumSize(QSize(16777215, 30))
        self.label.setStyleSheet(u"QLabel{\n"
"\n"
"	color: #F9F9F9;\n"
"	font-family: inter;\n"
"	font-weight: bold;\n"
"	font-size: 16px;\n"
"}")

        self.verticalLayout.addWidget(self.label)

        self.widget = QWidget(self.styleTab)
        self.widget.setObjectName(u"widget")
        self.gridLayout_2 = QGridLayout(self.widget)
        self.gridLayout_2.setSpacing(9)
        self.gridLayout_2.setObjectName(u"gridLayout_2")
        self.lblTitleFont = QLabel(self.widget)
        self.lblTitleFont.setObjectName(u"lblTitleFont")
        self.lblTitleFont.setMaximumSize(QSize(120, 16777215))
        self.lblTitleFont.setStyleSheet(u"color: #AAAAAA;\n"
"font-size: 16px;\n"
"font-family: inter;\n"
"")

        self.gridLayout_2.addWidget(self.lblTitleFont, 0, 0, 1, 1)

        self.comboBoxLegendFontSize = QComboBox(self.widget)
        self.comboBoxLegendFontSize.setObjectName(u"comboBoxLegendFontSize")
        self.comboBoxLegendFontSize.setMinimumSize(QSize(0, 45))
        self.comboBoxLegendFontSize.setMaximumSize(QSize(100, 45))
        self.comboBoxLegendFontSize.setStyleSheet(u"/* ===== Core ComboBox ===== */\n"
"QComboBox {\n"
"    border: 1px solid #303030;\n"
"    border-radius: 10px;\n"
"    padding: 4px 15px;\n"
"    background: #1C1C1C;\n"
"    color: #EEEEEE;\n"
"    font-size: 16px;\n"
"    /* Force non-native popup */\n"
"    combobox-popup: 0;\n"
"}\n"
"\n"
"/* ===== Dropdown Container Hack ===== */\n"
"QComboBox QFrame {\n"
"    border: none !important; /* Target Qt's internal container */\n"
"    background-color: #2a2a2a;\n"
"    margin: 0;\n"
"    padding: 0;\n"
"    border-radius: 10px;\n"
"}\n"
"\n"
"/* ===== Dropdown Arrow ===== */\n"
"QComboBox::drop-down {\n"
"    border: none;\n"
"    background: transparent;\n"
"    width: 20px;\n"
"    margin-right: 8px;\n"
"}\n"
"\n"
"QComboBox::down-arrow {\n"
"    image: url(\"assets/down-arrow.png\");\n"
"    width: 24px;\n"
"    height: 24px;\n"
"}\n"
"\n"
"\n"
"/* ===== Dropdown List ===== */\n"
"QComboBox QAbstractItemView {\n"
"    border: none;\n"
"    background-color: #2a2a2a;\n"
"    margin: 5px; /* Inner spacing */\n"
""
                        "    padding: 0;\n"
"    border-radius: 10px;\n"
"    /* Remove focus rectangle */\n"
"    outline: 0;\n"
"}\n"
"\n"
"/* ===== Scroll Area ===== */\n"
"QComboBox QScrollArea {\n"
"    border: none;\n"
"    background: transparent;\n"
"}\n"
"\n"
"QComboBox QScrollArea QWidget {\n"
"    background: transparent;\n"
"    border: none;\n"
"}\n"
"\n"
"/* ===== Items ===== */\n"
"QComboBox QAbstractItemView::item {\n"
"    min-height: 30px;\n"
"    padding: 8px 12px;\n"
"    color: white;\n"
"    border: none;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView::item:hover {\n"
"    background: #3d3d3d;\n"
"    border-radius: 5px;\n"
"}\n"
"\n"
"/* Selected item */\n"
"QComboBox QAbstractItemView::item:selected {\n"
"    background-color: #3d3d3d;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"/* Remove the default focus rectangle */\n"
"QComboBox:focus {\n"
"    border: none;\n"
"    outline: none;\n"
"}\n"
"\n"
"/* Hover state for the combobox */\n"
"QComboBox:hover {\n"
"    background-color: #252525;\n"
"}\n"
"\n"
"QComboBox:edit"
                        "able {\n"
"    background: #1C1C1C; /* Fix for editable QComboBox */\n"
"}\n"
"\n"
"/* Scrollbar styling */\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}\n"
"\n"
"/* Horizontal bar */\n"
"QScrollBar:horizontal {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizo"
                        "ntal:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:horizontal,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:horizontal,\n"
"QScrollBar::sub-page:horizontal {\n"
"    background: none;\n"
"}\n"
"\n"
"QFrame{\n"
"	border: none;\n"
"	background-color: #1c1c1c;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView: QFrame{\n"
"	border: none;\n"
"	background-color: #1c1c1c;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView:: QFrame{\n"
"	border: none;\n"
"	background-color: #1c1c1c;\n"
"}")

        self.gridLayout_2.addWidget(self.comboBoxLegendFontSize, 3, 3, 1, 1)

        self.widgetTitleFontColor = QWidget(self.widget)
        self.widgetTitleFontColor.setObjectName(u"widgetTitleFontColor")
        self.widgetTitleFontColor.setMinimumSize(QSize(130, 45))
        self.widgetTitleFontColor.setMaximumSize(QSize(130, 45))
        self.widgetTitleFontColor.setStyleSheet(u"QWidget{\n"
"	background-color: #1C1C1C;\n"
"	border: 1px solid #303030;\n"
"	border-radius: 10px;\n"
"	color: #EEEEEE;\n"
"}\n"
"\n"
"QLineEdit{\n"
"		border:none;\n"
"	font-family: inter;\n"
"	font-size: 14px;\n"
"}")
        self.horizontalLayout = QHBoxLayout(self.widgetTitleFontColor)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalLayout.setContentsMargins(9, 2, -1, 9)
        self.lnEdtTitleFontColorHex = QLineEdit(self.widgetTitleFontColor)
        self.lnEdtTitleFontColorHex.setObjectName(u"lnEdtTitleFontColorHex")
        self.lnEdtTitleFontColorHex.setMinimumSize(QSize(0, 38))
        self.lnEdtTitleFontColorHex.setMaximumSize(QSize(16777215, 38))

        self.horizontalLayout.addWidget(self.lnEdtTitleFontColorHex)

        self.btnTitleFontColorPallet = QPushButton(self.widgetTitleFontColor)
        self.btnTitleFontColorPallet.setObjectName(u"btnTitleFontColorPallet")
        self.btnTitleFontColorPallet.setMinimumSize(QSize(30, 30))
        self.btnTitleFontColorPallet.setMaximumSize(QSize(23, 23))
        self.btnTitleFontColorPallet.setStyleSheet(u"QPushButton{\n"
"	background-color: green;\n"
"}")

        self.horizontalLayout.addWidget(self.btnTitleFontColorPallet)


        self.gridLayout_2.addWidget(self.widgetTitleFontColor, 0, 4, 1, 1)

        self.widgetLegendFontColor = QWidget(self.widget)
        self.widgetLegendFontColor.setObjectName(u"widgetLegendFontColor")
        self.widgetLegendFontColor.setMinimumSize(QSize(130, 45))
        self.widgetLegendFontColor.setMaximumSize(QSize(130, 45))
        self.widgetLegendFontColor.setStyleSheet(u"QWidget{\n"
"	background-color: #1C1C1C;\n"
"	border: 1px solid #303030;\n"
"	border-radius: 10px;\n"
"	color: #EEEEEE;\n"
"}\n"
"\n"
"QLineEdit{\n"
"		border:none;\n"
"	font-family: inter;\n"
"	font-size: 14px;\n"
"}")
        self.horizontalLayout_6 = QHBoxLayout(self.widgetLegendFontColor)
        self.horizontalLayout_6.setObjectName(u"horizontalLayout_6")
        self.horizontalLayout_6.setContentsMargins(-1, 2, -1, -1)
        self.lnEdtLegendFontColorHex = QLineEdit(self.widgetLegendFontColor)
        self.lnEdtLegendFontColorHex.setObjectName(u"lnEdtLegendFontColorHex")
        self.lnEdtLegendFontColorHex.setMinimumSize(QSize(0, 38))
        self.lnEdtLegendFontColorHex.setMaximumSize(QSize(16777215, 38))

        self.horizontalLayout_6.addWidget(self.lnEdtLegendFontColorHex)

        self.btnLegendFontColorPallet = QPushButton(self.widgetLegendFontColor)
        self.btnLegendFontColorPallet.setObjectName(u"btnLegendFontColorPallet")
        self.btnLegendFontColorPallet.setMinimumSize(QSize(30, 30))
        self.btnLegendFontColorPallet.setMaximumSize(QSize(23, 23))
        self.btnLegendFontColorPallet.setStyleSheet(u"QPushButton{\n"
"	background-color: green;\n"
"}")

        self.horizontalLayout_6.addWidget(self.btnLegendFontColorPallet)


        self.gridLayout_2.addWidget(self.widgetLegendFontColor, 3, 4, 1, 1)

        self.lblLabelFont = QLabel(self.widget)
        self.lblLabelFont.setObjectName(u"lblLabelFont")
        self.lblLabelFont.setMaximumSize(QSize(120, 16777215))
        self.lblLabelFont.setStyleSheet(u"color: #AAAAAA;\n"
"font-size: 16px;\n"
"font-family: inter;\n"
"")

        self.gridLayout_2.addWidget(self.lblLabelFont, 1, 0, 1, 1)

        self.comboBoxLabelFontSize = QComboBox(self.widget)
        self.comboBoxLabelFontSize.setObjectName(u"comboBoxLabelFontSize")
        self.comboBoxLabelFontSize.setMinimumSize(QSize(0, 45))
        self.comboBoxLabelFontSize.setMaximumSize(QSize(100, 45))
        self.comboBoxLabelFontSize.setStyleSheet(u"/* ===== Core ComboBox ===== */\n"
"QComboBox {\n"
"    border: 1px solid #303030;\n"
"    border-radius: 10px;\n"
"    padding: 4px 15px;\n"
"    background: #1C1C1C;\n"
"    color: #EEEEEE;\n"
"    font-size: 16px;\n"
"    /* Force non-native popup */\n"
"    combobox-popup: 0;\n"
"}\n"
"\n"
"/* ===== Dropdown Container Hack ===== */\n"
"QComboBox QFrame {\n"
"    border: none !important; /* Target Qt's internal container */\n"
"    background-color: #2a2a2a;\n"
"    margin: 0;\n"
"    padding: 0;\n"
"    border-radius: 10px;\n"
"}\n"
"\n"
"/* ===== Dropdown Arrow ===== */\n"
"QComboBox::drop-down {\n"
"    border: none;\n"
"    background: transparent;\n"
"    width: 20px;\n"
"    margin-right: 8px;\n"
"}\n"
"\n"
"QComboBox::down-arrow {\n"
"    image: url(\"assets/down-arrow.png\");\n"
"    width: 24px;\n"
"    height: 24px;\n"
"}\n"
"\n"
"\n"
"/* ===== Dropdown List ===== */\n"
"QComboBox QAbstractItemView {\n"
"    border: none;\n"
"    background-color: #2a2a2a;\n"
"    margin: 5px; /* Inner spacing */\n"
""
                        "    padding: 0;\n"
"    border-radius: 10px;\n"
"    /* Remove focus rectangle */\n"
"    outline: 0;\n"
"}\n"
"\n"
"/* ===== Scroll Area ===== */\n"
"QComboBox QScrollArea {\n"
"    border: none;\n"
"    background: transparent;\n"
"}\n"
"\n"
"QComboBox QScrollArea QWidget {\n"
"    background: transparent;\n"
"    border: none;\n"
"}\n"
"\n"
"/* ===== Items ===== */\n"
"QComboBox QAbstractItemView::item {\n"
"    min-height: 30px;\n"
"    padding: 8px 12px;\n"
"    color: white;\n"
"    border: none;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView::item:hover {\n"
"    background: #3d3d3d;\n"
"    border-radius: 5px;\n"
"}\n"
"\n"
"/* Selected item */\n"
"QComboBox QAbstractItemView::item:selected {\n"
"    background-color: #3d3d3d;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"/* Remove the default focus rectangle */\n"
"QComboBox:focus {\n"
"    border: none;\n"
"    outline: none;\n"
"}\n"
"\n"
"/* Hover state for the combobox */\n"
"QComboBox:hover {\n"
"    background-color: #252525;\n"
"}\n"
"\n"
"QComboBox:edit"
                        "able {\n"
"    background: #1C1C1C; /* Fix for editable QComboBox */\n"
"}\n"
"\n"
"/* Scrollbar styling */\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}\n"
"\n"
"/* Horizontal bar */\n"
"QScrollBar:horizontal {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizo"
                        "ntal:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:horizontal,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:horizontal,\n"
"QScrollBar::sub-page:horizontal {\n"
"    background: none;\n"
"}\n"
"\n"
"QFrame{\n"
"	border: none;\n"
"	background-color: #1c1c1c;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView: QFrame{\n"
"	border: none;\n"
"	background-color: #1c1c1c;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView:: QFrame{\n"
"	border: none;\n"
"	background-color: #1c1c1c;\n"
"}")

        self.gridLayout_2.addWidget(self.comboBoxLabelFontSize, 1, 3, 1, 1)

        self.widgetLabelFontColor = QWidget(self.widget)
        self.widgetLabelFontColor.setObjectName(u"widgetLabelFontColor")
        self.widgetLabelFontColor.setMinimumSize(QSize(130, 45))
        self.widgetLabelFontColor.setMaximumSize(QSize(130, 45))
        self.widgetLabelFontColor.setStyleSheet(u"QWidget{\n"
"	background-color: #1C1C1C;\n"
"	border: 1px solid #303030;\n"
"	border-radius: 10px;\n"
"	color: #EEEEEE;\n"
"}\n"
"\n"
"QLineEdit{\n"
"		border:none;\n"
"	font-family: inter;\n"
"	font-size: 14px;\n"
"}")
        self.horizontalLayout_4 = QHBoxLayout(self.widgetLabelFontColor)
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.horizontalLayout_4.setContentsMargins(-1, 2, -1, -1)
        self.lnEdtLabelFontColorHex = QLineEdit(self.widgetLabelFontColor)
        self.lnEdtLabelFontColorHex.setObjectName(u"lnEdtLabelFontColorHex")
        self.lnEdtLabelFontColorHex.setMinimumSize(QSize(0, 38))
        self.lnEdtLabelFontColorHex.setMaximumSize(QSize(16777215, 38))

        self.horizontalLayout_4.addWidget(self.lnEdtLabelFontColorHex)

        self.btnLabelFontColorPallet = QPushButton(self.widgetLabelFontColor)
        self.btnLabelFontColorPallet.setObjectName(u"btnLabelFontColorPallet")
        self.btnLabelFontColorPallet.setMinimumSize(QSize(30, 30))
        self.btnLabelFontColorPallet.setMaximumSize(QSize(23, 23))
        self.btnLabelFontColorPallet.setStyleSheet(u"QPushButton{\n"
"	background-color: green;\n"
"}")

        self.horizontalLayout_4.addWidget(self.btnLabelFontColorPallet)


        self.gridLayout_2.addWidget(self.widgetLabelFontColor, 1, 4, 1, 1)

        self.lblLegendFont = QLabel(self.widget)
        self.lblLegendFont.setObjectName(u"lblLegendFont")
        self.lblLegendFont.setMaximumSize(QSize(120, 16777215))
        self.lblLegendFont.setStyleSheet(u"color: #AAAAAA;\n"
"font-size: 16px;\n"
"font-family: inter;\n"
"")

        self.gridLayout_2.addWidget(self.lblLegendFont, 3, 0, 1, 1)

        self.comboBoxTitleFontSize = QComboBox(self.widget)
        self.comboBoxTitleFontSize.setObjectName(u"comboBoxTitleFontSize")
        self.comboBoxTitleFontSize.setMinimumSize(QSize(0, 45))
        self.comboBoxTitleFontSize.setMaximumSize(QSize(100, 45))
        self.comboBoxTitleFontSize.setStyleSheet(u"/* ===== Core ComboBox ===== */\n"
"QComboBox {\n"
"    border: 1px solid #303030;\n"
"    border-radius: 10px;\n"
"    padding: 4px 15px;\n"
"    background: #1C1C1C;\n"
"    color: #EEEEEE;\n"
"    font-size: 16px;\n"
"    /* Force non-native popup */\n"
"    combobox-popup: 0;\n"
"}\n"
"\n"
"/* ===== Dropdown Container Hack ===== */\n"
"QComboBox QFrame {\n"
"    border: none !important; /* Target Qt's internal container */\n"
"    background-color: #2a2a2a;\n"
"    margin: 0;\n"
"    padding: 0;\n"
"    border-radius: 10px;\n"
"}\n"
"\n"
"/* ===== Dropdown Arrow ===== */\n"
"QComboBox::drop-down {\n"
"    border: none;\n"
"    background: transparent;\n"
"    width: 20px;\n"
"    margin-right: 8px;\n"
"}\n"
"\n"
"QComboBox::down-arrow {\n"
"    image: url(\"assets/down-arrow.png\");\n"
"    width: 24px;\n"
"    height: 24px;\n"
"}\n"
"\n"
"\n"
"/* ===== Dropdown List ===== */\n"
"QComboBox QAbstractItemView {\n"
"    border: none;\n"
"    background-color: #2a2a2a;\n"
"    margin: 5px; /* Inner spacing */\n"
""
                        "    padding: 0;\n"
"    border-radius: 10px;\n"
"    /* Remove focus rectangle */\n"
"    outline: 0;\n"
"}\n"
"\n"
"/* ===== Scroll Area ===== */\n"
"QComboBox QScrollArea {\n"
"    border: none;\n"
"    background: transparent;\n"
"}\n"
"\n"
"QComboBox QScrollArea QWidget {\n"
"    background: transparent;\n"
"    border: none;\n"
"}\n"
"\n"
"/* ===== Items ===== */\n"
"QComboBox QAbstractItemView::item {\n"
"    min-height: 30px;\n"
"    padding: 8px 12px;\n"
"    color: white;\n"
"    border: none;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView::item:hover {\n"
"    background: #3d3d3d;\n"
"    border-radius: 5px;\n"
"}\n"
"\n"
"/* Selected item */\n"
"QComboBox QAbstractItemView::item:selected {\n"
"    background-color: #3d3d3d;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"/* Remove the default focus rectangle */\n"
"QComboBox:focus {\n"
"    border: none;\n"
"    outline: none;\n"
"}\n"
"\n"
"/* Hover state for the combobox */\n"
"QComboBox:hover {\n"
"    background-color: #252525;\n"
"}\n"
"\n"
"QComboBox:edit"
                        "able {\n"
"    background: #1C1C1C; /* Fix for editable QComboBox */\n"
"}\n"
"\n"
"/* Scrollbar styling */\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}\n"
"\n"
"/* Horizontal bar */\n"
"QScrollBar:horizontal {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizo"
                        "ntal:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:horizontal,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:horizontal,\n"
"QScrollBar::sub-page:horizontal {\n"
"    background: none;\n"
"}\n"
"\n"
"QFrame{\n"
"	border: none;\n"
"	background-color: #1c1c1c;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView: QFrame{\n"
"	border: none;\n"
"	background-color: #1c1c1c;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView:: QFrame{\n"
"	border: none;\n"
"	background-color: #1c1c1c;\n"
"}")

        self.gridLayout_2.addWidget(self.comboBoxTitleFontSize, 0, 3, 1, 1)

        self.comboBoxTitleFont = QFontComboBox(self.widget)
        self.comboBoxTitleFont.setObjectName(u"comboBoxTitleFont")
        self.comboBoxTitleFont.setMinimumSize(QSize(148, 45))
        self.comboBoxTitleFont.setMaximumSize(QSize(220, 45))
        self.comboBoxTitleFont.setStyleSheet(u"QComboBox {\n"
"    border: 1px solid #303030;\n"
"    border-radius: 10px;\n"
"    padding: 4px 10px;\n"
"    background-color: #1C1C1C;\n"
"    color: #EEEEEE;\n"
"    min-width: 6em;\n"
"	font-size: 16px;\n"
"}\n"
"\n"
"QComboBox::drop-down {\n"
"    border: none;\n"
"    background: transparent;\n"
"	width: 20px;\n"
"	margin-right: 8px;\n"
"}\n"
"\n"
"QComboBox::down-arrow {\n"
"    image: url(\"assets/down-arrow.png\");  /* You can use a custom arrow image */\n"
"    width: 24px;\n"
"    height: 24px;\n"
"}\n"
"\n"
"\n"
"/* Dropdown list styling */\n"
"QComboBox QAbstractItemView {\n"
"    border: none;\n"
"	margin: 5px;\n"
"    border-radius: 10px;\n"
"    padding: 5px 0px;\n"
"    background-color: #2a2a2a;\n"
"    color: white;\n"
"    selection-background-color: #3a3a3a;  /* Darker selection color */\n"
"    outline: 0px;  /* Remove focus border */\n"
"}\n"
"\n"
"/* Individual item styling */\n"
"QComboBox QAbstractItemView::item {\n"
"    padding: 8px 12px;  /* Vertical and horizontal padding for ite"
                        "ms */\n"
"    border: none;\n"
"    min-height: 24px;  /* Minimum height for items */\n"
"}\n"
"\n"
"/* Hover state for items */\n"
"QComboBox QAbstractItemView::item:hover {\n"
"    background-color: #3d3d3d;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"/* Selected item */\n"
"QComboBox QAbstractItemView::item:selected {\n"
"    background-color: #3d3d3d;\n"
"}\n"
"\n"
"/* Remove the default focus rectangle */\n"
"QComboBox:focus {\n"
"    border: none;\n"
"    outline: none;\n"
"}\n"
"\n"
"/* Hover state for the combobox */\n"
"QComboBox:hover {\n"
"    background-color: #252525;\n"
"}\n"
"\n"
"/* Scrollbar styling */\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical,\n"
""
                        "QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}\n"
"\n"
"/* Horizontal bar */\n"
"QScrollBar:horizontal {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:horizontal,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:horizontal,\n"
"QScrollBar::sub-page:horizontal {\n"
"    background: none;\n"
"}")

        self.gridLayout_2.addWidget(self.comboBoxTitleFont, 0, 2, 1, 1)

        self.comboBoxLabelFont = QFontComboBox(self.widget)
        self.comboBoxLabelFont.setObjectName(u"comboBoxLabelFont")
        self.comboBoxLabelFont.setMinimumSize(QSize(148, 45))
        self.comboBoxLabelFont.setMaximumSize(QSize(220, 45))
        self.comboBoxLabelFont.setStyleSheet(u"QComboBox {\n"
"    border: 1px solid #303030;\n"
"    border-radius: 10px;\n"
"    padding: 4px 10px;\n"
"    background-color: #1C1C1C;\n"
"    color: #EEEEEE;\n"
"    min-width: 6em;\n"
"	font-size: 16px;\n"
"}\n"
"\n"
"QComboBox::drop-down {\n"
"    border: none;\n"
"    background: transparent;\n"
"	width: 20px;\n"
"	margin-right: 8px;\n"
"}\n"
"\n"
"QComboBox::down-arrow {\n"
"    image: url(\"assets/down-arrow.png\");  /* You can use a custom arrow image */\n"
"    width: 24px;\n"
"    height: 24px;\n"
"}\n"
"\n"
"\n"
"/* Dropdown list styling */\n"
"QComboBox QAbstractItemView {\n"
"    border: none;\n"
"	margin: 5px;\n"
"    border-radius: 10px;\n"
"    padding: 5px 0px;\n"
"    background-color: #2a2a2a;\n"
"    color: white;\n"
"    selection-background-color: #3a3a3a;  /* Darker selection color */\n"
"    outline: 0px;  /* Remove focus border */\n"
"}\n"
"\n"
"/* Individual item styling */\n"
"QComboBox QAbstractItemView::item {\n"
"    padding: 8px 12px;  /* Vertical and horizontal padding for ite"
                        "ms */\n"
"    border: none;\n"
"    min-height: 24px;  /* Minimum height for items */\n"
"}\n"
"\n"
"/* Hover state for items */\n"
"QComboBox QAbstractItemView::item:hover {\n"
"    background-color: #3d3d3d;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"/* Selected item */\n"
"QComboBox QAbstractItemView::item:selected {\n"
"    background-color: #3d3d3d;\n"
"}\n"
"\n"
"/* Remove the default focus rectangle */\n"
"QComboBox:focus {\n"
"    border: none;\n"
"    outline: none;\n"
"}\n"
"\n"
"/* Hover state for the combobox */\n"
"QComboBox:hover {\n"
"    background-color: #252525;\n"
"}\n"
"\n"
"/* Scrollbar styling */\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical,\n"
""
                        "QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}\n"
"\n"
"/* Horizontal bar */\n"
"QScrollBar:horizontal {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:horizontal,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:horizontal,\n"
"QScrollBar::sub-page:horizontal {\n"
"    background: none;\n"
"}")

        self.gridLayout_2.addWidget(self.comboBoxLabelFont, 1, 2, 1, 1)

        self.comboBoxLegendFont = QFontComboBox(self.widget)
        self.comboBoxLegendFont.setObjectName(u"comboBoxLegendFont")
        self.comboBoxLegendFont.setMinimumSize(QSize(148, 45))
        self.comboBoxLegendFont.setMaximumSize(QSize(220, 45))
        self.comboBoxLegendFont.setStyleSheet(u"QComboBox {\n"
"    border: 1px solid #303030;\n"
"    border-radius: 10px;\n"
"    padding: 4px 10px;\n"
"    background-color: #1C1C1C;\n"
"    color: #EEEEEE;\n"
"    min-width: 6em;\n"
"	font-size: 16px;\n"
"}\n"
"\n"
"QComboBox::drop-down {\n"
"    border: none;\n"
"    background: transparent;\n"
"	width: 20px;\n"
"	margin-right: 8px;\n"
"}\n"
"\n"
"QComboBox::down-arrow {\n"
"    image: url(\"assets/down-arrow.png\");  /* You can use a custom arrow image */\n"
"    width: 24px;\n"
"    height: 24px;\n"
"}\n"
"\n"
"\n"
"/* Dropdown list styling */\n"
"QComboBox QAbstractItemView {\n"
"    border: none;\n"
"	margin: 5px;\n"
"    border-radius: 10px;\n"
"    padding: 5px 0px;\n"
"    background-color: #2a2a2a;\n"
"    color: white;\n"
"    selection-background-color: #3a3a3a;  /* Darker selection color */\n"
"    outline: 0px;  /* Remove focus border */\n"
"}\n"
"\n"
"/* Individual item styling */\n"
"QComboBox QAbstractItemView::item {\n"
"    padding: 8px 12px;  /* Vertical and horizontal padding for ite"
                        "ms */\n"
"    border: none;\n"
"    min-height: 24px;  /* Minimum height for items */\n"
"}\n"
"\n"
"/* Hover state for items */\n"
"QComboBox QAbstractItemView::item:hover {\n"
"    background-color: #3d3d3d;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"/* Selected item */\n"
"QComboBox QAbstractItemView::item:selected {\n"
"    background-color: #3d3d3d;\n"
"}\n"
"\n"
"/* Remove the default focus rectangle */\n"
"QComboBox:focus {\n"
"    border: none;\n"
"    outline: none;\n"
"}\n"
"\n"
"/* Hover state for the combobox */\n"
"QComboBox:hover {\n"
"    background-color: #252525;\n"
"}\n"
"\n"
"/* Scrollbar styling */\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical,\n"
""
                        "QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}\n"
"\n"
"/* Horizontal bar */\n"
"QScrollBar:horizontal {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:horizontal,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:horizontal,\n"
"QScrollBar::sub-page:horizontal {\n"
"    background: none;\n"
"}")

        self.gridLayout_2.addWidget(self.comboBoxLegendFont, 3, 2, 1, 1)


        self.verticalLayout.addWidget(self.widget)

        self.line = QFrame(self.styleTab)
        self.line.setObjectName(u"line")
        self.line.setMinimumSize(QSize(0, 11))
        self.line.setMaximumSize(QSize(16777215, 11))
        self.line.setFont(font)
        self.line.setStyleSheet(u"QFrame{\n"
"	background-color: #212121;\n"
"	color: #212121;\n"
"}")
        self.line.setFrameShadow(QFrame.Shadow.Plain)
        self.line.setLineWidth(2)
        self.line.setFrameShape(QFrame.Shape.HLine)

        self.verticalLayout.addWidget(self.line)

        self.label_2 = QLabel(self.styleTab)
        self.label_2.setObjectName(u"label_2")
        self.label_2.setMaximumSize(QSize(16777215, 30))
        self.label_2.setStyleSheet(u"QLabel{\n"
"\n"
"	color: #F9F9F9;\n"
"	font-family: inter;\n"
"	font-weight: bold;\n"
"	font-size: 16px;\n"
"}")

        self.verticalLayout.addWidget(self.label_2)

        self.widget_2 = QWidget(self.styleTab)
        self.widget_2.setObjectName(u"widget_2")
        self.verticalLayout_2 = QVBoxLayout(self.widget_2)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.widget_9 = QWidget(self.widget_2)
        self.widget_9.setObjectName(u"widget_9")
        self.horizontalLayout_2 = QHBoxLayout(self.widget_9)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.lblDataSelection = QLabel(self.widget_9)
        self.lblDataSelection.setObjectName(u"lblDataSelection")
        self.lblDataSelection.setMaximumSize(QSize(120, 16777215))
        self.lblDataSelection.setStyleSheet(u"color: #AAAAAA;\n"
"font-size: 16px;\n"
"font-family: inter;\n"
"")

        self.horizontalLayout_2.addWidget(self.lblDataSelection)

        self.comboBoxDataSelection = QComboBox(self.widget_9)
        self.comboBoxDataSelection.setObjectName(u"comboBoxDataSelection")
        self.comboBoxDataSelection.setMinimumSize(QSize(0, 45))
        self.comboBoxDataSelection.setMaximumSize(QSize(330, 45))
        self.comboBoxDataSelection.setStyleSheet(u"/* ===== Core ComboBox ===== */\n"
"QComboBox {\n"
"    border: 1px solid #303030;\n"
"    border-radius: 10px;\n"
"    padding: 4px 15px;\n"
"    background: #1C1C1C;\n"
"    color: #EEEEEE;\n"
"    font-size: 16px;\n"
"    /* Force non-native popup */\n"
"    combobox-popup: 0;\n"
"}\n"
"\n"
"/* ===== Dropdown Container Hack ===== */\n"
"QComboBox QFrame {\n"
"    border: none !important; /* Target Qt's internal container */\n"
"    background-color: #2a2a2a;\n"
"    margin: 0;\n"
"    padding: 0;\n"
"    border-radius: 10px;\n"
"}\n"
"\n"
"/* ===== Dropdown Arrow ===== */\n"
"QComboBox::drop-down {\n"
"    border: none;\n"
"    background: transparent;\n"
"    width: 20px;\n"
"    margin-right: 8px;\n"
"}\n"
"\n"
"QComboBox::down-arrow {\n"
"    image: url(\"assets/down-arrow.png\");\n"
"    width: 24px;\n"
"    height: 24px;\n"
"}\n"
"\n"
"\n"
"/* ===== Dropdown List ===== */\n"
"QComboBox QAbstractItemView {\n"
"    border: none;\n"
"    background-color: #2a2a2a;\n"
"    margin: 5px; /* Inner spacing */\n"
""
                        "    padding: 0;\n"
"    border-radius: 10px;\n"
"    /* Remove focus rectangle */\n"
"    outline: 0;\n"
"}\n"
"\n"
"/* ===== Scroll Area ===== */\n"
"QComboBox QScrollArea {\n"
"    border: none;\n"
"    background: transparent;\n"
"}\n"
"\n"
"QComboBox QScrollArea QWidget {\n"
"    background: transparent;\n"
"    border: none;\n"
"}\n"
"\n"
"/* ===== Items ===== */\n"
"QComboBox QAbstractItemView::item {\n"
"    min-height: 30px;\n"
"    padding: 8px 12px;\n"
"    color: white;\n"
"    border: none;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView::item:hover {\n"
"    background: #3d3d3d;\n"
"    border-radius: 5px;\n"
"}\n"
"\n"
"/* Selected item */\n"
"QComboBox QAbstractItemView::item:selected {\n"
"    background-color: #3d3d3d;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"/* Remove the default focus rectangle */\n"
"QComboBox:focus {\n"
"    border: none;\n"
"    outline: none;\n"
"}\n"
"\n"
"/* Hover state for the combobox */\n"
"QComboBox:hover {\n"
"    background-color: #252525;\n"
"}\n"
"\n"
"QComboBox:edit"
                        "able {\n"
"    background: #1C1C1C; /* Fix for editable QComboBox */\n"
"}\n"
"\n"
"/* Scrollbar styling */\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}\n"
"\n"
"/* Horizontal bar */\n"
"QScrollBar:horizontal {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizo"
                        "ntal:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:horizontal,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:horizontal,\n"
"QScrollBar::sub-page:horizontal {\n"
"    background: none;\n"
"}\n"
"\n"
"QFrame{\n"
"	border: none;\n"
"	background-color: #1c1c1c;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView: QFrame{\n"
"	border: none;\n"
"	background-color: #1c1c1c;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView:: QFrame{\n"
"	border: none;\n"
"	background-color: #1c1c1c;\n"
"}")

        self.horizontalLayout_2.addWidget(self.comboBoxDataSelection)

        self.widgetDataSelectionColor = QWidget(self.widget_9)
        self.widgetDataSelectionColor.setObjectName(u"widgetDataSelectionColor")
        self.widgetDataSelectionColor.setMinimumSize(QSize(130, 45))
        self.widgetDataSelectionColor.setMaximumSize(QSize(130, 45))
        self.widgetDataSelectionColor.setStyleSheet(u"QWidget{\n"
"	background-color: #1C1C1C;\n"
"	border: 1px solid #303030;\n"
"	border-radius: 10px;\n"
"	color: #EEEEEE;\n"
"}\n"
"\n"
"QLineEdit{\n"
"		border:none;\n"
"	font-family: inter;\n"
"	font-size: 14px;\n"
"}")
        self.horizontalLayout_5 = QHBoxLayout(self.widgetDataSelectionColor)
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.horizontalLayout_5.setContentsMargins(-1, 2, -1, -1)
        self.lnEdtDataSelectionColorHex = QLineEdit(self.widgetDataSelectionColor)
        self.lnEdtDataSelectionColorHex.setObjectName(u"lnEdtDataSelectionColorHex")
        self.lnEdtDataSelectionColorHex.setMinimumSize(QSize(0, 38))
        self.lnEdtDataSelectionColorHex.setMaximumSize(QSize(16777215, 38))

        self.horizontalLayout_5.addWidget(self.lnEdtDataSelectionColorHex)

        self.btnDataSelectionColorPallet = QPushButton(self.widgetDataSelectionColor)
        self.btnDataSelectionColorPallet.setObjectName(u"btnDataSelectionColorPallet")
        self.btnDataSelectionColorPallet.setMinimumSize(QSize(30, 30))
        self.btnDataSelectionColorPallet.setMaximumSize(QSize(23, 23))
        self.btnDataSelectionColorPallet.setStyleSheet(u"QPushButton{\n"
"	background-color: green;\n"
"}")

        self.horizontalLayout_5.addWidget(self.btnDataSelectionColorPallet)


        self.horizontalLayout_2.addWidget(self.widgetDataSelectionColor)


        self.verticalLayout_2.addWidget(self.widget_9)

        self.widget_10 = QWidget(self.widget_2)
        self.widget_10.setObjectName(u"widget_10")
        self.horizontalLayout_3 = QHBoxLayout(self.widget_10)
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.lblLegendName = QLabel(self.widget_10)
        self.lblLegendName.setObjectName(u"lblLegendName")
        self.lblLegendName.setMinimumSize(QSize(120, 0))
        self.lblLegendName.setMaximumSize(QSize(120, 16777215))
        self.lblLegendName.setStyleSheet(u"color: #AAAAAA;\n"
"font-size: 16px;\n"
"font-family: inter;\n"
"")

        self.horizontalLayout_3.addWidget(self.lblLegendName)

        self.lnEdtLegendName = QLineEdit(self.widget_10)
        self.lnEdtLegendName.setObjectName(u"lnEdtLegendName")
        self.lnEdtLegendName.setMinimumSize(QSize(0, 45))
        self.lnEdtLegendName.setMaximumSize(QSize(16777215, 45))
        self.lnEdtLegendName.setStyleSheet(u"QLineEdit{\n"
"	background-color: #1C1C1C;\n"
"	border: 1px solid #303030;\n"
"	border-radius: 10px;\n"
"	color: #EEEEEE;\n"
"	font-size: 16px;\n"
"	padding: 4px 15px;\n"
"}")

        self.horizontalLayout_3.addWidget(self.lnEdtLegendName)


        self.verticalLayout_2.addWidget(self.widget_10)


        self.verticalLayout.addWidget(self.widget_2)

        self.line_3 = QFrame(self.styleTab)
        self.line_3.setObjectName(u"line_3")
        self.line_3.setMinimumSize(QSize(0, 11))
        self.line_3.setMaximumSize(QSize(16777215, 11))
        self.line_3.setFont(font)
        self.line_3.setStyleSheet(u"QFrame{\n"
"	background-color: #212121;\n"
"	color: #212121;\n"
"}")
        self.line_3.setFrameShadow(QFrame.Shadow.Plain)
        self.line_3.setLineWidth(2)
        self.line_3.setFrameShape(QFrame.Shape.HLine)

        self.verticalLayout.addWidget(self.line_3)

        self.label_3 = QLabel(self.styleTab)
        self.label_3.setObjectName(u"label_3")
        self.label_3.setMaximumSize(QSize(16777215, 30))
        self.label_3.setStyleSheet(u"QLabel{\n"
"\n"
"	color: #F9F9F9;\n"
"	font-family: inter;\n"
"	font-weight: bold;\n"
"	font-size: 16px;\n"
"}")

        self.verticalLayout.addWidget(self.label_3)

        self.widget_3 = QWidget(self.styleTab)
        self.widget_3.setObjectName(u"widget_3")
        self.widget_3.setStyleSheet(u"QCheckBox {\n"
"    color: #444444;\n"
"}\n"
"\n"
"QCheckBox::indicator {\n"
"    width: 18px;\n"
"    height: 18px;\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(\"D:/SoftwareDevelopment/User_verification_Email/assets/checkbox_checked.png\");\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(\"D:/SoftwareDevelopment/User_verification_Email/assets/checkbox_unchecked.png\");\n"
"}\n"
"")
        self.horizontalLayout_9 = QHBoxLayout(self.widget_3)
        self.horizontalLayout_9.setObjectName(u"horizontalLayout_9")
        self.checkBoxMajorGrid = QCheckBox(self.widget_3)
        self.checkBoxMajorGrid.setObjectName(u"checkBoxMajorGrid")
        self.checkBoxMajorGrid.setMaximumSize(QSize(25, 25))
        self.checkBoxMajorGrid.setStyleSheet(u"QCheckBox {\n"
"    color: #444444;\n"
"}\n"
"\n"
"QCheckBox::indicator {\n"
"    width: 18px;\n"
"    height: 18px;\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(\"D:/SoftwareDevelopment/User_verification_Email/assets/checkbox_checked.png\");\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(\"D:/SoftwareDevelopment/User_verification_Email/assets/checkbox_unchecked.png\");\n"
"}\n"
"")

        self.horizontalLayout_9.addWidget(self.checkBoxMajorGrid)

        self.lblMajorGrid = QLabel(self.widget_3)
        self.lblMajorGrid.setObjectName(u"lblMajorGrid")
        self.lblMajorGrid.setMinimumSize(QSize(100, 0))
        self.lblMajorGrid.setMaximumSize(QSize(100, 16777215))
        self.lblMajorGrid.setStyleSheet(u"color: #AAAAAA;\n"
"font-size: 16px;\n"
"font-family: inter;\n"
"")

        self.horizontalLayout_9.addWidget(self.lblMajorGrid)

        self.widgetMajorGridColor = QWidget(self.widget_3)
        self.widgetMajorGridColor.setObjectName(u"widgetMajorGridColor")
        self.widgetMajorGridColor.setMinimumSize(QSize(130, 45))
        self.widgetMajorGridColor.setMaximumSize(QSize(130, 45))
        self.widgetMajorGridColor.setStyleSheet(u"QWidget{\n"
"	background-color: #1C1C1C;\n"
"	border: 1px solid #303030;\n"
"	border-radius: 10px;\n"
"	color: #EEEEEE;\n"
"}\n"
"\n"
"QLineEdit{\n"
"		border:none;\n"
"	font-family: inter;\n"
"	font-size: 14px;\n"
"}")
        self.horizontalLayout_7 = QHBoxLayout(self.widgetMajorGridColor)
        self.horizontalLayout_7.setObjectName(u"horizontalLayout_7")
        self.horizontalLayout_7.setContentsMargins(-1, 2, -1, -1)
        self.lnEdtMajorGridColorHex = QLineEdit(self.widgetMajorGridColor)
        self.lnEdtMajorGridColorHex.setObjectName(u"lnEdtMajorGridColorHex")
        self.lnEdtMajorGridColorHex.setMinimumSize(QSize(0, 38))
        self.lnEdtMajorGridColorHex.setMaximumSize(QSize(16777215, 38))

        self.horizontalLayout_7.addWidget(self.lnEdtMajorGridColorHex)

        self.btnMajorGridColorPallet = QPushButton(self.widgetMajorGridColor)
        self.btnMajorGridColorPallet.setObjectName(u"btnMajorGridColorPallet")
        self.btnMajorGridColorPallet.setMinimumSize(QSize(30, 30))
        self.btnMajorGridColorPallet.setMaximumSize(QSize(23, 23))
        self.btnMajorGridColorPallet.setStyleSheet(u"QPushButton{\n"
"	background-color: green;\n"
"}")

        self.horizontalLayout_7.addWidget(self.btnMajorGridColorPallet)


        self.horizontalLayout_9.addWidget(self.widgetMajorGridColor)

        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_9.addItem(self.horizontalSpacer)

        self.line_5 = QFrame(self.widget_3)
        self.line_5.setObjectName(u"line_5")
        self.line_5.setMinimumSize(QSize(2, 0))
        self.line_5.setMaximumSize(QSize(2, 1667125))
        self.line_5.setStyleSheet(u"Line{\n"
"	background-color: #212121;\n"
"}")
        self.line_5.setFrameShape(QFrame.Shape.VLine)
        self.line_5.setFrameShadow(QFrame.Shadow.Sunken)

        self.horizontalLayout_9.addWidget(self.line_5)

        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_9.addItem(self.horizontalSpacer_2)

        self.checkBoxMinorGrid = QCheckBox(self.widget_3)
        self.checkBoxMinorGrid.setObjectName(u"checkBoxMinorGrid")
        self.checkBoxMinorGrid.setMaximumSize(QSize(25, 25))

        self.horizontalLayout_9.addWidget(self.checkBoxMinorGrid)

        self.lblMinorGrid = QLabel(self.widget_3)
        self.lblMinorGrid.setObjectName(u"lblMinorGrid")
        self.lblMinorGrid.setMinimumSize(QSize(100, 0))
        self.lblMinorGrid.setMaximumSize(QSize(100, 16777215))
        self.lblMinorGrid.setStyleSheet(u"color: #AAAAAA;\n"
"font-size: 16px;\n"
"font-family: inter;\n"
"")

        self.horizontalLayout_9.addWidget(self.lblMinorGrid)

        self.widgetMinorGridColor = QWidget(self.widget_3)
        self.widgetMinorGridColor.setObjectName(u"widgetMinorGridColor")
        self.widgetMinorGridColor.setMinimumSize(QSize(130, 45))
        self.widgetMinorGridColor.setMaximumSize(QSize(130, 45))
        self.widgetMinorGridColor.setStyleSheet(u"QWidget{\n"
"	background-color: #1C1C1C;\n"
"	border: 1px solid #303030;\n"
"	border-radius: 10px;\n"
"	color: #EEEEEE;\n"
"}\n"
"\n"
"QLineEdit{\n"
"		border:none;\n"
"	font-family: inter;\n"
"	font-size: 14px;\n"
"}")
        self.horizontalLayout_8 = QHBoxLayout(self.widgetMinorGridColor)
        self.horizontalLayout_8.setObjectName(u"horizontalLayout_8")
        self.horizontalLayout_8.setContentsMargins(-1, 2, 9, -1)
        self.lnEdtMinorGridColorHex = QLineEdit(self.widgetMinorGridColor)
        self.lnEdtMinorGridColorHex.setObjectName(u"lnEdtMinorGridColorHex")
        self.lnEdtMinorGridColorHex.setMinimumSize(QSize(0, 38))
        self.lnEdtMinorGridColorHex.setMaximumSize(QSize(16777215, 38))

        self.horizontalLayout_8.addWidget(self.lnEdtMinorGridColorHex)

        self.btnMinorGridColorPallet = QPushButton(self.widgetMinorGridColor)
        self.btnMinorGridColorPallet.setObjectName(u"btnMinorGridColorPallet")
        self.btnMinorGridColorPallet.setMinimumSize(QSize(30, 30))
        self.btnMinorGridColorPallet.setMaximumSize(QSize(23, 23))
        self.btnMinorGridColorPallet.setStyleSheet(u"QPushButton{\n"
"	background-color: green;\n"
"}")

        self.horizontalLayout_8.addWidget(self.btnMinorGridColorPallet)


        self.horizontalLayout_9.addWidget(self.widgetMinorGridColor)


        self.verticalLayout.addWidget(self.widget_3)

        self.line_4 = QFrame(self.styleTab)
        self.line_4.setObjectName(u"line_4")
        self.line_4.setMinimumSize(QSize(0, 11))
        self.line_4.setMaximumSize(QSize(16777215, 11))
        self.line_4.setFont(font)
        self.line_4.setStyleSheet(u"QFrame{\n"
"	background-color: #212121;\n"
"	color: #212121;\n"
"}")
        self.line_4.setFrameShadow(QFrame.Shadow.Plain)
        self.line_4.setLineWidth(2)
        self.line_4.setFrameShape(QFrame.Shape.HLine)

        self.verticalLayout.addWidget(self.line_4)

        self.label_4 = QLabel(self.styleTab)
        self.label_4.setObjectName(u"label_4")
        self.label_4.setMaximumSize(QSize(16777215, 30))
        self.label_4.setStyleSheet(u"QLabel{\n"
"\n"
"	color: #F9F9F9;\n"
"	font-family: inter;\n"
"	font-weight: bold;\n"
"	font-size: 16px;\n"
"}")

        self.verticalLayout.addWidget(self.label_4)

        self.widget_11 = QWidget(self.styleTab)
        self.widget_11.setObjectName(u"widget_11")
        self.horizontalLayout_11 = QHBoxLayout(self.widget_11)
        self.horizontalLayout_11.setObjectName(u"horizontalLayout_11")
        self.comboBoxDataSelectionForPlotRange = QComboBox(self.widget_11)
        self.comboBoxDataSelectionForPlotRange.setObjectName(u"comboBoxDataSelectionForPlotRange")
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.comboBoxDataSelectionForPlotRange.sizePolicy().hasHeightForWidth())
        self.comboBoxDataSelectionForPlotRange.setSizePolicy(sizePolicy)
        self.comboBoxDataSelectionForPlotRange.setMinimumSize(QSize(0, 45))
        self.comboBoxDataSelectionForPlotRange.setMaximumSize(QSize(1667125, 45))
        self.comboBoxDataSelectionForPlotRange.setStyleSheet(u"/* ===== Core ComboBox ===== */\n"
"QComboBox {\n"
"    border: 1px solid #303030;\n"
"    border-radius: 10px;\n"
"    padding: 4px 15px;\n"
"    background: #1C1C1C;\n"
"    color: #EEEEEE;\n"
"    font-size: 16px;\n"
"    /* Force non-native popup */\n"
"    combobox-popup: 0;\n"
"}\n"
"\n"
"/* ===== Dropdown Container Hack ===== */\n"
"QComboBox QFrame {\n"
"    border: none !important; /* Target Qt's internal container */\n"
"    background-color: #2a2a2a;\n"
"    margin: 0;\n"
"    padding: 0;\n"
"    border-radius: 10px;\n"
"}\n"
"\n"
"/* ===== Dropdown Arrow ===== */\n"
"QComboBox::drop-down {\n"
"    border: none;\n"
"    background: transparent;\n"
"    width: 20px;\n"
"    margin-right: 8px;\n"
"}\n"
"\n"
"QComboBox::down-arrow {\n"
"    image: url(\"assets/down-arrow.png\");\n"
"    width: 24px;\n"
"    height: 24px;\n"
"}\n"
"\n"
"\n"
"/* ===== Dropdown List ===== */\n"
"QComboBox QAbstractItemView {\n"
"    border: none;\n"
"    background-color: #2a2a2a;\n"
"    margin: 5px; /* Inner spacing */\n"
""
                        "    padding: 0;\n"
"    border-radius: 10px;\n"
"    /* Remove focus rectangle */\n"
"    outline: 0;\n"
"}\n"
"\n"
"/* ===== Scroll Area ===== */\n"
"QComboBox QScrollArea {\n"
"    border: none;\n"
"    background: transparent;\n"
"}\n"
"\n"
"QComboBox QScrollArea QWidget {\n"
"    background: transparent;\n"
"    border: none;\n"
"}\n"
"\n"
"/* ===== Items ===== */\n"
"QComboBox QAbstractItemView::item {\n"
"    min-height: 30px;\n"
"    padding: 8px 12px;\n"
"    color: white;\n"
"    border: none;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView::item:hover {\n"
"    background: #3d3d3d;\n"
"    border-radius: 5px;\n"
"}\n"
"\n"
"/* Selected item */\n"
"QComboBox QAbstractItemView::item:selected {\n"
"    background-color: #3d3d3d;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"/* Remove the default focus rectangle */\n"
"QComboBox:focus {\n"
"    border: none;\n"
"    outline: none;\n"
"}\n"
"\n"
"/* Hover state for the combobox */\n"
"QComboBox:hover {\n"
"    background-color: #252525;\n"
"}\n"
"\n"
"QComboBox:edit"
                        "able {\n"
"    background: #1C1C1C; /* Fix for editable QComboBox */\n"
"}\n"
"\n"
"/* Scrollbar styling */\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}\n"
"\n"
"/* Horizontal bar */\n"
"QScrollBar:horizontal {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizo"
                        "ntal:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:horizontal,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:horizontal,\n"
"QScrollBar::sub-page:horizontal {\n"
"    background: none;\n"
"}\n"
"\n"
"QFrame{\n"
"	border: none;\n"
"	background-color: #1c1c1c;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView: QFrame{\n"
"	border: none;\n"
"	background-color: #1c1c1c;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView:: QFrame{\n"
"	border: none;\n"
"	background-color: #1c1c1c;\n"
"}")

        self.horizontalLayout_11.addWidget(self.comboBoxDataSelectionForPlotRange)

        self.customDoubleSpinBoxRangeMin = CustomDoubleSpinBox(self.widget_11)
        self.customDoubleSpinBoxRangeMin.setObjectName(u"customDoubleSpinBoxRangeMin")
        sizePolicy.setHeightForWidth(self.customDoubleSpinBoxRangeMin.sizePolicy().hasHeightForWidth())
        self.customDoubleSpinBoxRangeMin.setSizePolicy(sizePolicy)
        self.customDoubleSpinBoxRangeMin.setMinimumSize(QSize(100, 45))
        self.customDoubleSpinBoxRangeMin.setMaximumSize(QSize(130, 16777215))
        self.customDoubleSpinBoxRangeMin.setStyleSheet(u"QDoubleSpinBox{\n"
"	border: 1px solid #303030;\n"
"    border-radius: 10px;\n"
"    padding: 4px 15px;\n"
"    background: #1C1C1C;\n"
"    color: #EEEEEE;\n"
"    font-size: 16px;\n"
"}")
        self.customDoubleSpinBoxRangeMin.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.customDoubleSpinBoxRangeMin.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.customDoubleSpinBoxRangeMin.setMaximum(10000000.000000000000000)
        self.customDoubleSpinBoxRangeMin.setSingleStep(0.000000000000000)

        self.horizontalLayout_11.addWidget(self.customDoubleSpinBoxRangeMin)

        self.customDoubleSpinBoxRangeMax = CustomDoubleSpinBox(self.widget_11)
        self.customDoubleSpinBoxRangeMax.setObjectName(u"customDoubleSpinBoxRangeMax")
        sizePolicy.setHeightForWidth(self.customDoubleSpinBoxRangeMax.sizePolicy().hasHeightForWidth())
        self.customDoubleSpinBoxRangeMax.setSizePolicy(sizePolicy)
        self.customDoubleSpinBoxRangeMax.setMinimumSize(QSize(100, 45))
        self.customDoubleSpinBoxRangeMax.setMaximumSize(QSize(130, 16777215))
        self.customDoubleSpinBoxRangeMax.setStyleSheet(u"QDoubleSpinBox{\n"
"	border: 1px solid #303030;\n"
"    border-radius: 10px;\n"
"    padding: 4px 15px;\n"
"    background: #1C1C1C;\n"
"    color: #EEEEEE;\n"
"    font-size: 16px;\n"
"}")
        self.customDoubleSpinBoxRangeMax.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.customDoubleSpinBoxRangeMax.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.customDoubleSpinBoxRangeMax.setMaximum(10000000.000000000000000)
        self.customDoubleSpinBoxRangeMax.setSingleStep(0.000000000000000)

        self.horizontalLayout_11.addWidget(self.customDoubleSpinBoxRangeMax)


        self.verticalLayout.addWidget(self.widget_11)

        self.frame = QFrame(self.styleTab)
        self.frame.setObjectName(u"frame")
        self.frame.setMinimumSize(QSize(0, 50))
        self.frame.setMaximumSize(QSize(16777215, 50))
        self.frame.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_10 = QHBoxLayout(self.frame)
        self.horizontalLayout_10.setObjectName(u"horizontalLayout_10")
        self.horizontalLayout_10.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_3 = QSpacerItem(391, 17, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_10.addItem(self.horizontalSpacer_3)

        self.pushButton_7 = QPushButton(self.frame)
        self.pushButton_7.setObjectName(u"pushButton_7")
        self.pushButton_7.setMinimumSize(QSize(100, 40))
        self.pushButton_7.setMaximumSize(QSize(100, 16777215))
        self.pushButton_7.setStyleSheet(u"QPushButton{\n"
"	background-color: #1c1c1c;\n"
"	color: white;\n"
"	font-family: inter;\n"
"	font-size: 14px;\n"
"	border-radius: 5px;\n"
"}")

        self.horizontalLayout_10.addWidget(self.pushButton_7)

        self.pushButton_8 = QPushButton(self.frame)
        self.pushButton_8.setObjectName(u"pushButton_8")
        self.pushButton_8.setMinimumSize(QSize(100, 40))
        self.pushButton_8.setMaximumSize(QSize(100, 16777215))
        self.pushButton_8.setStyleSheet(u"QPushButton{\n"
"	background-color: #4f39f8;\n"
"font-family: inter;\n"
"	font-size: 14px;\n"
"	border-radius: 5px;\n"
"}")

        self.horizontalLayout_10.addWidget(self.pushButton_8)


        self.verticalLayout.addWidget(self.frame)

        self.tabWidget.addTab(self.styleTab, "")

        self.gridLayout.addWidget(self.tabWidget, 0, 0, 1, 1)


        self.retranslateUi(Plot_settings_Dialog)

        self.tabWidget.setCurrentIndex(0)


        QMetaObject.connectSlotsByName(Plot_settings_Dialog)
    # setupUi

    def retranslateUi(self, Plot_settings_Dialog):
        Plot_settings_Dialog.setWindowTitle(QCoreApplication.translate("Plot_settings_Dialog", u"Plot Settings", None))
        self.label.setText(QCoreApplication.translate("Plot_settings_Dialog", u"Font Settings", None))
        self.lblTitleFont.setText(QCoreApplication.translate("Plot_settings_Dialog", u"Title Font", None))
        self.lnEdtTitleFontColorHex.setText(QCoreApplication.translate("Plot_settings_Dialog", u"#008000", None))
        self.btnTitleFontColorPallet.setText("")
        self.lnEdtLegendFontColorHex.setText(QCoreApplication.translate("Plot_settings_Dialog", u"#008000", None))
        self.btnLegendFontColorPallet.setText("")
        self.lblLabelFont.setText(QCoreApplication.translate("Plot_settings_Dialog", u"Label Font", None))
        self.lnEdtLabelFontColorHex.setText(QCoreApplication.translate("Plot_settings_Dialog", u"#008000", None))
        self.btnLabelFontColorPallet.setText("")
        self.lblLegendFont.setText(QCoreApplication.translate("Plot_settings_Dialog", u"Legend Font", None))
        self.label_2.setText(QCoreApplication.translate("Plot_settings_Dialog", u"Data Settings", None))
        self.lblDataSelection.setText(QCoreApplication.translate("Plot_settings_Dialog", u"Data Selection", None))
        self.lnEdtDataSelectionColorHex.setText(QCoreApplication.translate("Plot_settings_Dialog", u"#008000", None))
        self.btnDataSelectionColorPallet.setText("")
        self.lblLegendName.setText(QCoreApplication.translate("Plot_settings_Dialog", u"Legend Name", None))
        self.label_3.setText(QCoreApplication.translate("Plot_settings_Dialog", u"Grid Settings", None))
        self.checkBoxMajorGrid.setText("")
        self.lblMajorGrid.setText(QCoreApplication.translate("Plot_settings_Dialog", u"Major Grid", None))
        self.lnEdtMajorGridColorHex.setText(QCoreApplication.translate("Plot_settings_Dialog", u"#008000", None))
        self.btnMajorGridColorPallet.setText("")
        self.checkBoxMinorGrid.setText("")
        self.lblMinorGrid.setText(QCoreApplication.translate("Plot_settings_Dialog", u"Minor Grid", None))
        self.lnEdtMinorGridColorHex.setText(QCoreApplication.translate("Plot_settings_Dialog", u"#008000", None))
        self.btnMinorGridColorPallet.setText("")
        self.label_4.setText(QCoreApplication.translate("Plot_settings_Dialog", u"Plot Range Settings", None))
        self.pushButton_7.setText(QCoreApplication.translate("Plot_settings_Dialog", u"Cancel", None))
        self.pushButton_8.setText(QCoreApplication.translate("Plot_settings_Dialog", u"Confirm", None))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.styleTab), QCoreApplication.translate("Plot_settings_Dialog", u"Style", None))
    # retranslateUi

