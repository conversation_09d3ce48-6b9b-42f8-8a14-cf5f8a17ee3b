from PySide6.QtWidgets import QDialog

from gui.dialog import Ui_DataColumnSelectorDialog
from src.utils import get_resource_path


class TemperatureDataConfigDialog(QDialog):
    def __init__(self, columns, detected_scan_rate=None, parent=None):
        super().__init__(parent)
        self.ui = Ui_DataColumnSelectorDialog()
        self.ui.setupUi(self)

        # Sample column selection
        self.ui.selectSampleCombo.addItems(columns)

        # Temperature columns selection
        self.ui.temperatureColumnSelector.addItems(columns)

        # Scan rate input
        if detected_scan_rate is not None:
            self.ui.lnEdtScanRate.setText(str(detected_scan_rate))

        # Proceed button
        self.ui.proceedBtn.clicked.connect(self.accept)

    def setup_icons(self):
        """Setup icons for buttons"""
        down_arrow_path = get_resource_path("assets/down-arrow.png").replace('\\', '/')
        down_arrow_style = """
                                            QComboBox::down-arrow {
                                                image: url("%s");
                                                width: 24px;
                                                height: 24px;
                                            }
                                            QComboBox::drop-down {
                                                border: none;
                                                background: transparent;
                                                width: 20px;
                                                margin-right: 8px;
                                            }
                                        """ % down_arrow_path

        # Applying the style to all combo boxes
        combo_boxes = [
            self.ui.selectSampleCombo,
            self.ui.temperatureColumnSelector
        ]

        for combo_box in combo_boxes:
            current_style = combo_box.styleSheet()
            combo_box.setStyleSheet(current_style + down_arrow_style)

    def get_sample_column(self):
        return self.ui.selectSampleCombo.currentText()

    def get_temperature_columns(self):
        try:
            # Make sure we're calling the method correctly with a dot
            selected = self.ui.temperatureColumnSelector.getCheckedItems()
            return selected
        except Exception as e:
            print(f"Error getting temperature columns: {str(e)}")
            # Fallback: return all columns as selected
            return [self.ui.temperatureColumnSelector.itemText(i)
                    for i in range(self.ui.temperatureColumnSelector.count())]

    def get_scan_rate(self):
        try:
            return float(self.ui.lnEdtScanRate.text())
        except ValueError:
            return None