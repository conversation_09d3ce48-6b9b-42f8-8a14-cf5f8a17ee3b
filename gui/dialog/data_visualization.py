from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QIcon
from PySide6.QtWidgets import QVBoxLayout, QScrollArea, QWidget, QPushButton, QTableWidget, QLabel, \
    QTableWidgetItem, QHBoxLayout

from src.database import <PERSON>Handler, DatabaseConfig
from src.utils import get_resource_path, open_pdf


class DataVisualizationWidget(QWidget):
    def __init__(self, test_no: int, parent=None):
        super().__init__(parent)
        self.test_no = test_no
        self.db_handler = DatabaseHandler(DatabaseConfig)
        self.setup_ui()

    def setup_ui(self):

        self.setStyleSheet("""
                    QDialog {
                        background-color: #1e1e1e;  /* or any color you want */
                        border-radius: 10px;      /* adjust radius for roundness */
                        border: 2px solid #888;   /* optional border */
                    }

                    QComboBox {
                        background-color: #333333;
                        color: white;
                        padding: 5px;
                        border: 1px solid #446699;
                        min-width: 150px;
                    }
                    QComboBox::drop-down {
                        border: none;
                    }
                    QComboBox::down-arrow {
                        image: none;
                        border-width: 0px;
                    }
                    QScrollArea {
                        background-color: #1e1e1e;
                        border: none;
                    }
                    /* Scrollbar styling */
QScrollBar:vertical {
    border: none;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}

QScrollBar::handle:vertical {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background: none;
}
                    QLabel {
                        color: white;
                    }
                    QPushButton {
                        background-color: #446699;
                        color: white;
                        padding: 8px 16px;
                        border-radius: 4px;
                        min-width: 80px;
                    }
                    QPushButton:hover {
                        background-color: #557799;
                    }
                """)

        # Main layout
        self.layout = QVBoxLayout(self)

        # Get test numbers and verify
        test_numbers = self.db_handler.get_all_test_numbers()

        # Scroll area for tables
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll_content = QWidget()
        self.scroll_layout = QVBoxLayout(scroll_content)
        scroll.setWidget(scroll_content)
        self.layout.addWidget(scroll)

        # Style for section headers
        self.header_style = """
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #FFFFFF;
                background-color: #47a08e;
                padding: 5px;
                border-radius: 4px;
            }
        """

        self.scroll_content_style = """

        QWidget{
            background-color:#1e1e1e;
        }

        /* Scrollbar styling */
            QScrollBar:vertical {
                border: none;
                width: 10px;
                margin: 0px;
            	border-radius: 5px;
            }

QScrollBar::handle:vertical {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background: none;
}
        """

        # Style for tables
        self.table_style = """
            QTableWidget {
                background-color: #2b2b2b;
                color: white;
                gridline-color: #404040;
            }
            QHeaderView::section {
                background-color: #404040;
                color: white;
                padding: 4px;
                border: 1px solid #505050;
            }
            QTableWidget::item {
                padding: 4px;
                border-bottom: 1px solid #404040;
            }

            /* Scrollbar styling */
QScrollBar:vertical {
    border: none;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}

QScrollBar::handle:vertical {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background: none;
}
        """

        self.comboBox = """
            QComboBox{
	            background-color: #22324c;
	            color: white;
	            padding:5px;
	            font-size:15px;
	            border:1px solid #446699;
            }

            QComboBox QAbstractItemView {
                /* Dropdown menu styling */
                background-color: black;
                border: 1px solid #ccc;
                selection-background-color: #0078d7;
                selection-color: white;
            }
        """

        scroll_content.setStyleSheet(self.scroll_content_style)

        # Load test data directly
        self.load_test_data(self.test_no)

    def create_section_header(self, text):
        header = QLabel(text)
        header.setStyleSheet(self.header_style)
        return header

    def create_table_from_dict(self, data_dict):
        table = QTableWidget()
        table.setStyleSheet(self.table_style)
        table.setColumnCount(2)
        table.setRowCount(len(data_dict))
        table.setHorizontalHeaderLabels(["Parameter", "Value"])

        for i, (key, value) in enumerate(data_dict.items()):
            key_item = QTableWidgetItem(str(key).replace('_', ' ').title())
            value_item = QTableWidgetItem(str(value))
            table.setItem(i, 0, key_item)
            table.setItem(i, 1, value_item)

        table.resizeColumnsToContents()
        table.setMinimumHeight(min(400, (table.rowHeight(0) * (len(data_dict) + 1))))

        return table

    def create_heater_cycles_table(self, cycles_data):
        table = QTableWidget()
        table.setStyleSheet(self.table_style)
        headers = ["Cycle", "Switch On", "Switch Off", "Max Temp", "Location", "Tank Bottom Temp"]
        table.setColumnCount(len(headers))
        table.setRowCount(len(cycles_data))
        table.setHorizontalHeaderLabels(headers)

        for i, cycle in enumerate(cycles_data):
            table.setItem(i, 0, QTableWidgetItem(str(i + 1)))
            table.setItem(i, 1, QTableWidgetItem(str(cycle.get('switch_on', ''))))
            table.setItem(i, 2, QTableWidgetItem(str(cycle.get('switch_off', ''))))
            table.setItem(i, 3, QTableWidgetItem(str(cycle.get('max_temp', ''))))
            table.setItem(i, 4, QTableWidgetItem(str(cycle.get('max_temp_location', ''))))
            table.setItem(i, 5, QTableWidgetItem(str(cycle.get('tank_bottom_temp', ''))))

        table.resizeColumnsToContents()
        return table

    def show_current_test_number(self, test_no):
        # Test selection
        test_selection_layout = QHBoxLayout()
        test_label = QLabel("Current Test Number:")
        self.test_number_label = QLabel(test_no)

        test_selection_layout.addWidget(test_label)
        test_selection_layout.addWidget(self.test_number_label)
        test_selection_layout.addStretch()
        self.layout.addLayout(test_selection_layout)

    def load_test_data(self, test_no):
        if not test_no:
            return

        self.show_current_test_number(test_no)

        print(f"Loading data for test: {test_no}")  # Debug print

        # Clear existing content
        while self.scroll_layout.count():
            child = self.scroll_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        # Getting test data
        test_data = self.db_handler.get_test_data(test_no)
        if not test_data:
            print(f"No data found for test: {test_no}")  # Debug print
            return

        # Adding sections
        sections = [
            ("Basic Information", test_data.get('basic_info', {})),
            ("System Specifications", test_data.get('system_specs', {})),
            ("Propellant Specifications", test_data.get('propellant_specs', {})),
            ("Catalyst Specifications", test_data.get('catalyst_specs', {})),
            ("Component Details", test_data.get('component_details', {})),
            ("Test Details", test_data.get('test_details', {})),
            ("Heater Information", test_data.get('heater_info', {})),
            ("Post Test Observations", test_data.get('post_test_observations', {})),
            ("Catalyst Post Analysis", test_data.get('catalyst_post_analysis', {})),
            ("Propellant Post Analysis", test_data.get('propellant_post_analysis', {})),
            ("System Performance", test_data.get('performance_data', {}))
        ]

        for section_title, section_data in sections:
            if section_data:
                self.scroll_layout.addWidget(self.create_section_header(section_title))
                table = self.create_table_from_dict(section_data)
                self.scroll_layout.addWidget(table)
                self.scroll_layout.addSpacing(20)

        # Add heater cycles section
        heater_cycles = test_data.get('heater_cycles', [])
        if heater_cycles:
            self.scroll_layout.addWidget(self.create_section_header("Heater Cycles"))
            cycles_table = self.create_heater_cycles_table(heater_cycles)
            self.scroll_layout.addWidget(cycles_table)
            self.scroll_layout.addSpacing(20)

        # Add note section if exists
        note = test_data.get('note')
        if note:
            self.scroll_layout.addWidget(self.create_section_header("Notes"))
            note_label = QLabel(note)
            note_label.setWordWrap(True)
            note_label.setStyleSheet("color: white; padding: 10px;")
            self.scroll_layout.addWidget(note_label)

        self.scroll_layout.addStretch()

        # Adding the PDF Report at the end
        report = self.db_handler.get_test_report(test_data['test_id'])
        pdf_button = QPushButton('Show Report')
        pdf_button.clicked.connect(lambda: open_pdf(self, report))
        self.scroll_layout.addWidget(pdf_button)

        pdf_icon = QIcon()
        pdf_icon_path = get_resource_path("assets/pdf.png")
        pdf_icon.addFile(pdf_icon_path, QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        pdf_button.setIcon(pdf_icon)
        pdf_button.setIconSize(QSize(70, 70))