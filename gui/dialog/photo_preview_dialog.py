from PySide6.QtCore import Qt, QSize, Signal
from PySide6.QtGui import QPixmap, QIcon
from PySide6.QtWidgets import QMessageBox, QPushButton, QLabel, QScrollArea, QVBoxLayout, QDialog

# from gui_main import MainWindow
from src.utils import get_resource_path


class PhotoPreviewDialog(QDialog):
    image_deleted = Signal(str)  # Signal emits the photo_id when image is deleted

    def __init__(self, image_path, photo_id, parent=None):
        super().__init__(parent)
        self.image_path = image_path
        self.photo_id = photo_id
        self.setWindowTitle("Photo Preview")
        self.setModal(True)

        # self.mainWindow = MainWindow()

        layout = QVBoxLayout(self)

        # Scroll area for the image
        scroll = QScrollArea(self)
        scroll.setWidgetResizable(True)
        self.image_label = QLabel()
        scroll.setWidget(self.image_label)
        layout.addWidget(scroll)

        # Delete button
        delete_btn = QPushButton()
        delete_btn.setStyleSheet("background-color: transparent")
        delete_btn.setFixedSize(30, 30)
        delete_btn.clicked.connect(self.confirm_delete)

        delete_btn_icon = QIcon()
        delete_btn_icon_path = get_resource_path("assets/delete.png")
        delete_btn_icon.addFile(delete_btn_icon_path, QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        delete_btn.setIcon(delete_btn_icon)
        delete_btn.setIconSize(QSize(30, 30))

        layout.addWidget(delete_btn)

        self.load_image()

    def load_image(self):
        pixmap = QPixmap(self.image_path)
        if pixmap.isNull():
            self.image_label.setText("Failed to load image.")
            return
        max_size = 800
        if pixmap.width() > max_size or pixmap.height() > max_size:
            pixmap = pixmap.scaled(max_size, max_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        self.image_label.setPixmap(pixmap)

    def confirm_delete(self):
        reply = QMessageBox.question(
            self, 'Delete Image', 'Are you sure you want to delete this image?',
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            try:
                # os.remove(self.image_path)
                self.image_deleted.emit(self.photo_id)
                self.accept()
            except Exception as e:
                QMessageBox.critical(self, 'Error', f'Could not delete image: {str(e)}')