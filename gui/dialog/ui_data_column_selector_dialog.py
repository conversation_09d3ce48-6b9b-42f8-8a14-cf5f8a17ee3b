# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'data_column_selector_dialoguKXsKP.ui'
##
## Created by: Qt User Interface Compiler version 6.8.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, QConicalGradient, QCursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QComboBox, QDialog, QFrame,
    QGridLayout, QHBoxLayout, QLabel, QLineEdit,
    QPushButton, QSizePolicy, QWidget)

from gui.checkableComboBox import CheckableComboBox

class Ui_DataColumnSelectorDialog(object):
    def setupUi(self, Dialog):
        if not Dialog.objectName():
            Dialog.setObjectName(u"Dialog")
        Dialog.resize(528, 269)
        Dialog.setStyleSheet(u"QDialog{\n"
"	background-color: #000000;\n"
"}\n"
"\n"
"QLabel{\n"
"	color: #AAAAAA;\n"
"font-size: 16px;\n"
"font-family: inter;\n"
"	background-color: transparent;\n"
"}\n"
"\n"
"QLineEdit{\n"
"	background-color: #1C1C1C;\n"
"	border: 1px solid #303030;\n"
"	border-radius: 10px;\n"
"	color: #EEEEEE;\n"
"	font-size: 16px;\n"
"	padding: 4px 15px;\n"
"}\n"
"\n"
"\n"
"QComboBox {\n"
"    border: 1px solid #303030;\n"
"    border-radius: 10px;\n"
"    padding: 4px 10px;\n"
"    background-color: #1C1C1C;\n"
"    color: #EEEEEE;\n"
"    min-width: 6em;\n"
"	font-size: 16px;\n"
"}\n"
"\n"
"QComboBox::drop-down {\n"
"    border: none;\n"
"    background: transparent;\n"
"	width: 20px;\n"
"	margin-right: 8px;\n"
"}\n"
"\n"
"QComboBox::down-arrow {\n"
"    image: url(\"D:/SoftwareDevelopment/User_verification_Email/assets/down-arrow.png\");  /* You can use a custom arrow image */\n"
"    width: 24px;\n"
"    height: 24px;\n"
"}\n"
"\n"
"\n"
"/* Dropdown list styling */\n"
"QComboBox QAbstractItemView {\n"
"    border:"
                        " none;\n"
"	margin: 5px;\n"
"    border-radius: 10px;\n"
"    padding: 5px 0px;\n"
"    background-color: #2a2a2a;\n"
"    color: white;\n"
"    selection-background-color: #3a3a3a;  /* Darker selection color */\n"
"    outline: 0px;  /* Remove focus border */\n"
"}\n"
"\n"
"/* Individual item styling */\n"
"QComboBox QAbstractItemView::item {\n"
"    padding: 8px 12px;  /* Vertical and horizontal padding for items */\n"
"    border: none;\n"
"    min-height: 24px;  /* Minimum height for items */\n"
"}\n"
"\n"
"/* Hover state for items */\n"
"QComboBox QAbstractItemView::item:hover {\n"
"    background-color: #3d3d3d;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"/* Selected item */\n"
"QComboBox QAbstractItemView::item:selected {\n"
"    background-color: #3d3d3d;\n"
"}\n"
"\n"
"/* Remove the default focus rectangle */\n"
"QComboBox:focus {\n"
"    border: none;\n"
"    outline: none;\n"
"}\n"
"\n"
"/* Hover state for the combobox */\n"
"QComboBox:hover {\n"
"    background-color: #252525;\n"
"}\n"
"\n"
"/* Scrollb"
                        "ar styling */\n"
"QScrollBar:vertical {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical,\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical,\n"
"QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}\n"
"\n"
"/* Horizontal bar */\n"
"QScrollBar:horizontal {\n"
"    border: none;\n"
"    width: 10px;\n"
"    margin: 0px;\n"
"	border-radius: 5px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal {\n"
"    background-color: #bdbdbd;\n"
"    border-radius: 3px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::handle:horizontal:hover {\n"
"    background-color: #9e9e9e;\n"
"}\n"
"\n"
"QScrollBar::add-line:horizontal"
                        ",\n"
"QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar::add-page:horizontal,\n"
"QScrollBar::sub-page:horizontal {\n"
"    background: none;\n"
"}")
        Dialog.setSizeGripEnabled(False)
        self.gridLayout = QGridLayout(Dialog)
        self.gridLayout.setObjectName(u"gridLayout")
        self.gridLayout.setHorizontalSpacing(8)
        self.gridLayout.setVerticalSpacing(0)
        self.gridLayout.setContentsMargins(15, -1, 15, 0)
        self.temperatureColumnSelector = CheckableComboBox(Dialog)
        self.temperatureColumnSelector.setObjectName(u"temperatureColumnSelector")

        self.gridLayout.addWidget(self.temperatureColumnSelector, 1, 1, 1, 1)

        self.selectSampleCombo = QComboBox(Dialog)
        self.selectSampleCombo.setObjectName(u"selectSampleCombo")
        self.selectSampleCombo.setMinimumSize(QSize(148, 0))
        self.selectSampleCombo.setStyleSheet(u"")

        self.gridLayout.addWidget(self.selectSampleCombo, 0, 1, 1, 1)

        self.frame = QFrame(Dialog)
        self.frame.setObjectName(u"frame")
        self.frame.setMaximumSize(QSize(16777215, 35))
        self.frame.setStyleSheet(u"QFrame{\n"
"	background-color: transparent;\n"
"}")
        self.frame.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout = QHBoxLayout(self.frame)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalLayout.setContentsMargins(0, 0, -1, 0)
        self.proceedBtn = QPushButton(self.frame)
        self.proceedBtn.setObjectName(u"proceedBtn")
        self.proceedBtn.setMinimumSize(QSize(0, 30))
        self.proceedBtn.setMaximumSize(QSize(150, 16777215))
        self.proceedBtn.setStyleSheet(u"QPushButton{\n"
"	background-color:rgb(255, 52, 20);\n"
"	border-radius:6px;\n"
"	padding:5px;\n"
"	font-size: 17px;\n"
"	font-family: Arial;\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	background-color:#b089a1;\n"
"}\n"
"\n"
"QPushButton:pressed{\n"
"	background-color:b03781;\n"
"}")

        self.horizontalLayout.addWidget(self.proceedBtn)


        self.gridLayout.addWidget(self.frame, 4, 1, 1, 1)

        self.label_3 = QLabel(Dialog)
        self.label_3.setObjectName(u"label_3")

        self.gridLayout.addWidget(self.label_3, 3, 0, 1, 1)

        self.label_2 = QLabel(Dialog)
        self.label_2.setObjectName(u"label_2")

        self.gridLayout.addWidget(self.label_2, 1, 0, 1, 1)

        self.label = QLabel(Dialog)
        self.label.setObjectName(u"label")

        self.gridLayout.addWidget(self.label, 0, 0, 1, 1)

        self.frame_2 = QFrame(Dialog)
        self.frame_2.setObjectName(u"frame_2")
        self.frame_2.setMaximumSize(QSize(16777215, 42))
        self.frame_2.setStyleSheet(u"QFrame{\n"
"background-color: transparant;\n"
"}")
        self.frame_2.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_2.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_2 = QHBoxLayout(self.frame_2)
        self.horizontalLayout_2.setSpacing(0)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.lnEdtScanRate = QLineEdit(self.frame_2)
        self.lnEdtScanRate.setObjectName(u"lnEdtScanRate")
        self.lnEdtScanRate.setMaximumSize(QSize(100, 16777215))
        self.lnEdtScanRate.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_2.addWidget(self.lnEdtScanRate)


        self.gridLayout.addWidget(self.frame_2, 3, 1, 1, 1)


        self.retranslateUi(Dialog)

        QMetaObject.connectSlotsByName(Dialog)
    # setupUi

    def retranslateUi(self, Dialog):
        Dialog.setWindowTitle(QCoreApplication.translate("Dialog", u"Configure Temperature Data", None))
        self.proceedBtn.setText(QCoreApplication.translate("Dialog", u"Proceed", None))
        self.label_3.setText(QCoreApplication.translate("Dialog", u"Scan Rate (Samples per second):", None))
        self.label_2.setText(QCoreApplication.translate("Dialog", u"Select Temperature Columns:", None))
        self.label.setText(QCoreApplication.translate("Dialog", u"Select Sample Column:", None))
    # retranslateUi

