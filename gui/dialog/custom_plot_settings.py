import os

import numpy as np
from PySide6 import QtGui, QtWidgets
from PySide6.QtGui import QIcon
from matplotlib.backends.backend_qt5agg import NavigationToolbar2QT as NavigationToolbar
from PySide6.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QDialog, QPushButton, QColorDialog, QFileDialog
from PySide6.QtCore import Qt, QSize
from matplotlib.font_manager import FontProperties

from .ui_plot_settings import Ui_Plot_settings_Dialog
from src.utils import get_resource_path


class CustomNavigationToolbar(NavigationToolbar):
    def __init__(self, canvas, parent=None, data_series=None):
        super(CustomNavigationToolbar, self).__init__(canvas, parent)
        self.data_series = data_series or {}
        self.canvas = canvas
        self.parent = parent
        self.settings = {}  # Initializing settings dictionary

        # Set icon size for the entire toolbar
        self.setIconSize(QSize(24, 24))

        # Replace default navigation icons with custom ones
        self.setup_custom_icons()

        # Adding a custom button
        self.addSeparator()
        settings_btn = QPushButton()
        settings_icon = QIcon()
        settings_icon_path = get_resource_path("assets/advanced_settings.png")
        settings_icon.addFile(settings_icon_path, QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        settings_btn.setIcon(settings_icon)
        settings_btn.setIconSize(QSize(25, 25))
        settings_btn.setToolTip('Plot Settings')
        settings_btn.setCursor(Qt.PointingHandCursor)

        settings_btn.clicked.connect(self.open_plot_settings)
        self.addWidget(settings_btn)

        # Define the stylesheet (matching PlotSettingsDialog)
        self.stylesheet = """
                QDialog {
                    background-color: #171717;
                }
                QLabel {
                    color: black;
                    padding: 1px 50px;
                    font-family: Arial;
                    font-size: 14px;
                }
                QLineEdit {
                    background-color: #1C1C1C;
                    border: 1px solid #303030;
                    border-radius: 10px;
                    color: #EEEEEE;
                    font-size: 16px;
                    padding: 4px 15px;
                }
                QComboBox {
                    border: 1px solid #303030;
                    border-radius: 10px;
                    padding: 4px 15px;
                    background: #1C1C1C;
                    color: #EEEEEE;
                    font-size: 16px;
                }
                QComboBox::drop-down {
                    border: none;
                    background: transparent;
                    width: 20px;
                    margin-right: 8px;
                }
                QComboBox::down-arrow {
                    image: url("D:/SoftwareDevelopment/User_verification_Email/assets/down-arrow.png");
                    width: 24px;
                    height: 24px;
                }
                QCheckBox {
                    color: #444444;
                }
                QCheckBox::indicator {
                    width: 18px;
                    height: 18px;
                }
                QCheckBox::indicator:checked {
                    image: url("D:/SoftwareDevelopment/User_verification_Email/assets/checkbox_checked.png");
                }
                QCheckBox::indicator:unchecked {
                    image: url("D:/SoftwareDevelopment/User_verification_Email/assets/checkbox_unchecked.png");
                }
                QTabWidget {
    background-color: transparent;
    border: none;
    border-radius: 10px;
}

QWidget {
    background-color: transparent;
}

QTabWidget::tab-bar {
    alignment: center;
}

QTabBar::tab {
    border-radius: 5px;
    width: 120px;
    height: 20px;
    color: black;
    font-size: 16px;
    font-family: inter;
    padding: 2px;
    background-color: #ffffff;  /* Optional: White background for tabs */
}

QTabBar::tab:selected {
    background: black;
    color: white;
}

QTabBar::tab:hover {
    background: #787878;
}

QTabWidget::pane {  /* Target the content area frame */
    border: none;  /* Remove border */
    background-color: transparent;  /* Make background transparent */
    border-radius: 10px;  /* Match the tab widget's border radius */
}
                """

        # Apply stylesheet to the toolbar itself
        self.setStyleSheet(self.stylesheet)


    def edit_parameters(self):
        super().edit_parameters()
        dialog = self._get_active_dialog()
        if dialog:
            dialog.setStyleSheet(
                """
                                QDialog {
                                    background-color: #171717;
                                }
                                QWidget {
                                    background-color: #171717;
                                    color: #EEEEEE;
                                }
                                QPushButton {
                                    background-color: #1C1C1C;
                                    color: white;
                                    font-family: inter;
                                    font-size: 14px;
                                    border-radius: 5px;
                                }
                                QPushButton:hover {
                                    background-color: #252525;
                                }
                                QLineEdit {
                                    background-color: #1C1C1C;
                                    border: 1px solid #303030;
                                    border-radius: 10px;
                                    color: #EEEEEE;
                                    font-size: 16px;
                                    padding: 4px 15px;
                                }
                                QComboBox {
                                    border: 1px solid #303030;
                                    border-radius: 10px;
                                    padding: 4px 15px;
                                    background: #1C1C1C;
                                    color: #EEEEEE;
                                    font-size: 16px;
                                }
                                QComboBox::drop-down {
                                    border: none;
                                    background: transparent;
                                    width: 20px;
                                    margin-right: 8px;
                                }
                                QComboBox::down-arrow {
                                    image: url("D:/SoftwareDevelopment/User_verification_Email/assets/down-arrow.png");
                                    width: 24px;
                                    height: 24px;
                                }
                                QCheckBox {
                                    color: #444444;
                                }
                                QCheckBox::indicator {
                                    width: 18px;
                                    height: 18px;
                                }
                                QCheckBox::indicator:checked {
                                    image: url("D:/SoftwareDevelopment/User_verification_Email/assets/checkbox_checked.png");
                                }
                                QCheckBox::indicator:unchecked {
                                    image: url("D:/SoftwareDevelopment/User_verification_Email/assets/checkbox_unchecked.png");
                                }

                                QTabWidget{
    background-color: #171717;

}

QTabWidget::tab-bar{
alignment: center;
}

QTabBar::tab{
border-radius: 5px;
width: 60px;
height: 20px;
color: white;
font-size: 16px;
font-family: inter;
padding: 2px;
background-color: transparent;
}

QTabBar::tab:selected{

background: white;
color: black;

}

QTabBar::tab:hover {
                background: #787878;
            }

             QLabel{
                    color: #AAAAAA;
                    font-size: 16px;
                    font-family: inter;
                }
                                """

            )

    def configure_subplots(self):
        super().configure_subplots()
        dialog = self._get_active_dialog()
        if dialog:
            dialog.setStyleSheet(
                """
                                                    QDialog {
                                                        background-color: #171717;

                                                    }
                                                    QWidget {
                                                        background-color: #171717;
                                                        color: #EEEEEE;
                                                    }
                                                    QPushButton {
                                                        background-color: #171717;
                                                        color: white;
                                                        font-family: inter;
                                                        font-size: 14px;
                                                        border-radius: 5px;
                                                    }
                                                    QPushButton:hover {
                                                        background-color: #252525;
                                                    }
                                                    QLineEdit {
                                                        background-color: #1C1C1C;
                                                        border: 1px solid #303030;
                                                        border-radius: 10px;
                                                        color: #EEEEEE;
                                                        font-size: 16px;
                                                        padding: 4px 15px;
                                                    }
                                                    QComboBox {
                                                        border: 1px solid #303030;
                                                        border-radius: 10px;
                                                        padding: 4px 15px;
                                                        background: #1C1C1C;
                                                        color: #EEEEEE;
                                                        font-size: 16px;
                                                    }
                                                    QComboBox::drop-down {
                                                        border: none;
                                                        background: transparent;
                                                        width: 20px;
                                                        margin-right: 8px;
                                                    }
                                                    QComboBox::down-arrow {
                                                        image: url("D:/SoftwareDevelopment/User_verification_Email/assets/down-arrow.png");
                                                        width: 24px;
                                                        height: 24px;
                                                    }

                                                    QDoubleSpinBox {
border: 1px solid #303030;
border-radius: 10px;
padding: 4px 15px;
background: #1C1C1C;
color: #EEEEEE;
font-size: 16px;
}

QDoubleSpinBox::up-button, QDoubleSpinBox::down-button {
border: none;
background: transparent;
width: 20px;
margin-right: 8px;
}

QDoubleSpinBox::up-arrow {
image: url("D:/SoftwareDevelopment/User_verification_Email/assets/up-arrow.png");
width: 24px;
height: 24px;
}

QDoubleSpinBox::down-arrow {
image: url("D:/SoftwareDevelopment/User_verification_Email/assets/down-arrow.png");
width: 24px;
height: 24px;
}

                                                    QCheckBox {
                                                        color: #444444;
                                                    }
                                                    QCheckBox::indicator {
                                                        width: 18px;
                                                        height: 18px;
                                                    }
                                                    QCheckBox::indicator:checked {
                                                        image: url("D:/SoftwareDevelopment/User_verification_Email/assets/checkbox_checked.png");
                                                    }
                                                    QCheckBox::indicator:unchecked {
                                                        image: url("D:/SoftwareDevelopment/User_verification_Email/assets/checkbox_unchecked.png");
                                                    }

                                                    QTabWidget{
                        background-color: #171717;

                }

                QTabWidget::tab-bar{
                    alignment: center;
                }

                QTabBar::tab{
                    border-radius: 5px;
                    width: 60px;
                    height: 20px;
                    color: white;
                    font-size: 16px;
                    font-family: inter;
                    padding: 2px;
                }

                QTabBar::tab:selected{

                    background: white;
                    color: black;

                }

                QTabBar::tab:hover {
                                    background: #787878;
                                }

                                                    """
            )

    def _get_active_dialog(self):
        for widget in QtWidgets.QApplication.topLevelWidgets():
            if isinstance(widget, QtWidgets.QDialog) and widget.isVisible():
                return widget
        return None

    def open_plot_settings(self):
        """Show the plot settings dialog"""
        self.settings_dialog = PlotSettingsDialog(self.parent, self.canvas.figure, self.data_series, self.settings)
        self.settings_dialog.show()

    def setup_custom_icons(self):
        """Replace default matplotlib toolbar icons with custom ones"""
        # Define icon mappings - action name to icon file
        icon_mapping = {
            'home': 'home.png',
            'back': 'back.png',
            'forward': 'forward.png',
            'pan': 'pan.png',
            'zoom': 'zoom.png',
            'save': 'save.png',
            'subplots': 'subplots.png'
        }

        # Replace icons for each action
        for action in self.actions():
            action_name = action.text().lower() if action.text() else ""

            # Skip separators and the custom settings button
            if not action_name or action_name == "separator":
                continue

            # Find matching icon name (handle partial matches)
            icon_name = None
            for key in icon_mapping:
                if key in action_name:
                    icon_name = icon_mapping[key]
                    break

            # If we found a matching icon, apply it
            if icon_name:
                icon_path = get_resource_path(f"assets/{icon_name}")
                if os.path.exists(icon_path):
                    # Create a QIcon with appropriate size
                    icon = QIcon(icon_path)
                    # Set the icon for the action
                    action.setIcon(icon)


class PlotSettingsDialog(QDialog, Ui_Plot_settings_Dialog):
    def __init__(self, parent, figure, data_series, settings):
        super().__init__(parent)
        self.setupUi(self)
        self.figure = figure
        self.ax = figure.gca()
        self.data_series = data_series
        self.settings = settings  # Storing the settings dictionary

        # Connect signals
        self.pushButton_8.clicked.connect(self.all_changes_confirmed)
        self.pushButton_7.clicked.connect(self.reject)

        # Setting the pointing cursor over the color pallet buttons
        self.btnLabelFontColorPallet.setCursor(Qt.PointingHandCursor)
        self.btnTitleFontColorPallet.setCursor(Qt.PointingHandCursor)
        self.btnLegendFontColorPallet.setCursor(Qt.PointingHandCursor)
        self.btnDataSelectionColorPallet.setCursor(Qt.PointingHandCursor)
        self.btnMajorGridColorPallet.setCursor(Qt.PointingHandCursor)
        self.btnMinorGridColorPallet.setCursor(Qt.PointingHandCursor)

        # Connect the editingFinished signal
        self.lnEdtTitleFontColorHex.editingFinished.connect(
            lambda: self.dynamicColorChange(self.lnEdtTitleFontColorHex.text()))

        # UI Element Initialization
        self.comboBoxTitleFontSize.addItems([str(i) for i in range(8, 25, 1)])
        self.comboBoxLabelFontSize.addItems([str(i) for i in range(8, 25, 1)])
        self.comboBoxLegendFontSize.addItems([str(i) for i in range(8, 25, 1)])

        self.btnTitleFontColorPallet.clicked.connect(
            lambda: self._pick_color(self.btnTitleFontColorPallet, 'title_color', self.lnEdtTitleFontColorHex))
        self.btnLabelFontColorPallet.clicked.connect(
            lambda: self._pick_color(self.btnLabelFontColorPallet, 'label_color', self.lnEdtLabelFontColorHex))
        self.btnLegendFontColorPallet.clicked.connect(
            lambda: self._pick_color(self.btnLegendFontColorPallet, 'legend_color', self.lnEdtLegendFontColorHex))
        self.btnDataSelectionColorPallet.clicked.connect(
            lambda: self._pick_color(self.btnDataSelectionColorPallet, 'data_series_color',
                                     self.lnEdtDataSelectionColorHex))
        self.btnMajorGridColorPallet.clicked.connect(
            lambda: self._pick_color(self.btnMajorGridColorPallet, 'major_grid_color', self.lnEdtMajorGridColorHex))
        self.btnMinorGridColorPallet.clicked.connect(
            lambda: self._pick_color(self.btnMinorGridColorPallet, 'minor_grid_color', self.lnEdtMinorGridColorHex))

        self.lnEdtLegendName.editingFinished.connect(self._update_legend_name)

        # Connect signals for grid checkboxes (real-time updates)
        self.checkBoxMajorGrid.stateChanged.connect(self._update_grids)
        self.checkBoxMinorGrid.stateChanged.connect(self._update_grids)

        # Connect combo box selection change to update color and hex code
        self.comboBoxDataSelection.currentTextChanged.connect(self._update_data_series_ui)

        # Initialize current values
        self._init_values()

        # Initialize setup icons
        self.setup_icons()

    def _init_values(self):
        """Initialize dialog with current plot values or settings"""
        # Initialize or update settings from the current plot state
        self._update_settings_from_plot()

        # Set UI elements based on settings
        # Title font
        title_font = self.settings.get('title_font', 'Arial')
        title_size = self.settings['title_size']
        title_color = self.settings.get('title_color', '#000000')

        # Use QFont to set the font in the combo box, ensuring compatibility
        font = QtGui.QFont()
        font.setFamily(title_font)
        self.comboBoxTitleFont.setCurrentFont(font)
        self.comboBoxTitleFontSize.setCurrentText(str(title_size))
        self.lnEdtTitleFontColorHex.setText(title_color)
        self.btnTitleFontColorPallet.setStyleSheet(f"background-color: {title_color};")

        # Label font
        label_font = self.settings.get('label_font', 'Arial')
        label_size = self.settings.get('label_size', 10)
        label_color = self.settings.get('label_color', '#000000')

        font.setFamily(label_font)
        self.comboBoxLabelFont.setCurrentFont(font)
        self.comboBoxLabelFontSize.setCurrentText(str(label_size))
        self.lnEdtLabelFontColorHex.setText(label_color)
        self.btnLabelFontColorPallet.setStyleSheet(f"background-color: {label_color};")

        # Legend font
        legend_font = self.settings.get('legend_font', 'Arial')
        legend_size = self.settings.get('legend_size', 10)
        legend_color = self.settings.get('legend_color', '#000000')

        font.setFamily(legend_font)
        self.comboBoxLegendFont.setCurrentFont(font)
        self.comboBoxLegendFontSize.setCurrentText(str(legend_size))
        self.lnEdtLegendFontColorHex.setText(legend_color)
        self.btnLegendFontColorPallet.setStyleSheet(f"background-color: {legend_color};")

        # Data series - Populate combo box with all series
        self.comboBoxDataSelection.clear()
        self.comboBoxDataSelectionForPlotRange.clear()
        for series in self.data_series.keys():
            legend_name = self.settings.get(f'legend_name_{series}', series)
            self.comboBoxDataSelection.addItem(legend_name)
            self.comboBoxDataSelectionForPlotRange.addItem(legend_name)

        # Set initial data series UI values (first series by default)
        if self.data_series:
            first_series = list(self.data_series.keys())[0]
            series_color = self.settings.get(f'data_series_color_{first_series}', '#008000')
            legend_name = self.settings.get(f'legend_name_{first_series}', first_series)
            self.lnEdtDataSelectionColorHex.setText(series_color)
            self.btnDataSelectionColorPallet.setStyleSheet(f"background-color: {series_color};")
            self.lnEdtLegendName.setText(legend_name)

        # Grid settings
        major_grid_visible = self.settings.get('major_grid_visible', False)
        minor_grid_visible = self.settings.get('minor_grid_visible', False)
        major_grid_color = self.settings.get('major_grid_color', '#008000')
        minor_grid_color = self.settings.get('minor_grid_color', '#008000')

        self.checkBoxMajorGrid.setChecked(major_grid_visible)
        self.checkBoxMinorGrid.setChecked(minor_grid_visible)
        self.lnEdtMajorGridColorHex.setText(major_grid_color)
        self.lnEdtMinorGridColorHex.setText(minor_grid_color)
        self.btnMajorGridColorPallet.setStyleSheet(f"background-color: {major_grid_color};")
        self.btnMinorGridColorPallet.setStyleSheet(f"background-color: {minor_grid_color};")

        # Add axes names to the plot range combo box
        self.comboBoxDataSelectionForPlotRange.clear()
        self.comboBoxDataSelectionForPlotRange.addItem("X-Axis")
        self.comboBoxDataSelectionForPlotRange.addItem("Y-Axis")

        # Initialize range values with current axis limits
        x_min, x_max = self.ax.get_xlim()
        y_min, y_max = self.ax.get_ylim()

        # Store current limits
        self.settings['x_axis_min'] = x_min
        self.settings['x_axis_max'] = x_max
        self.settings['y_axis_min'] = y_min
        self.settings['y_axis_max'] = y_max

        # Set initial values in the range spinboxes
        self.customDoubleSpinBoxRangeMin.setValue(x_min)
        self.customDoubleSpinBoxRangeMax.setValue(x_max)

        # Connect the axis selection change to update range values
        self.comboBoxDataSelectionForPlotRange.currentTextChanged.connect(self._update_range_spinboxes)

        # Connect range spinboxes to update preview
        self.customDoubleSpinBoxRangeMin.valueChanged.connect(self._update_plot_range)
        self.customDoubleSpinBoxRangeMax.valueChanged.connect(self._update_plot_range)

    def setup_icons(self):
        """Setup icons for buttons"""
        # Setting down arrow icon for all combo boxes
        down_arrow_path = get_resource_path("assets/down-arrow.png").replace('\\', '/')
        down_arrow_style = """
                                    QComboBox::down-arrow {
                                        image: url("%s");
                                        width: 24px;
                                        height: 24px;
                                    }
                                    QComboBox::drop-down {
                                        border: none;
                                        background: transparent;
                                        width: 20px;
                                        margin-right: 8px;
                                    }
                                """ % down_arrow_path

        # Applying the style to all combo boxes
        combo_boxes = [
            self.comboBoxDataSelection,
            self.comboBoxDataSelectionForPlotRange,
            self.comboBoxTitleFont,
            self.comboBoxLabelFont,
            self.comboBoxLegendFont,
            self.comboBoxTitleFontSize,
            self.comboBoxLabelFontSize,
            self.comboBoxLegendFontSize
        ]

        for combo_box in combo_boxes:
            current_style = combo_box.styleSheet()
            combo_box.setStyleSheet(current_style + down_arrow_style)

        # Setting checkbox icons
        checkbox_unchecked_path = get_resource_path("assets/checkbox_unchecked.png").replace('\\', '/')
        checkbox_checked_path = get_resource_path("assets/checkbox_checked.png").replace('\\', '/')
        checkbox_style = f"""
            QCheckBox::indicator {{
                width: 18px;
                height: 18px;
            }}
            QCheckBox::indicator:checked {{
                image: url("{checkbox_checked_path}");
            }}
            QCheckBox::indicator:unchecked {{
                image: url("{checkbox_unchecked_path}");
            }}
        """

        # Applying the style to all checkboxes
        checkboxes = [
            self.checkBoxMajorGrid,
            self.checkBoxMinorGrid
        ]

        for checkbox in checkboxes:
            current_style = checkbox.styleSheet()
            checkbox.setStyleSheet(current_style + checkbox_style)

    def _update_settings_from_plot(self):
        """Update the settings dictionary based on the current plot state"""
        # Title settings
        title = self.ax.title
        self.settings['title_font'] = title.get_fontname() or 'Arial'
        self.settings['title_size'] = int(
            title.get_fontsize() or 12)  # Ensure integer, default to 12 if None or invalid
        self.settings['title_color'] = title.get_color() or '#000000'

        # Label settings
        self.settings['label_font'] = self.ax.xaxis.get_label().get_fontname() or 'Arial'
        self.settings['label_size'] = int(
            self.ax.xaxis.get_label().get_fontsize() or 10)  # Ensure integer, default to 10
        self.settings['label_color'] = self.ax.xaxis.get_label().get_color() or '#000000'

        # Legend settings
        if self.ax.legend_ is not None:
            legend = self.ax.legend_
            for text in legend.get_texts():
                self.settings['legend_font'] = text.get_fontname() or 'Arial'
                self.settings['legend_size'] = int(text.get_fontsize() or 10)  # Ensure integer, default to 10
                self.settings['legend_color'] = text.get_color() or '#000000'
                break  # Assuming all legend texts share the same properties

        # Data series settings (iterate through lines for all series)
        for line in self.ax.get_lines():
            series_name = line.get_label()
            if series_name and series_name in self.data_series:
                self.settings[f'data_series_color_{series_name}'] = line.get_color() or '#008000'
                self.settings[f'legend_name_{series_name}'] = series_name  # Store current legend name

        # Grid settings
        self.settings['major_grid_visible'] = self.ax.xaxis._major_tick_kw.get('gridOn', False)
        self.settings['minor_grid_visible'] = self.ax.xaxis._minor_tick_kw.get('gridOn', False) if hasattr(
            self.ax.xaxis, '_minor_tick_kw') else False

        # Get major grid color (if visible and grid lines exist)
        if self.settings['major_grid_visible']:
            major_gridlines = self.ax.get_xgridlines() + self.ax.get_ygridlines()
            if major_gridlines and len(major_gridlines) > 0:
                major_lines = [line for line in major_gridlines if
                               line.get_linestyle() == '-' or line.get_linestyle() == 'solid']
                self.settings['major_grid_color'] = major_lines[0].get_color() if major_lines else '#008000'
            else:
                self.settings['major_grid_color'] = '#008000'  # Default if no gridlines found
        else:
            self.settings['major_grid_color'] = '#008000'  # Default if grid is not visible

        # Get minor grid color (if visible and grid lines exist)
        if self.settings['minor_grid_visible']:
            all_gridlines = self.ax.get_xgridlines() + self.ax.get_ygridlines()
            if all_gridlines and len(all_gridlines) > 0:
                minor_lines = [line for line in all_gridlines if
                               line.get_linestyle() in ['--', ':', 'dashed', 'dotted']]
                self.settings['minor_grid_color'] = minor_lines[0].get_color() if minor_lines else '#008000'
            else:
                self.settings['minor_grid_color'] = '#008000'  # Default if no gridlines found
        else:
            self.settings['minor_grid_color'] = '#008000'  # Default if grid is not visible


    def dynamicColorChange(self, color):
        self.btnTitleFontColorPallet.setStyleSheet(f"background-color: {color};")

    def _restore_original_plot(self):
        """Restore the original plot data for the selected series"""
        for line in self.ax.get_lines():
            if hasattr(line, 'set_data_orig'):
                line.set_data(*line.set_data_orig)
        self.figure.tight_layout()
        self.figure.canvas.draw()

    def reject(self):
        """Handle dialog rejection"""
        self._restore_original_plot()
        super().reject()

    def _update_data_series_ui(self):
        """Update the UI elements (color indicator and hex code) when a different data series is selected"""
        if not self.data_series:
            return

        # Get the currently selected series from the combo box
        selected_series = self._get_original_series_name(self.comboBoxDataSelection.currentText())
        if selected_series:
            # Update the color indicator and hex code based on the selected series
            series_color = self.settings.get(f'data_series_color_{selected_series}', '#008000')
            legend_name = self.settings.get(f'legend_name_{selected_series}', selected_series)
            self.lnEdtDataSelectionColorHex.setText(series_color)
            self.btnDataSelectionColorPallet.setStyleSheet(f"background-color: {series_color};")
            self.lnEdtLegendName.setText(legend_name)

    def _get_original_series_name(self, legend_name):
        """Map the legend name back to the original series name"""
        for series in self.data_series.keys():
            if self.settings.get(f'legend_name_{series}', series) == legend_name:
                return series
        return None

    def _update_legend_name(self):
        """Updates legend name"""
        current_legend_name = self.comboBoxDataSelection.currentText()
        series = self._get_original_series_name(current_legend_name)
        if not series:
            return

        new_name = self.lnEdtLegendName.text()
        color = self.lnEdtDataSelectionColorHex.text()

        for line in self.ax.get_lines():
            series_name = line.get_label()
            if series_name == series:
                line.set_color(color)
                line.set_label(new_name)

        # Update the combo box item text and settings
        self.comboBoxDataSelection.setItemText(self.comboBoxDataSelection.currentIndex(), new_name)
        self.settings[f'legend_name_{series}'] = new_name
        self.settings[f'data_series_color_{series}'] = color

        self.apply_changes()

    def _pick_color(self, button, setting_key, color_lnEdt):
        """Enhanced color picker that updates settings"""
        color = QColorDialog.getColor()

        if color.isValid():
            color_name = color.name()
            button.setStyleSheet(f"background-color: {color_name};")
            color_lnEdt.setText(color_name)

            # Update settings based on the setting_key
            if 'title_color' in setting_key:
                self.settings['title_color'] = color_name
            elif 'label_color' in setting_key:
                self.settings['label_color'] = color_name
            elif 'legend_color' in setting_key:
                self.settings['legend_color'] = color_name
            elif 'data_series_color' in setting_key:
                series = self._get_original_series_name(self.comboBoxDataSelection.currentText())
                if series:
                    self.settings[f'data_series_color_{series}'] = color_name
            elif 'major_grid_color' in setting_key:
                self.settings['major_grid_color'] = color_name
            elif 'minor_grid_color' in setting_key:
                self.settings['minor_grid_color'] = color_name

            self._update_plot_appearance()  # Apply changes immediately

    def _update_grids(self):
        """Update the plot's major and minor grids based on checkbox states"""
        major_visible = self.checkBoxMajorGrid.isChecked()
        minor_visible = self.checkBoxMinorGrid.isChecked()
        major_color = self.lnEdtMajorGridColorHex.text() or '#008000'
        minor_color = self.lnEdtMinorGridColorHex.text() or '#008000'

        # Update settings
        self.settings['major_grid_visible'] = major_visible
        self.settings['minor_grid_visible'] = minor_visible
        self.settings['major_grid_color'] = major_color
        self.settings['minor_grid_color'] = minor_color

        if major_visible:
            self.ax.grid(which='major', visible=True, color=major_color)
        else:
            self.ax.grid(which='major', visible=False)

        if minor_visible:
            self.ax.grid(which='minor', visible=True, color=minor_color)
            self.ax.minorticks_on()
        else:
            self.ax.grid(which='minor', visible=False)
            self.ax.minorticks_off()
        self.figure.tight_layout()
        self.figure.canvas.draw()

    def _update_plot_appearance(self):
        """Apply current settings to the plot"""
        try:
            # Update title
            self.ax.title.set_fontname(self.settings.get('title_font', 'Arial'))
            self.ax.title.set_fontsize(self.settings.get('title_size', 12))
            self.ax.title.set_color(self.settings.get('title_color', '#000000'))

            # Update labels
            for label, axis in [(self.ax.get_xlabel(), self.ax.xaxis), (self.ax.get_ylabel(), self.ax.yaxis)]:
                axis.set_label_text(label,
                                    fontsize=self.settings.get('label_size', 10),
                                    color=self.settings.get('label_color', '#000000'),
                                    fontname=self.settings.get('label_font', 'Arial'))

            # Update legend - Fix the font application
            legend_font = self.settings.get('legend_font', 'Arial')
            legend_size = self.settings.get('legend_size', 10)
            legend_color = self.settings.get('legend_color', '#000000')

            # Create proper font properties
            font_props = FontProperties(family=legend_font, size=legend_size)

            # Get current handles and labels
            handles, labels = self.ax.get_legend_handles_labels()

            # Remove existing legend
            if self.ax.legend_ is not None:
                self.ax.legend_.remove()

            # Create new legend with updated font properties
            legend = self.ax.legend(handles, labels,
                                    bbox_to_anchor=(0.5, -0.15),
                                    loc='upper center',
                                    ncol=5,
                                    columnspacing=1,
                                    handletextpad=0.5,
                                    borderaxespad=0,
                                    facecolor='none',
                                    edgecolor='#446699',
                                    prop=font_props,  # Use prop instead of FontProperties
                                    bbox_transform=self.ax.transAxes)

            # Set text color separately
            for text in legend.get_texts():
                text.set_color(legend_color)

            # Update data series (apply colors to all series)
            for line in self.ax.get_lines():
                series_name = line.get_label()
                if series_name and series_name in self.data_series:
                    line.set_color(self.settings.get(f'data_series_color_{series_name}', '#008000'))

            # Update grids
            major_visible = self.settings.get('major_grid_visible', False)
            minor_visible = self.settings.get('minor_grid_visible', False)
            major_color = self.settings.get('major_grid_color', '#008000')
            minor_color = self.settings.get('minor_grid_color', '#008000')

            if major_visible:
                self.ax.grid(which='major', visible=True, color=major_color)
            else:
                self.ax.grid(which='major', visible=False)

            self.ax.minorticks_on()
            if minor_visible:
                self.ax.grid(which='minor', visible=True, color=minor_color)
            else:
                self.ax.grid(which='minor', visible=False)

            self.figure.tight_layout()
            self.figure.canvas.draw()

        except Exception as e:
            print(f"Error updating plot appearance: {str(e)}")

    def apply_changes(self):
        """Apply settings to plot and update settings dictionary"""
        # Update settings based on current UI values before applying
        self.settings['title_font'] = self.comboBoxTitleFont.currentFont().family()
        self.settings['title_size'] = int(self.comboBoxTitleFontSize.currentText())  # Ensure integer
        self.settings['title_color'] = self.lnEdtTitleFontColorHex.text() or '#000000'

        self.settings['label_font'] = self.comboBoxLabelFont.currentFont().family()
        self.settings['label_size'] = int(self.comboBoxLabelFontSize.currentText())  # Ensure integer
        self.settings['label_color'] = self.lnEdtLabelFontColorHex.text() or '#000000'

        self.settings['legend_font'] = self.comboBoxLegendFont.currentFont().family()
        self.settings['legend_size'] = int(self.comboBoxLegendFontSize.currentText())  # Ensure integer
        self.settings['legend_color'] = self.lnEdtLegendFontColorHex.text() or '#000000'

        # Update data series settings for all series
        for series in self.data_series.keys():
            legend_name = self.settings.get(f'legend_name_{series}', series)
            self.settings[f'data_series_color_{series}'] = self.settings.get(f'data_series_color_{series}', '#008000')
            self.settings[f'legend_name_{series}'] = legend_name

        # Update grid settings
        self.settings['major_grid_visible'] = self.checkBoxMajorGrid.isChecked()
        self.settings['minor_grid_visible'] = self.checkBoxMinorGrid.isChecked()
        self.settings['major_grid_color'] = self.lnEdtMajorGridColorHex.text() or '#008000'
        self.settings['minor_grid_color'] = self.lnEdtMinorGridColorHex.text() or '#008000'

        self._update_plot_appearance()
        self._update_settings_from_plot()  # Ensure settings reflect the current plot state

    def _update_range_spinboxes(self, axis_name):
        """Update range spinboxes when axis selection changes"""
        if self.comboBoxDataSelectionForPlotRange.currentText == "X-Axis":
            self.customDoubleSpinBoxRangeMin.setValue(self.settings['x_axis_min'])
            self.customDoubleSpinBoxRangeMax.setValue(self.settings['x_axis_max'])
        elif self.comboBoxDataSelectionForPlotRange == "Y-Axis":
            self.customDoubleSpinBoxRangeMin.setValue(self.settings['y_axis_min'])
            self.customDoubleSpinBoxRangeMax.setValue(self.settings['y_axis_max'])

    def _update_plot_range(self):
        """Update plot limits based on selected range"""
        try:
            min_val = self.customDoubleSpinBoxRangeMin.value()
            max_val = self.customDoubleSpinBoxRangeMax.value()

            # Ensure min is less than max
            if min_val >= max_val:
                return

            selected_axis = self.comboBoxDataSelectionForPlotRange.currentText()

            if selected_axis == "X-Axis":
                self.ax.set_xlim(min_val, max_val)
                self.settings['x_axis_min'] = min_val
                self.settings['x_axis_max'] = max_val
            elif selected_axis == "Y-Axis":
                self.ax.set_ylim(min_val, max_val)
                self.settings['y_axis_min'] = min_val
                self.settings['y_axis_max'] = max_val

            # Redraw the plot
            self.figure.canvas.draw_idle()
        except Exception as e:
            print(f"Error updating plot range: {str(e)}")

    def all_changes_confirmed(self):
        """Handle confirmation of all changes"""
        self.apply_changes()

        # Apply the final axis limits
        self.ax.set_xlim(self.settings['x_axis_min'], self.settings['x_axis_max'])
        self.ax.set_ylim(self.settings['y_axis_min'], self.settings['y_axis_max'])

        self.figure.canvas.draw()

        self.accept()


