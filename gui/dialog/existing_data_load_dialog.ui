<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Dialog</class>
 <widget class="QDialog" name="Dialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>440</width>
    <height>211</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Load Existing Data</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color:#09090b;</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <widget class="QFrame" name="frame">
     <property name="styleSheet">
      <string notr="true">QPushButton{
	background-color: transparent;
	border: none;
}

QPushButton: hover{
	background-color: #000000;
}

QPushButton: selected{
	border:none;
}</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="spacing">
       <number>20</number>
      </property>
      <item>
       <widget class="QFrame" name="frame_2">
        <property name="styleSheet">
         <string notr="true">QFrame{
background-color:#171717;
border-radius:5px;
}</string>
        </property>
        <property name="frameShape">
         <enum>QFrame::Shape::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Shadow::Raised</enum>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout">
         <item>
          <widget class="QLabel" name="label">
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>20</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">color:white;
font-family:arial;</string>
           </property>
           <property name="text">
            <string>Browse File</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame_5">
           <property name="frameShape">
            <enum>QFrame::Shape::StyledPanel</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Shadow::Raised</enum>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_3">
            <item>
             <widget class="QPushButton" name="btnBrowseJSONFile">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>100</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>100</width>
                <height>100</height>
               </size>
              </property>
              <property name="text">
               <string/>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="Line" name="line">
        <property name="styleSheet">
         <string notr="true">background-color:white;</string>
        </property>
        <property name="orientation">
         <enum>Qt::Orientation::Vertical</enum>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QFrame" name="frame_3">
        <property name="styleSheet">
         <string notr="true">QFrame{

background-color:#171717;
border-radius:5px;
}</string>
        </property>
        <property name="frameShape">
         <enum>QFrame::Shape::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Shadow::Raised</enum>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_2">
         <item>
          <widget class="QLabel" name="label_2">
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>20</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">color:white;
font-family:arial;</string>
           </property>
           <property name="text">
            <string>Browse Database</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame_6">
           <property name="frameShape">
            <enum>QFrame::Shape::NoFrame</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Shadow::Raised</enum>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_4">
            <item>
             <widget class="QPushButton" name="btnBrowseDatabase">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>100</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>100</width>
                <height>100</height>
               </size>
              </property>
              <property name="text">
               <string/>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
