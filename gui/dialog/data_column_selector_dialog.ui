<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Dialog</class>
 <widget class="QDialog" name="Dialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>528</width>
    <height>269</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Configure Temperature Data</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QDialog{
	background-color: #000000;
}

QLabel{
	color: #AAAAAA;
font-size: 16px;
font-family: inter;
	background-color: transparent;
}

QLineEdit{
	background-color: #1C1C1C;
	border: 1px solid #303030;
	border-radius: 10px;
	color: #EEEEEE;
	font-size: 16px;
	padding: 4px 15px;
}


QComboBox {
    border: 1px solid #303030;
    border-radius: 10px;
    padding: 4px 10px;
    background-color: #1C1C1C;
    color: #EEEEEE;
    min-width: 6em;
	font-size: 16px;
}

QComboBox::drop-down {
    border: none;
    background: transparent;
	width: 20px;
	margin-right: 8px;
}

QComboBox::down-arrow {
    image: url(&quot;assets/down-arrow.png&quot;);  /* You can use a custom arrow image */
    width: 24px;
    height: 24px;
}


/* Dropdown list styling */
QComboBox QAbstractItemView {
    border: none;
	margin: 5px;
    border-radius: 10px;
    padding: 5px 0px;
    background-color: #2a2a2a;
    color: white;
    selection-background-color: #3a3a3a;  /* Darker selection color */
    outline: 0px;  /* Remove focus border */
}

/* Individual item styling */
QComboBox QAbstractItemView::item {
    padding: 8px 12px;  /* Vertical and horizontal padding for items */
    border: none;
    min-height: 24px;  /* Minimum height for items */
}

/* Hover state for items */
QComboBox QAbstractItemView::item:hover {
    background-color: #3d3d3d;
	border-radius: 5px;
}

/* Selected item */
QComboBox QAbstractItemView::item:selected {
    background-color: #3d3d3d;
}

/* Remove the default focus rectangle */
QComboBox:focus {
    border: none;
    outline: none;
}

/* Hover state for the combobox */
QComboBox:hover {
    background-color: #252525;
}

/* Scrollbar styling */
QScrollBar:vertical {
    border: none;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}

QScrollBar::handle:vertical {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background: none;
}

/* Horizontal bar */
QScrollBar:horizontal {
    border: none;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}

QScrollBar::handle:horizontal {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:horizontal,
QScrollBar::sub-page:horizontal {
    background: none;
}</string>
  </property>
  <property name="sizeGripEnabled">
   <bool>false</bool>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>15</number>
   </property>
   <property name="rightMargin">
    <number>15</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="horizontalSpacing">
    <number>8</number>
   </property>
   <property name="verticalSpacing">
    <number>0</number>
   </property>
   <item row="1" column="1">
    <widget class="CheckableComboBox" name="temperatureColumnSelector"/>
   </item>
   <item row="0" column="1">
    <widget class="QComboBox" name="selectSampleCombo">
     <property name="minimumSize">
      <size>
       <width>148</width>
       <height>0</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
    </widget>
   </item>
   <item row="4" column="1">
    <widget class="QFrame" name="frame">
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>35</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">QFrame{
	background-color: transparent;
}</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QPushButton" name="proceedBtn">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>30</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>150</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton{
	background-color:rgb(255, 52, 20);
	border-radius:6px;
	padding:5px;
	font-size: 17px;
	font-family: Arial;
}

QPushButton:hover{
	background-color:#b089a1;
}

QPushButton:pressed{
	background-color:b03781;
}</string>
        </property>
        <property name="text">
         <string>Proceed</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="3" column="0">
    <widget class="QLabel" name="label_3">
     <property name="text">
      <string>Scan Rate (Samples per second):</string>
     </property>
    </widget>
   </item>
   <item row="1" column="0">
    <widget class="QLabel" name="label_2">
     <property name="text">
      <string>Select Temperature Columns:</string>
     </property>
    </widget>
   </item>
   <item row="0" column="0">
    <widget class="QLabel" name="label">
     <property name="text">
      <string>Select Sample Column:</string>
     </property>
    </widget>
   </item>
   <item row="3" column="1">
    <widget class="QFrame" name="frame_2">
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>42</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">QFrame{
background-color: transparant;
}</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QLineEdit" name="lnEdtScanRate">
        <property name="maximumSize">
         <size>
          <width>100</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="alignment">
         <set>Qt::AlignmentFlag::AlignCenter</set>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>CheckableComboBox</class>
   <extends>QComboBox</extends>
   <header location="global">.checkableComboBox.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
