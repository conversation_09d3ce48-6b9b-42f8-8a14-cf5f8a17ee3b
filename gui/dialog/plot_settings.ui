<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Plot_settings_Dialog</class>
 <widget class="QDialog" name="Plot_settings_Dialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>618</width>
    <height>732</height>
   </rect>
  </property>
  <property name="maximumSize">
   <size>
    <width>618</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Plot Settings</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QDialog{
	background-color: #171717;
}</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QTabWidget" name="tabWidget">
     <property name="styleSheet">
      <string notr="true">QTabWidget{
		background-color: transparent;
	
}

QWidget{
	background-color: transparent;
	
}	

QTabWidget::tab-bar{
	alignment: center;
}

QTabBar::tab{
	border-radius: 5px;
	width: 60px;
	height: 20px;
	color: white;
	font-size: 16px;
	font-family: inter;
	padding: 2px;
}

QTabBar::tab:selected{

	background: white;
	color: black;
	
}

QTabBar::tab:hover {
                    background: #787878;
                }</string>
     </property>
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="styleTab">
      <attribute name="title">
       <string>Style</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <widget class="Line" name="line_2">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>11</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>11</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>25</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QFrame{
	background-color: #212121;
	color: #212121;
}</string>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Shadow::Plain</enum>
         </property>
         <property name="lineWidth">
          <number>2</number>
         </property>
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label">
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>30</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">QLabel{

	color: #F9F9F9;
	font-family: inter;
	font-weight: bold;
	font-size: 16px;
}</string>
         </property>
         <property name="text">
          <string>Font Settings</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QWidget" name="widget" native="true">
         <layout class="QGridLayout" name="gridLayout_2">
          <property name="spacing">
           <number>9</number>
          </property>
          <item row="0" column="0">
           <widget class="QLabel" name="lblTitleFont">
            <property name="maximumSize">
             <size>
              <width>120</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">color: #AAAAAA;
font-size: 16px;
font-family: inter;
</string>
            </property>
            <property name="text">
             <string>Title Font</string>
            </property>
           </widget>
          </item>
          <item row="3" column="3">
           <widget class="QComboBox" name="comboBoxLegendFontSize">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>100</width>
              <height>45</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">/* ===== Core ComboBox ===== */
QComboBox {
    border: 1px solid #303030;
    border-radius: 10px;
    padding: 4px 15px;
    background: #1C1C1C;
    color: #EEEEEE;
    font-size: 16px;
    /* Force non-native popup */
    combobox-popup: 0;
}

/* ===== Dropdown Container Hack ===== */
QComboBox QFrame {
    border: none !important; /* Target Qt's internal container */
    background-color: #2a2a2a;
    margin: 0;
    padding: 0;
    border-radius: 10px;
}

/* ===== Dropdown Arrow ===== */
QComboBox::drop-down {
    border: none;
    background: transparent;
    width: 20px;
    margin-right: 8px;
}

QComboBox::down-arrow {
    image: url(&quot;assets/down-arrow.png&quot;);
    width: 24px;
    height: 24px;
}


/* ===== Dropdown List ===== */
QComboBox QAbstractItemView {
    border: none;
    background-color: #2a2a2a;
    margin: 5px; /* Inner spacing */
    padding: 0;
    border-radius: 10px;
    /* Remove focus rectangle */
    outline: 0;
}

/* ===== Scroll Area ===== */
QComboBox QScrollArea {
    border: none;
    background: transparent;
}

QComboBox QScrollArea QWidget {
    background: transparent;
    border: none;
}

/* ===== Items ===== */
QComboBox QAbstractItemView::item {
    min-height: 30px;
    padding: 8px 12px;
    color: white;
    border: none;
}

QComboBox QAbstractItemView::item:hover {
    background: #3d3d3d;
    border-radius: 5px;
}

/* Selected item */
QComboBox QAbstractItemView::item:selected {
    background-color: #3d3d3d;
	border-radius: 5px;
}

/* Remove the default focus rectangle */
QComboBox:focus {
    border: none;
    outline: none;
}

/* Hover state for the combobox */
QComboBox:hover {
    background-color: #252525;
}

QComboBox:editable {
    background: #1C1C1C; /* Fix for editable QComboBox */
}

/* Scrollbar styling */
QScrollBar:vertical {
    border: none;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}

QScrollBar::handle:vertical {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background: none;
}

/* Horizontal bar */
QScrollBar:horizontal {
    border: none;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}

QScrollBar::handle:horizontal {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:horizontal,
QScrollBar::sub-page:horizontal {
    background: none;
}

QFrame{
	border: none;
	background-color: #1c1c1c;
}

QComboBox QAbstractItemView: QFrame{
	border: none;
	background-color: #1c1c1c;
}

QComboBox QAbstractItemView:: QFrame{
	border: none;
	background-color: #1c1c1c;
}</string>
            </property>
           </widget>
          </item>
          <item row="0" column="4">
           <widget class="QWidget" name="widgetTitleFontColor" native="true">
            <property name="minimumSize">
             <size>
              <width>130</width>
              <height>45</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>130</width>
              <height>45</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QWidget{
	background-color: #1C1C1C;
	border: 1px solid #303030;
	border-radius: 10px;
	color: #EEEEEE;
}

QLineEdit{
		border:none;
	font-family: inter;
	font-size: 14px;
}</string>
            </property>
            <layout class="QHBoxLayout" name="horizontalLayout">
             <property name="leftMargin">
              <number>9</number>
             </property>
             <property name="topMargin">
              <number>2</number>
             </property>
             <property name="bottomMargin">
              <number>9</number>
             </property>
             <item>
              <widget class="QLineEdit" name="lnEdtTitleFontColorHex">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>38</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>38</height>
                </size>
               </property>
               <property name="text">
                <string>#008000</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btnTitleFontColorPallet">
               <property name="minimumSize">
                <size>
                 <width>30</width>
                 <height>30</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>23</width>
                 <height>23</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton{
	background-color: green;
}</string>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item row="3" column="4">
           <widget class="QWidget" name="widgetLegendFontColor" native="true">
            <property name="minimumSize">
             <size>
              <width>130</width>
              <height>45</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>130</width>
              <height>45</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QWidget{
	background-color: #1C1C1C;
	border: 1px solid #303030;
	border-radius: 10px;
	color: #EEEEEE;
}

QLineEdit{
		border:none;
	font-family: inter;
	font-size: 14px;
}</string>
            </property>
            <layout class="QHBoxLayout" name="horizontalLayout_6">
             <property name="topMargin">
              <number>2</number>
             </property>
             <item>
              <widget class="QLineEdit" name="lnEdtLegendFontColorHex">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>38</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>38</height>
                </size>
               </property>
               <property name="text">
                <string>#008000</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btnLegendFontColorPallet">
               <property name="minimumSize">
                <size>
                 <width>30</width>
                 <height>30</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>23</width>
                 <height>23</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton{
	background-color: green;
}</string>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="lblLabelFont">
            <property name="maximumSize">
             <size>
              <width>120</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">color: #AAAAAA;
font-size: 16px;
font-family: inter;
</string>
            </property>
            <property name="text">
             <string>Label Font</string>
            </property>
           </widget>
          </item>
          <item row="1" column="3">
           <widget class="QComboBox" name="comboBoxLabelFontSize">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>100</width>
              <height>45</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">/* ===== Core ComboBox ===== */
QComboBox {
    border: 1px solid #303030;
    border-radius: 10px;
    padding: 4px 15px;
    background: #1C1C1C;
    color: #EEEEEE;
    font-size: 16px;
    /* Force non-native popup */
    combobox-popup: 0;
}

/* ===== Dropdown Container Hack ===== */
QComboBox QFrame {
    border: none !important; /* Target Qt's internal container */
    background-color: #2a2a2a;
    margin: 0;
    padding: 0;
    border-radius: 10px;
}

/* ===== Dropdown Arrow ===== */
QComboBox::drop-down {
    border: none;
    background: transparent;
    width: 20px;
    margin-right: 8px;
}

QComboBox::down-arrow {
    image: url(&quot;assets/down-arrow.png&quot;);
    width: 24px;
    height: 24px;
}


/* ===== Dropdown List ===== */
QComboBox QAbstractItemView {
    border: none;
    background-color: #2a2a2a;
    margin: 5px; /* Inner spacing */
    padding: 0;
    border-radius: 10px;
    /* Remove focus rectangle */
    outline: 0;
}

/* ===== Scroll Area ===== */
QComboBox QScrollArea {
    border: none;
    background: transparent;
}

QComboBox QScrollArea QWidget {
    background: transparent;
    border: none;
}

/* ===== Items ===== */
QComboBox QAbstractItemView::item {
    min-height: 30px;
    padding: 8px 12px;
    color: white;
    border: none;
}

QComboBox QAbstractItemView::item:hover {
    background: #3d3d3d;
    border-radius: 5px;
}

/* Selected item */
QComboBox QAbstractItemView::item:selected {
    background-color: #3d3d3d;
	border-radius: 5px;
}

/* Remove the default focus rectangle */
QComboBox:focus {
    border: none;
    outline: none;
}

/* Hover state for the combobox */
QComboBox:hover {
    background-color: #252525;
}

QComboBox:editable {
    background: #1C1C1C; /* Fix for editable QComboBox */
}

/* Scrollbar styling */
QScrollBar:vertical {
    border: none;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}

QScrollBar::handle:vertical {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background: none;
}

/* Horizontal bar */
QScrollBar:horizontal {
    border: none;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}

QScrollBar::handle:horizontal {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:horizontal,
QScrollBar::sub-page:horizontal {
    background: none;
}

QFrame{
	border: none;
	background-color: #1c1c1c;
}

QComboBox QAbstractItemView: QFrame{
	border: none;
	background-color: #1c1c1c;
}

QComboBox QAbstractItemView:: QFrame{
	border: none;
	background-color: #1c1c1c;
}</string>
            </property>
           </widget>
          </item>
          <item row="1" column="4">
           <widget class="QWidget" name="widgetLabelFontColor" native="true">
            <property name="minimumSize">
             <size>
              <width>130</width>
              <height>45</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>130</width>
              <height>45</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QWidget{
	background-color: #1C1C1C;
	border: 1px solid #303030;
	border-radius: 10px;
	color: #EEEEEE;
}

QLineEdit{
		border:none;
	font-family: inter;
	font-size: 14px;
}</string>
            </property>
            <layout class="QHBoxLayout" name="horizontalLayout_4">
             <property name="topMargin">
              <number>2</number>
             </property>
             <item>
              <widget class="QLineEdit" name="lnEdtLabelFontColorHex">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>38</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>38</height>
                </size>
               </property>
               <property name="text">
                <string>#008000</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btnLabelFontColorPallet">
               <property name="minimumSize">
                <size>
                 <width>30</width>
                 <height>30</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>23</width>
                 <height>23</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton{
	background-color: green;
}</string>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLabel" name="lblLegendFont">
            <property name="maximumSize">
             <size>
              <width>120</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">color: #AAAAAA;
font-size: 16px;
font-family: inter;
</string>
            </property>
            <property name="text">
             <string>Legend Font</string>
            </property>
           </widget>
          </item>
          <item row="0" column="3">
           <widget class="QComboBox" name="comboBoxTitleFontSize">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>100</width>
              <height>45</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">/* ===== Core ComboBox ===== */
QComboBox {
    border: 1px solid #303030;
    border-radius: 10px;
    padding: 4px 15px;
    background: #1C1C1C;
    color: #EEEEEE;
    font-size: 16px;
    /* Force non-native popup */
    combobox-popup: 0;
}

/* ===== Dropdown Container Hack ===== */
QComboBox QFrame {
    border: none !important; /* Target Qt's internal container */
    background-color: #2a2a2a;
    margin: 0;
    padding: 0;
    border-radius: 10px;
}

/* ===== Dropdown Arrow ===== */
QComboBox::drop-down {
    border: none;
    background: transparent;
    width: 20px;
    margin-right: 8px;
}

QComboBox::down-arrow {
    image: url(&quot;assets/down-arrow.png&quot;);
    width: 24px;
    height: 24px;
}


/* ===== Dropdown List ===== */
QComboBox QAbstractItemView {
    border: none;
    background-color: #2a2a2a;
    margin: 5px; /* Inner spacing */
    padding: 0;
    border-radius: 10px;
    /* Remove focus rectangle */
    outline: 0;
}

/* ===== Scroll Area ===== */
QComboBox QScrollArea {
    border: none;
    background: transparent;
}

QComboBox QScrollArea QWidget {
    background: transparent;
    border: none;
}

/* ===== Items ===== */
QComboBox QAbstractItemView::item {
    min-height: 30px;
    padding: 8px 12px;
    color: white;
    border: none;
}

QComboBox QAbstractItemView::item:hover {
    background: #3d3d3d;
    border-radius: 5px;
}

/* Selected item */
QComboBox QAbstractItemView::item:selected {
    background-color: #3d3d3d;
	border-radius: 5px;
}

/* Remove the default focus rectangle */
QComboBox:focus {
    border: none;
    outline: none;
}

/* Hover state for the combobox */
QComboBox:hover {
    background-color: #252525;
}

QComboBox:editable {
    background: #1C1C1C; /* Fix for editable QComboBox */
}

/* Scrollbar styling */
QScrollBar:vertical {
    border: none;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}

QScrollBar::handle:vertical {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background: none;
}

/* Horizontal bar */
QScrollBar:horizontal {
    border: none;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}

QScrollBar::handle:horizontal {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:horizontal,
QScrollBar::sub-page:horizontal {
    background: none;
}

QFrame{
	border: none;
	background-color: #1c1c1c;
}

QComboBox QAbstractItemView: QFrame{
	border: none;
	background-color: #1c1c1c;
}

QComboBox QAbstractItemView:: QFrame{
	border: none;
	background-color: #1c1c1c;
}</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QFontComboBox" name="comboBoxTitleFont">
            <property name="minimumSize">
             <size>
              <width>148</width>
              <height>45</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>220</width>
              <height>45</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QComboBox {
    border: 1px solid #303030;
    border-radius: 10px;
    padding: 4px 10px;
    background-color: #1C1C1C;
    color: #EEEEEE;
    min-width: 6em;
	font-size: 16px;
}

QComboBox::drop-down {
    border: none;
    background: transparent;
	width: 20px;
	margin-right: 8px;
}

QComboBox::down-arrow {
    image: url(&quot;assets/down-arrow.png&quot;);  /* You can use a custom arrow image */
    width: 24px;
    height: 24px;
}


/* Dropdown list styling */
QComboBox QAbstractItemView {
    border: none;
	margin: 5px;
    border-radius: 10px;
    padding: 5px 0px;
    background-color: #2a2a2a;
    color: white;
    selection-background-color: #3a3a3a;  /* Darker selection color */
    outline: 0px;  /* Remove focus border */
}

/* Individual item styling */
QComboBox QAbstractItemView::item {
    padding: 8px 12px;  /* Vertical and horizontal padding for items */
    border: none;
    min-height: 24px;  /* Minimum height for items */
}

/* Hover state for items */
QComboBox QAbstractItemView::item:hover {
    background-color: #3d3d3d;
	border-radius: 5px;
}

/* Selected item */
QComboBox QAbstractItemView::item:selected {
    background-color: #3d3d3d;
}

/* Remove the default focus rectangle */
QComboBox:focus {
    border: none;
    outline: none;
}

/* Hover state for the combobox */
QComboBox:hover {
    background-color: #252525;
}

/* Scrollbar styling */
QScrollBar:vertical {
    border: none;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}

QScrollBar::handle:vertical {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background: none;
}

/* Horizontal bar */
QScrollBar:horizontal {
    border: none;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}

QScrollBar::handle:horizontal {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:horizontal,
QScrollBar::sub-page:horizontal {
    background: none;
}</string>
            </property>
           </widget>
          </item>
          <item row="1" column="2">
           <widget class="QFontComboBox" name="comboBoxLabelFont">
            <property name="minimumSize">
             <size>
              <width>148</width>
              <height>45</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>220</width>
              <height>45</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QComboBox {
    border: 1px solid #303030;
    border-radius: 10px;
    padding: 4px 10px;
    background-color: #1C1C1C;
    color: #EEEEEE;
    min-width: 6em;
	font-size: 16px;
}

QComboBox::drop-down {
    border: none;
    background: transparent;
	width: 20px;
	margin-right: 8px;
}

QComboBox::down-arrow {
    image: url(&quot;assets/down-arrow.png&quot;);  /* You can use a custom arrow image */
    width: 24px;
    height: 24px;
}


/* Dropdown list styling */
QComboBox QAbstractItemView {
    border: none;
	margin: 5px;
    border-radius: 10px;
    padding: 5px 0px;
    background-color: #2a2a2a;
    color: white;
    selection-background-color: #3a3a3a;  /* Darker selection color */
    outline: 0px;  /* Remove focus border */
}

/* Individual item styling */
QComboBox QAbstractItemView::item {
    padding: 8px 12px;  /* Vertical and horizontal padding for items */
    border: none;
    min-height: 24px;  /* Minimum height for items */
}

/* Hover state for items */
QComboBox QAbstractItemView::item:hover {
    background-color: #3d3d3d;
	border-radius: 5px;
}

/* Selected item */
QComboBox QAbstractItemView::item:selected {
    background-color: #3d3d3d;
}

/* Remove the default focus rectangle */
QComboBox:focus {
    border: none;
    outline: none;
}

/* Hover state for the combobox */
QComboBox:hover {
    background-color: #252525;
}

/* Scrollbar styling */
QScrollBar:vertical {
    border: none;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}

QScrollBar::handle:vertical {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background: none;
}

/* Horizontal bar */
QScrollBar:horizontal {
    border: none;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}

QScrollBar::handle:horizontal {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:horizontal,
QScrollBar::sub-page:horizontal {
    background: none;
}</string>
            </property>
           </widget>
          </item>
          <item row="3" column="2">
           <widget class="QFontComboBox" name="comboBoxLegendFont">
            <property name="minimumSize">
             <size>
              <width>148</width>
              <height>45</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>220</width>
              <height>45</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QComboBox {
    border: 1px solid #303030;
    border-radius: 10px;
    padding: 4px 10px;
    background-color: #1C1C1C;
    color: #EEEEEE;
    min-width: 6em;
	font-size: 16px;
}

QComboBox::drop-down {
    border: none;
    background: transparent;
	width: 20px;
	margin-right: 8px;
}

QComboBox::down-arrow {
    image: url(&quot;assets/down-arrow.png&quot;);  /* You can use a custom arrow image */
    width: 24px;
    height: 24px;
}


/* Dropdown list styling */
QComboBox QAbstractItemView {
    border: none;
	margin: 5px;
    border-radius: 10px;
    padding: 5px 0px;
    background-color: #2a2a2a;
    color: white;
    selection-background-color: #3a3a3a;  /* Darker selection color */
    outline: 0px;  /* Remove focus border */
}

/* Individual item styling */
QComboBox QAbstractItemView::item {
    padding: 8px 12px;  /* Vertical and horizontal padding for items */
    border: none;
    min-height: 24px;  /* Minimum height for items */
}

/* Hover state for items */
QComboBox QAbstractItemView::item:hover {
    background-color: #3d3d3d;
	border-radius: 5px;
}

/* Selected item */
QComboBox QAbstractItemView::item:selected {
    background-color: #3d3d3d;
}

/* Remove the default focus rectangle */
QComboBox:focus {
    border: none;
    outline: none;
}

/* Hover state for the combobox */
QComboBox:hover {
    background-color: #252525;
}

/* Scrollbar styling */
QScrollBar:vertical {
    border: none;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}

QScrollBar::handle:vertical {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background: none;
}

/* Horizontal bar */
QScrollBar:horizontal {
    border: none;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}

QScrollBar::handle:horizontal {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:horizontal,
QScrollBar::sub-page:horizontal {
    background: none;
}</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="Line" name="line">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>11</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>11</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>25</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QFrame{
	background-color: #212121;
	color: #212121;
}</string>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Shadow::Plain</enum>
         </property>
         <property name="lineWidth">
          <number>2</number>
         </property>
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label_2">
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>30</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">QLabel{

	color: #F9F9F9;
	font-family: inter;
	font-weight: bold;
	font-size: 16px;
}</string>
         </property>
         <property name="text">
          <string>Data Settings</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QWidget" name="widget_2" native="true">
         <layout class="QVBoxLayout" name="verticalLayout_2">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QWidget" name="widget_9" native="true">
            <layout class="QHBoxLayout" name="horizontalLayout_2">
             <item>
              <widget class="QLabel" name="lblDataSelection">
               <property name="maximumSize">
                <size>
                 <width>120</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">color: #AAAAAA;
font-size: 16px;
font-family: inter;
</string>
               </property>
               <property name="text">
                <string>Data Selection</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QComboBox" name="comboBoxDataSelection">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>45</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>330</width>
                 <height>45</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">/* ===== Core ComboBox ===== */
QComboBox {
    border: 1px solid #303030;
    border-radius: 10px;
    padding: 4px 15px;
    background: #1C1C1C;
    color: #EEEEEE;
    font-size: 16px;
    /* Force non-native popup */
    combobox-popup: 0;
}

/* ===== Dropdown Container Hack ===== */
QComboBox QFrame {
    border: none !important; /* Target Qt's internal container */
    background-color: #2a2a2a;
    margin: 0;
    padding: 0;
    border-radius: 10px;
}

/* ===== Dropdown Arrow ===== */
QComboBox::drop-down {
    border: none;
    background: transparent;
    width: 20px;
    margin-right: 8px;
}

QComboBox::down-arrow {
    image: url(&quot;assets/down-arrow.png&quot;);
    width: 24px;
    height: 24px;
}


/* ===== Dropdown List ===== */
QComboBox QAbstractItemView {
    border: none;
    background-color: #2a2a2a;
    margin: 5px; /* Inner spacing */
    padding: 0;
    border-radius: 10px;
    /* Remove focus rectangle */
    outline: 0;
}

/* ===== Scroll Area ===== */
QComboBox QScrollArea {
    border: none;
    background: transparent;
}

QComboBox QScrollArea QWidget {
    background: transparent;
    border: none;
}

/* ===== Items ===== */
QComboBox QAbstractItemView::item {
    min-height: 30px;
    padding: 8px 12px;
    color: white;
    border: none;
}

QComboBox QAbstractItemView::item:hover {
    background: #3d3d3d;
    border-radius: 5px;
}

/* Selected item */
QComboBox QAbstractItemView::item:selected {
    background-color: #3d3d3d;
	border-radius: 5px;
}

/* Remove the default focus rectangle */
QComboBox:focus {
    border: none;
    outline: none;
}

/* Hover state for the combobox */
QComboBox:hover {
    background-color: #252525;
}

QComboBox:editable {
    background: #1C1C1C; /* Fix for editable QComboBox */
}

/* Scrollbar styling */
QScrollBar:vertical {
    border: none;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}

QScrollBar::handle:vertical {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background: none;
}

/* Horizontal bar */
QScrollBar:horizontal {
    border: none;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}

QScrollBar::handle:horizontal {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:horizontal,
QScrollBar::sub-page:horizontal {
    background: none;
}

QFrame{
	border: none;
	background-color: #1c1c1c;
}

QComboBox QAbstractItemView: QFrame{
	border: none;
	background-color: #1c1c1c;
}

QComboBox QAbstractItemView:: QFrame{
	border: none;
	background-color: #1c1c1c;
}</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QWidget" name="widgetDataSelectionColor" native="true">
               <property name="minimumSize">
                <size>
                 <width>130</width>
                 <height>45</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>130</width>
                 <height>45</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QWidget{
	background-color: #1C1C1C;
	border: 1px solid #303030;
	border-radius: 10px;
	color: #EEEEEE;
}

QLineEdit{
		border:none;
	font-family: inter;
	font-size: 14px;
}</string>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_5">
                <property name="topMargin">
                 <number>2</number>
                </property>
                <item>
                 <widget class="QLineEdit" name="lnEdtDataSelectionColorHex">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>38</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>38</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>#008000</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btnDataSelectionColorPallet">
                  <property name="minimumSize">
                   <size>
                    <width>30</width>
                    <height>30</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>23</width>
                    <height>23</height>
                   </size>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">QPushButton{
	background-color: green;
}</string>
                  </property>
                  <property name="text">
                   <string/>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QWidget" name="widget_10" native="true">
            <layout class="QHBoxLayout" name="horizontalLayout_3">
             <item>
              <widget class="QLabel" name="lblLegendName">
               <property name="minimumSize">
                <size>
                 <width>120</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>120</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">color: #AAAAAA;
font-size: 16px;
font-family: inter;
</string>
               </property>
               <property name="text">
                <string>Legend Name</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lnEdtLegendName">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>45</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>45</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QLineEdit{
	background-color: #1C1C1C;
	border: 1px solid #303030;
	border-radius: 10px;
	color: #EEEEEE;
	font-size: 16px;
	padding: 4px 15px;
}</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="Line" name="line_3">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>11</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>11</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>25</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QFrame{
	background-color: #212121;
	color: #212121;
}</string>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Shadow::Plain</enum>
         </property>
         <property name="lineWidth">
          <number>2</number>
         </property>
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label_3">
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>30</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">QLabel{

	color: #F9F9F9;
	font-family: inter;
	font-weight: bold;
	font-size: 16px;
}</string>
         </property>
         <property name="text">
          <string>Grid Settings</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QWidget" name="widget_3" native="true">
         <property name="styleSheet">
          <string notr="true">QCheckBox {
    color: #444444;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
}

QCheckBox::indicator:checked {
    image: url(&quot;D:/SoftwareDevelopment/User_verification_Email/assets/checkbox_checked.png&quot;);
}

QCheckBox::indicator:unchecked {
    image: url(&quot;D:/SoftwareDevelopment/User_verification_Email/assets/checkbox_unchecked.png&quot;);
}
</string>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_9">
          <item>
           <widget class="QCheckBox" name="checkBoxMajorGrid">
            <property name="maximumSize">
             <size>
              <width>25</width>
              <height>25</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QCheckBox {
    color: #444444;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
}

QCheckBox::indicator:checked {
    image: url(&quot;D:/SoftwareDevelopment/User_verification_Email/assets/checkbox_checked.png&quot;);
}

QCheckBox::indicator:unchecked {
    image: url(&quot;D:/SoftwareDevelopment/User_verification_Email/assets/checkbox_unchecked.png&quot;);
}
</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="lblMajorGrid">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>100</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">color: #AAAAAA;
font-size: 16px;
font-family: inter;
</string>
            </property>
            <property name="text">
             <string>Major Grid</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QWidget" name="widgetMajorGridColor" native="true">
            <property name="minimumSize">
             <size>
              <width>130</width>
              <height>45</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>130</width>
              <height>45</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QWidget{
	background-color: #1C1C1C;
	border: 1px solid #303030;
	border-radius: 10px;
	color: #EEEEEE;
}

QLineEdit{
		border:none;
	font-family: inter;
	font-size: 14px;
}</string>
            </property>
            <layout class="QHBoxLayout" name="horizontalLayout_7">
             <property name="topMargin">
              <number>2</number>
             </property>
             <item>
              <widget class="QLineEdit" name="lnEdtMajorGridColorHex">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>38</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>38</height>
                </size>
               </property>
               <property name="text">
                <string>#008000</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btnMajorGridColorPallet">
               <property name="minimumSize">
                <size>
                 <width>30</width>
                 <height>30</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>23</width>
                 <height>23</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton{
	background-color: green;
}</string>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="Line" name="line_5">
            <property name="minimumSize">
             <size>
              <width>2</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>2</width>
              <height>1667125</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">Line{
	background-color: #212121;
}</string>
            </property>
            <property name="orientation">
             <enum>Qt::Orientation::Vertical</enum>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_2">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QCheckBox" name="checkBoxMinorGrid">
            <property name="maximumSize">
             <size>
              <width>25</width>
              <height>25</height>
             </size>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="lblMinorGrid">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>100</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">color: #AAAAAA;
font-size: 16px;
font-family: inter;
</string>
            </property>
            <property name="text">
             <string>Minor Grid</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QWidget" name="widgetMinorGridColor" native="true">
            <property name="minimumSize">
             <size>
              <width>130</width>
              <height>45</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>130</width>
              <height>45</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QWidget{
	background-color: #1C1C1C;
	border: 1px solid #303030;
	border-radius: 10px;
	color: #EEEEEE;
}

QLineEdit{
		border:none;
	font-family: inter;
	font-size: 14px;
}</string>
            </property>
            <layout class="QHBoxLayout" name="horizontalLayout_8">
             <property name="topMargin">
              <number>2</number>
             </property>
             <property name="rightMargin">
              <number>9</number>
             </property>
             <item>
              <widget class="QLineEdit" name="lnEdtMinorGridColorHex">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>38</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>38</height>
                </size>
               </property>
               <property name="text">
                <string>#008000</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btnMinorGridColorPallet">
               <property name="minimumSize">
                <size>
                 <width>30</width>
                 <height>30</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>23</width>
                 <height>23</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton{
	background-color: green;
}</string>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="Line" name="line_4">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>11</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>11</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>25</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QFrame{
	background-color: #212121;
	color: #212121;
}</string>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Shadow::Plain</enum>
         </property>
         <property name="lineWidth">
          <number>2</number>
         </property>
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label_4">
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>30</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">QLabel{

	color: #F9F9F9;
	font-family: inter;
	font-weight: bold;
	font-size: 16px;
}</string>
         </property>
         <property name="text">
          <string>Plot Range Settings</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QWidget" name="widget_11" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout_11">
          <item>
           <widget class="QComboBox" name="comboBoxDataSelectionForPlotRange">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>1667125</width>
              <height>45</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">/* ===== Core ComboBox ===== */
QComboBox {
    border: 1px solid #303030;
    border-radius: 10px;
    padding: 4px 15px;
    background: #1C1C1C;
    color: #EEEEEE;
    font-size: 16px;
    /* Force non-native popup */
    combobox-popup: 0;
}

/* ===== Dropdown Container Hack ===== */
QComboBox QFrame {
    border: none !important; /* Target Qt's internal container */
    background-color: #2a2a2a;
    margin: 0;
    padding: 0;
    border-radius: 10px;
}

/* ===== Dropdown Arrow ===== */
QComboBox::drop-down {
    border: none;
    background: transparent;
    width: 20px;
    margin-right: 8px;
}

QComboBox::down-arrow {
    image: url(&quot;assets/down-arrow.png&quot;);
    width: 24px;
    height: 24px;
}


/* ===== Dropdown List ===== */
QComboBox QAbstractItemView {
    border: none;
    background-color: #2a2a2a;
    margin: 5px; /* Inner spacing */
    padding: 0;
    border-radius: 10px;
    /* Remove focus rectangle */
    outline: 0;
}

/* ===== Scroll Area ===== */
QComboBox QScrollArea {
    border: none;
    background: transparent;
}

QComboBox QScrollArea QWidget {
    background: transparent;
    border: none;
}

/* ===== Items ===== */
QComboBox QAbstractItemView::item {
    min-height: 30px;
    padding: 8px 12px;
    color: white;
    border: none;
}

QComboBox QAbstractItemView::item:hover {
    background: #3d3d3d;
    border-radius: 5px;
}

/* Selected item */
QComboBox QAbstractItemView::item:selected {
    background-color: #3d3d3d;
	border-radius: 5px;
}

/* Remove the default focus rectangle */
QComboBox:focus {
    border: none;
    outline: none;
}

/* Hover state for the combobox */
QComboBox:hover {
    background-color: #252525;
}

QComboBox:editable {
    background: #1C1C1C; /* Fix for editable QComboBox */
}

/* Scrollbar styling */
QScrollBar:vertical {
    border: none;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}

QScrollBar::handle:vertical {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background: none;
}

/* Horizontal bar */
QScrollBar:horizontal {
    border: none;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}

QScrollBar::handle:horizontal {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:horizontal,
QScrollBar::sub-page:horizontal {
    background: none;
}

QFrame{
	border: none;
	background-color: #1c1c1c;
}

QComboBox QAbstractItemView: QFrame{
	border: none;
	background-color: #1c1c1c;
}

QComboBox QAbstractItemView:: QFrame{
	border: none;
	background-color: #1c1c1c;
}</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="CustomDoubleSpinBox" name="customDoubleSpinBoxRangeMin">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>45</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>130</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QDoubleSpinBox{
	border: 1px solid #303030;
    border-radius: 10px;
    padding: 4px 15px;
    background: #1C1C1C;
    color: #EEEEEE;
    font-size: 16px;
}</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignmentFlag::AlignCenter</set>
            </property>
            <property name="buttonSymbols">
             <enum>QAbstractSpinBox::ButtonSymbols::NoButtons</enum>
            </property>
            <property name="maximum">
             <double>10000000.000000000000000</double>
            </property>
            <property name="singleStep">
             <double>0.000000000000000</double>
            </property>
           </widget>
          </item>
          <item>
           <widget class="CustomDoubleSpinBox" name="customDoubleSpinBoxRangeMax">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>45</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>130</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QDoubleSpinBox{
	border: 1px solid #303030;
    border-radius: 10px;
    padding: 4px 15px;
    background: #1C1C1C;
    color: #EEEEEE;
    font-size: 16px;
}</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignmentFlag::AlignCenter</set>
            </property>
            <property name="buttonSymbols">
             <enum>QAbstractSpinBox::ButtonSymbols::NoButtons</enum>
            </property>
            <property name="maximum">
             <double>10000000.000000000000000</double>
            </property>
            <property name="singleStep">
             <double>0.000000000000000</double>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="frame">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>50</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>50</height>
          </size>
         </property>
         <property name="frameShape">
          <enum>QFrame::Shape::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Shadow::Raised</enum>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_10">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <spacer name="horizontalSpacer_3">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>391</width>
              <height>17</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="pushButton_7">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>40</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>100</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton{
	background-color: #1c1c1c;
	color: white;
	font-family: inter;
	font-size: 14px;
	border-radius: 5px;
}</string>
            </property>
            <property name="text">
             <string>Cancel</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="pushButton_8">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>40</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>100</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton{
	background-color: #4f39f8;
font-family: inter;
	font-size: 14px;
	border-radius: 5px;
}</string>
            </property>
            <property name="text">
             <string>Confirm</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>CustomDoubleSpinBox</class>
   <extends>QDoubleSpinBox</extends>
   <header location="global">.custom_double_spin_box.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
