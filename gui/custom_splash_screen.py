import logging
import os

from PySide6.QtCore import Qt, QSize, QUrl
from PySide6.QtGui import QMovie
from PySide6.QtMultimedia import QAudioOutput, QMediaPlayer
from PySide6.QtWidgets import <PERSON>A<PERSON><PERSON>, QLabel, QVBoxLayout, QWidget

from src.utils import get_resource_path


class CustomSplashScreen(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # Initialize media player for boot sound
        self.player = QMediaPlayer()
        self.audio_output = QAudioOutput()
        self.player.setAudioOutput(self.audio_output)

        # Load the boot sound
        sound_path = get_resource_path("assets/boot_sound.wav")
        logging.debug(f"Loading boot sound from : {sound_path}")

        if os.path.exists(sound_path):
            url = QUrl.fromLocalFile(sound_path)
            self.player.setSource(url)
            logging.debug("Boot sound file loaded successfully")
        else:
            logging.error(f"Boot sound file not found at: {sound_path}")

        # Set Volume (0.0 to 1.0)
        self.audio_output.setVolume(1.0)

        # Connect error handling signals
        self.player.errorOccurred.connect(self._handle_media_error)

        # Create main layout
        layout = QVBoxLayout(self)

        # Create inner widget with border radius
        self.content_widget = QWidget()
        self.content_widget.setObjectName("SplashScreen")
        content_layout = QVBoxLayout(self.content_widget)

        # Style the content widget
        self.content_widget.setStyleSheet("""
            QWidget#SplashScreen {
                background-color: #171717;
                border-radius: 15px;
            }
        """)

        # Add animated GIF
        self.movie_label = QLabel()
        gif_path = get_resource_path("assets/sine_wave_animation.gif")
        self.movie = QMovie(gif_path)
        self.movie.setScaledSize(QSize(400, 200))
        self.movie_label.setMovie(self.movie)

        # Add error handling for movie loading
        if self.movie.isValid():
            self.movie.start()
        else:
            self.movie_label.setText("Failed to load animation")
            print(f"Failed to load animation from: {gif_path}")

        content_layout.addWidget(self.movie_label)

        # Add status label
        self.status_label = QLabel("Initializing...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: #333333; font-size: 12px;")
        content_layout.addWidget(self.status_label)

        # Add the content widget to the main layout
        layout.addWidget(self.content_widget)

        # Set size and position
        self.resize(400, 250)
        screen = QApplication.primaryScreen().geometry()
        self.move(
            (screen.width() - self.width()) // 2,
            (screen.height() - self.height()) // 2
        )

    def update_status(self, message):
        self.status_label.setText(message)

    def _handle_media_error(self, error, error_string):
        logging.error(f"Media player error: {error_string}")

    def play_boot_sound(self):
        """Play the boot-up sound"""
        if self.player.source().isValid():
            self.player.play()
            logging.debug("Playing boot sound")
        else:
            logging.error("Invalid media source for boot sound")