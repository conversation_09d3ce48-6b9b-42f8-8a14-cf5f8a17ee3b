"""
Debug script to force save all data to temp_data.json
Run this script to manually trigger a save of all data
"""

import sys
import os
import time

# Add the parent directory to the path so we can import the modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    # Import the required modules
    from PySide6.QtWidgets import QApplication
    from gui_main import MainWindow

    # Create a QApplication instance
    app = QApplication(sys.argv)

    # Create a MainWindow instance
    window = MainWindow()

    # Wait for the window to initialize
    print("Waiting for window to initialize...")
    time.sleep(5)

    # Force save all data
    print("Forcing save of all data...")
    if hasattr(window, 'auto_saver'):
        success = window.auto_saver.force_save_all_data()
        if success:
            print("Data saved successfully!")
            # Print the path to the saved file
            user_home = os.path.expanduser("~")
            json_path = os.path.join(user_home, "temp_data.json")
            print(f"Data saved to: {json_path}")
            # Check if the file exists
            if os.path.exists(json_path):
                print(f"File size: {os.path.getsize(json_path)} bytes")
                # Print the contents of the file
                import json
                with open(json_path, 'r') as f:
                    data = json.load(f)
                    print(f"File contains {len(data)} top-level keys: {list(data.keys())}")
            else:
                print(f"File does not exist: {json_path}")
        else:
            print("Failed to save data!")
    else:
        print("Auto-saver not initialized!")

    # Exit the application
    sys.exit(0)

except Exception as e:
    print(f"Error: {str(e)}")
    import traceback
    traceback.print_exc()
