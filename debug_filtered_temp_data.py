import os
import sys
import traceback
import psycopg2
import pandas as pd
from psycopg2.extras import <PERSON><PERSON>, RealDictCursor

# Database connection parameters - update these to match your configuration
DB_PARAMS = {
    'dbname': 'vapr_idex',
    'user': 'postgres',
    'password': 'postgres',
    'host': 'localhost',
    'port': '5432'
}

def test_database_connection():
    """Test the database connection"""
    try:
        conn = psycopg2.connect(**DB_PARAMS)
        print("Database connection successful!")
        conn.close()
        return True
    except Exception as e:
        print(f"Database connection failed: {str(e)}")
        return False

def get_test_ids():
    """Get all test IDs from the database"""
    try:
        conn = psycopg2.connect(**DB_PARAMS)
        cur = conn.cursor()
        cur.execute("SELECT test_id, test_no FROM test_data ORDER BY test_id")
        results = cur.fetchall()
        conn.close()
        
        if results:
            print(f"Found {len(results)} tests in the database:")
            for test_id, test_no in results:
                print(f"  Test ID: {test_id}, Test No: {test_no}")
            return [test_id for test_id, _ in results]
        else:
            print("No tests found in the database.")
            return []
    except Exception as e:
        print(f"Error getting test IDs: {str(e)}")
        traceback.print_exc()
        return []

def check_performance_data(test_id):
    """Check the performance_data structure for a specific test"""
    try:
        conn = psycopg2.connect(**DB_PARAMS)
        cur = conn.cursor(cursor_factory=RealDictCursor)
        cur.execute("SELECT performance_data FROM test_data WHERE test_id = %s", (test_id,))
        result = cur.fetchone()
        conn.close()
        
        if result and result['performance_data']:
            print(f"\nPerformance data for test_id {test_id}:")
            perf_data = result['performance_data']
            print(f"  Keys: {list(perf_data.keys())}")
            
            # Check for filtered temperature data
            if 'filtered_temp_data' in perf_data:
                print("  Found 'filtered_temp_data' key")
                filtered_data = perf_data['filtered_temp_data']
                if isinstance(filtered_data, dict):
                    print(f"  filtered_temp_data keys: {list(filtered_data.keys())}")
                    
                    # Check for selection criteria
                    if 'selected_columns' in filtered_data:
                        print(f"  selected_columns: {filtered_data['selected_columns']}")
                    if 'selected_ranges' in filtered_data:
                        print(f"  selected_ranges: {filtered_data['selected_ranges']}")
                    
                    # Check for direct data
                    if 'time_data' in filtered_data:
                        print(f"  time_data length: {len(filtered_data['time_data'])}")
                    if 'temperature_data' in filtered_data:
                        print(f"  temperature_data keys: {list(filtered_data['temperature_data'].keys())}")
                else:
                    print(f"  filtered_temp_data is not a dictionary: {type(filtered_data)}")
            elif 'filtered_temperature_data' in perf_data:
                print("  Found 'filtered_temperature_data' key instead of 'filtered_temp_data'")
                filtered_data = perf_data['filtered_temperature_data']
                if isinstance(filtered_data, dict):
                    print(f"  filtered_temperature_data keys: {list(filtered_data.keys())}")
                else:
                    print(f"  filtered_temperature_data is not a dictionary: {type(filtered_data)}")
            else:
                print("  No filtered temperature data found")
            
            return perf_data
        else:
            print(f"\nNo performance data found for test_id {test_id}")
            return None
    except Exception as e:
        print(f"Error checking performance data: {str(e)}")
        traceback.print_exc()
        return None

def test_get_filtered_temp_data(test_id):
    """Test retrieving filtered temperature data for a specific test"""
    try:
        conn = psycopg2.connect(**DB_PARAMS)
        cur = conn.cursor()
        
        # Try with filtered_temp_data first
        query = """
            SELECT performance_data->'filtered_temp_data'
            FROM test_data
            WHERE test_id = %s
        """
        cur.execute(query, (test_id,))
        result = cur.fetchone()
        
        if not result or not result[0]:
            # Try with filtered_temperature_data
            query = """
                SELECT performance_data->'filtered_temperature_data'
                FROM test_data
                WHERE test_id = %s
            """
            cur.execute(query, (test_id,))
            result = cur.fetchone()
        
        conn.close()
        
        if result and result[0]:
            data_dict = result[0]
            print(f"\nRetrieved filtered temperature data for test_id {test_id}:")
            
            if isinstance(data_dict, dict):
                print(f"  Keys: {list(data_dict.keys())}")
                
                # Check if we have the new format with selected_columns and selected_ranges
                if 'selected_columns' in data_dict and 'selected_ranges' in data_dict:
                    print("  Found selection criteria format")
                    print(f"  selected_columns: {data_dict['selected_columns']}")
                    print(f"  selected_ranges: {data_dict['selected_ranges']}")
                
                # Check if we have the old format with time_data and temperature_data
                if 'time_data' in data_dict and 'temperature_data' in data_dict:
                    print("  Found direct data format")
                    print(f"  time_data length: {len(data_dict['time_data'])}")
                    print(f"  temperature_data keys: {list(data_dict['temperature_data'].keys())}")
                    
                    # Try to create a DataFrame
                    try:
                        df = pd.DataFrame({'time': data_dict['time_data']})
                        for col, values in data_dict['temperature_data'].items():
                            df[col] = values
                        print(f"  Successfully created DataFrame with shape: {df.shape}")
                        return df
                    except Exception as e:
                        print(f"  Error creating DataFrame: {str(e)}")
                        traceback.print_exc()
            else:
                print(f"  Data is not a dictionary: {type(data_dict)}")
            
            return data_dict
        else:
            print(f"\nNo filtered temperature data found for test_id {test_id}")
            return None
    except Exception as e:
        print(f"Error testing get_filtered_temp_data: {str(e)}")
        traceback.print_exc()
        return None

def fix_filtered_temp_data(test_id):
    """Fix the filtered temperature data for a specific test"""
    try:
        # First check the current structure
        perf_data = check_performance_data(test_id)
        if not perf_data:
            print(f"No performance data found for test_id {test_id}, cannot fix.")
            return False
        
        # Get the temperature data
        conn = psycopg2.connect(**DB_PARAMS)
        cur = conn.cursor()
        
        # Get temperature data
        cur.execute("SELECT time_data, temperature_data FROM temperature_data WHERE test_id = %s", (test_id,))
        temp_result = cur.fetchone()
        
        if not temp_result:
            print(f"No temperature data found for test_id {test_id}, cannot fix.")
            conn.close()
            return False
        
        time_data, temperature_data = temp_result
        
        # Check if we have filtered_temp_data with selection criteria
        filtered_data = None
        if 'filtered_temp_data' in perf_data and isinstance(perf_data['filtered_temp_data'], dict):
            filtered_data = perf_data['filtered_temp_data']
        elif 'filtered_temperature_data' in perf_data and isinstance(perf_data['filtered_temperature_data'], dict):
            filtered_data = perf_data['filtered_temperature_data']
        
        if not filtered_data or 'selected_columns' not in filtered_data or 'selected_ranges' not in filtered_data:
            print(f"No valid filtered temperature data found for test_id {test_id}, cannot fix.")
            conn.close()
            return False
        
        # Create a DataFrame from the temperature data
        df = pd.DataFrame({'time': time_data})
        for col, values in temperature_data.items():
            df[col] = values
        
        # Apply the selection criteria
        selected_columns = filtered_data['selected_columns']
        selected_ranges = filtered_data['selected_ranges']
        
        # Create mask for selected ranges
        mask = pd.Series(False, index=df.index)
        for start, end in selected_ranges:
            mask |= ((df['time'] >= start) & (df['time'] <= end))
        
        # Filter data
        filtered_df = df[mask]
        
        # Select only chosen columns plus time
        columns_to_use = ['time'] + selected_columns
        filtered_df = filtered_df[columns_to_use]
        
        # Create the updated filtered_temp_data
        updated_filtered_data = {
            'selected_columns': selected_columns,
            'selected_ranges': selected_ranges,
            'time_data': filtered_df['time'].tolist(),
            'temperature_data': {col: filtered_df[col].tolist() for col in filtered_df.columns if col != 'time'}
        }
        
        # Update the performance_data
        perf_data['filtered_temp_data'] = updated_filtered_data
        if 'filtered_temperature_data' in perf_data:
            del perf_data['filtered_temperature_data']
        
        # Save the updated performance_data
        cur.execute(
            "UPDATE test_data SET performance_data = %s WHERE test_id = %s",
            (Json(perf_data), test_id)
        )
        conn.commit()
        conn.close()
        
        print(f"\nSuccessfully fixed filtered temperature data for test_id {test_id}")
        return True
    except Exception as e:
        print(f"Error fixing filtered temperature data: {str(e)}")
        traceback.print_exc()
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False

def main():
    print("=== Database Filtered Temperature Data Debug Tool ===\n")
    
    # Test database connection
    if not test_database_connection():
        print("Exiting due to database connection failure.")
        return
    
    # Get all test IDs
    test_ids = get_test_ids()
    if not test_ids:
        print("No tests found, exiting.")
        return
    
    # Ask which test to check
    while True:
        try:
            test_id_input = input("\nEnter test ID to check (or 'q' to quit): ")
            if test_id_input.lower() == 'q':
                break
            
            test_id = int(test_id_input)
            if test_id not in test_ids:
                print(f"Test ID {test_id} not found in the database.")
                continue
            
            # Check performance data
            check_performance_data(test_id)
            
            # Test retrieving filtered temperature data
            test_get_filtered_temp_data(test_id)
            
            # Ask if user wants to fix this test
            fix_input = input("\nDo you want to fix the filtered temperature data for this test? (y/n): ")
            if fix_input.lower() == 'y':
                fix_filtered_temp_data(test_id)
        except ValueError:
            print("Please enter a valid test ID.")
        except Exception as e:
            print(f"Error: {str(e)}")
            traceback.print_exc()

if __name__ == "__main__":
    main()
