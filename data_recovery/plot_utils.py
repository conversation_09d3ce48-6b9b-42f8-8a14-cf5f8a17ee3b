"""
Utility functions for saving and loading plots in the autosave system
"""
import os
import json
import base64
import io
import matplotlib.pyplot as plt
from matplotlib.figure import Figure
import numpy as np
from PIL import Image

def save_figure_to_file(fig, directory, filename):
    """
    Save a matplotlib figure to a file

    Args:
        fig: The matplotlib figure to save
        directory: The directory to save the figure in
        filename: The filename to save the figure as

    Returns:
        The full path to the saved figure
    """
    # Create directory if it doesn't exist
    os.makedirs(directory, exist_ok=True)

    # Create full path
    filepath = os.path.join(directory, filename)

    # Save figure
    fig.savefig(filepath, bbox_inches='tight', dpi=300, facecolor='white')

    return filepath

def encode_figure_to_base64(fig):
    """
    Encode a matplotlib figure to a base64 string

    Args:
        fig: The matplotlib figure to encode

    Returns:
        A base64 encoded string of the figure
    """
    # Save figure to a BytesIO object
    buf = io.BytesIO()
    fig.savefig(buf, format='png', bbox_inches='tight', dpi=150, facecolor='white')
    buf.seek(0)

    # Encode to base64
    img_str = base64.b64encode(buf.read()).decode('utf-8')

    return img_str

def decode_base64_to_figure(base64_str):
    """
    Decode a base64 string to a matplotlib figure

    Args:
        base64_str: The base64 encoded string

    Returns:
        A matplotlib figure
    """
    # Decode base64 string
    img_data = base64.b64decode(base64_str)

    # Create a BytesIO object
    buf = io.BytesIO(img_data)

    # Open image with PIL
    img = Image.open(buf)

    # Create a new figure
    fig = plt.figure(figsize=(img.width/100, img.height/100))

    # Add the image to the figure
    ax = fig.add_subplot(111)
    ax.imshow(np.array(img))
    ax.axis('off')

    return fig

def save_plots_for_autosave(mainwindow, data):
    """
    Save plots for autosave

    Args:
        mainwindow: The main window instance
        data: The data dictionary to update with plot information

    Returns:
        Updated data dictionary with plot information
    """
    try:
        print("Saving plots for autosave...")

        # Create plots directory if it doesn't exist
        user_home = os.path.expanduser("~")
        plots_dir = os.path.join(user_home, "temp_plots")
        os.makedirs(plots_dir, exist_ok=True)

        # Initialize plots data structure if it doesn't exist
        if 'plots' not in data:
            data['plots'] = {'default': [], 'custom': []}

        # Clear existing plot data to avoid duplicates
        data['plots']['default'] = []
        data['plots']['custom'] = []

        # Save current figures if they exist
        if hasattr(mainwindow, 'current_figures'):
            for plot_type, fig in mainwindow.current_figures.items():
                if fig is not None:
                    # Create a unique filename
                    filename = f"{plot_type}_plot_{os.path.basename(plots_dir)}.png"
                    filepath = save_figure_to_file(fig, plots_dir, filename)

                    # Also encode the figure to base64 for direct loading
                    base64_str = encode_figure_to_base64(fig)

                    # Add to plots data
                    plot_info = {
                        'path': filepath,
                        'title': f"{plot_type.capitalize()} Plot",
                        'type': plot_type,
                        'base64': base64_str
                    }

                    data['plots']['default'].append(plot_info)
                    print(f"Saved figure: {plot_info['title']}")

        # Save report plots if they exist
        if hasattr(mainwindow, 'report_plots'):
            for category in ['default', 'custom']:
                if category in mainwindow.report_plots:
                    for plot_info in mainwindow.report_plots[category]:
                        # Check if the plot has a path and it exists
                        if 'path' in plot_info and os.path.exists(plot_info['path']):
                            # Create a clean copy with only serializable data
                            clean_plot_data = {
                                'path': plot_info['path'],
                                'title': plot_info.get('title', 'Unnamed Plot'),
                                'type': plot_info.get('type', 'temperature')
                            }

                            # Try to encode the figure to base64 if possible
                            try:
                                # Load the figure from the file
                                img = Image.open(plot_info['path'])

                                # Convert to base64
                                buf = io.BytesIO()
                                img.save(buf, format='PNG')
                                buf.seek(0)
                                base64_str = base64.b64encode(buf.read()).decode('utf-8')

                                # Add base64 to plot data
                                clean_plot_data['base64'] = base64_str
                            except Exception as e:
                                print(f"Error encoding plot to base64: {str(e)}")

                            # Add to plots data
                            data['plots'][category].append(clean_plot_data)
                            print(f"Saved plot: {clean_plot_data['title']}")

        return data

    except Exception as e:
        print(f"Error saving plots for autosave: {str(e)}")
        import traceback
        traceback.print_exc()
        return data

def load_plots_from_autosave(mainwindow, data):
    """
    Load plots from autosave

    Args:
        mainwindow: The main window instance
        data: The data dictionary containing plot information

    Returns:
        True if plots were loaded successfully, False otherwise
    """
    try:
        print("Loading plots from autosave...")

        # Check if plots data exists
        if 'plots' not in data:
            print("No plots data found in autosave")
            return False

        # Initialize report_plots if it doesn't exist
        if not hasattr(mainwindow, 'report_plots'):
            mainwindow.report_plots = {'default': [], 'custom': []}

        # Clear existing plots
        mainwindow.report_plots['default'] = []
        mainwindow.report_plots['custom'] = []

        # Load plots
        for category in ['default', 'custom']:
            if category in data['plots']:
                for plot_info in data['plots'][category]:
                    try:
                        # Check if we have a base64 encoded image
                        if 'base64' in plot_info:
                            # Create a temporary file for the plot
                            user_home = os.path.expanduser("~")
                            plots_dir = os.path.join(user_home, "temp_plots")
                            os.makedirs(plots_dir, exist_ok=True)

                            # Create a unique filename
                            safe_title = plot_info['title'].replace(' ', '_').replace('/', '_').replace('\\', '_')
                            filename = f"{plot_info['type']}_{safe_title}_{os.path.basename(plots_dir)}.png"
                            filepath = os.path.join(plots_dir, filename)

                            # Decode base64 to image and save
                            img_data = base64.b64decode(plot_info['base64'])
                            with open(filepath, 'wb') as f:
                                f.write(img_data)

                            # Update path in plot info
                            plot_info['path'] = filepath

                        # Check if the path exists
                        if 'path' in plot_info and os.path.exists(plot_info['path']):
                            # Create a clean copy with only necessary data
                            clean_plot_data = {
                                'path': plot_info['path'],
                                'title': plot_info.get('title', 'Unnamed Plot'),
                                'type': plot_info.get('type', 'temperature')
                            }

                            # Add to report plots
                            mainwindow.report_plots[category].append(clean_plot_data)

                            # Add to preview if the method exists
                            if hasattr(mainwindow, 'add_plot_to_preview'):
                                try:
                                    mainwindow.add_plot_to_preview(clean_plot_data)
                                except Exception as preview_error:
                                    print(f"Error adding plot to preview: {str(preview_error)}")

                            print(f"Loaded plot: {clean_plot_data['title']}")
                        else:
                            print(f"Plot file not found: {plot_info.get('path', 'unknown')}")

                    except Exception as e:
                        print(f"Error loading plot: {str(e)}")

        # Enable plots button if we loaded any plots
        if any(len(mainwindow.report_plots[category]) > 0 for category in ['default', 'custom']):
            if hasattr(mainwindow, 'ui') and hasattr(mainwindow.ui, 'btnPlots'):
                mainwindow.ui.btnPlots.setEnabled(True)
            return True

        return False

    except Exception as e:
        print(f"Error loading plots from autosave: {str(e)}")
        import traceback
        traceback.print_exc()
        return False