#!/usr/bin/env python3
"""
Test script to verify centralized color management system
"""

from color_manager import color_manager, get_column_color, print_color_assignments
from color_utils import assign_color

def test_centralized_color_system():
    """Test the centralized color management system"""
    print("=" * 60)
    print("TESTING CENTRALIZED COLOR MANAGEMENT SYSTEM")
    print("=" * 60)
    
    # Clear any existing assignments
    color_manager.clear_assignments()
    
    # Your actual column names
    test_columns = [
        "Tank Bottom (°C)",
        "Tank Flange (°C)", 
        "Thruster ka Flange (°C)",
        "Nozzle ki Convergent (°C)",
        "Nozzle ki Exit (°C)",
        "Tank Mid (°C)",
        "Tank ka Lid (°C)",
        "Thruster ka Chamber (°C)"
    ]
    
    print("\n1. Testing direct color manager access:")
    print("-" * 40)
    colors_direct = {}
    for column in test_columns:
        color = color_manager.get_color(column)
        colors_direct[column] = color
        print(f"  {column}: {color}")
    
    print("\n2. Testing color_utils.assign_color function:")
    print("-" * 40)
    colors_utils = {}
    dummy_dict = {}  # This won't be used but kept for compatibility
    for column in test_columns:
        color = assign_color(column, dummy_dict)
        colors_utils[column] = color
        print(f"  {column}: {color}")
    
    print("\n3. Verifying consistency:")
    print("-" * 40)
    consistent = True
    for column in test_columns:
        if colors_direct[column] != colors_utils[column]:
            print(f"  ❌ INCONSISTENT: {column}")
            print(f"     Direct: {colors_direct[column]}")
            print(f"     Utils:  {colors_utils[column]}")
            consistent = False
        else:
            print(f"  ✅ CONSISTENT: {column} -> {colors_direct[column]}")
    
    print("\n4. Testing color uniqueness:")
    print("-" * 40)
    all_colors = list(colors_direct.values())
    unique_colors = set(all_colors)
    
    print(f"  Total columns: {len(test_columns)}")
    print(f"  Unique colors: {len(unique_colors)}")
    
    if len(unique_colors) == len(test_columns):
        print("  ✅ All colors are unique!")
    else:
        print("  ⚠️  Some colors are duplicated")
        # Find duplicates
        from collections import Counter
        color_counts = Counter(all_colors)
        duplicates = {color: count for color, count in color_counts.items() if count > 1}
        for color, count in duplicates.items():
            columns_with_color = [col for col, c in colors_direct.items() if c == color]
            print(f"    Color {color} used {count} times: {columns_with_color}")
    
    print("\n5. Testing session persistence:")
    print("-" * 40)
    # Get color for first column again
    first_column = test_columns[0]
    color_again = color_manager.get_color(first_column)
    if color_again == colors_direct[first_column]:
        print(f"  ✅ Color persistence works: {first_column} -> {color_again}")
    else:
        print(f"  ❌ Color persistence failed: {first_column}")
        print(f"     First time: {colors_direct[first_column]}")
        print(f"     Second time: {color_again}")
    
    print("\n6. Current color assignments:")
    print("-" * 40)
    print_color_assignments()
    
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    
    if consistent and len(unique_colors) == len(test_columns):
        print("✅ SUCCESS: Centralized color management is working correctly!")
        print("   - All colors are unique and distinct")
        print("   - Color assignments are consistent across components")
        print("   - Colors persist within the session")
    else:
        print("❌ ISSUES FOUND:")
        if not consistent:
            print("   - Color assignments are inconsistent between components")
        if len(unique_colors) != len(test_columns):
            print("   - Some colors are duplicated")
    
    print("\nExpected behavior after restart:")
    print("- Each column gets a unique, bright color")
    print("- Same column always gets the same color in a session")
    print("- Colors are consistent across all plots and components")
    
    return consistent and len(unique_colors) == len(test_columns)

if __name__ == "__main__":
    success = test_centralized_color_system()
    exit(0 if success else 1)
