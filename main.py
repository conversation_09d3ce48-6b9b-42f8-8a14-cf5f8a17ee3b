#!/usr/bin/env python3
"""
VAPR-iDEX Thruster Analysis Application
Main entry point for the application.
"""

import sys
import logging
from PySide6.QtWidgets import QApplication
from PySide6.QtGui import QIcon
from PySide6.QtCore import QTimer

from ui.splash_screen import CustomSplashScreen
from main_window import MainWindow
from config.app_config import AppConfig
from src.utils import get_resource_path


def setup_logging():
    """Configure application logging."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('vapr_idex.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )


def main():
    """Main application entry point."""
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # Create QApplication
        app = QApplication(sys.argv)
        app.setStyle("Fusion")
        
        # Set application icon
        icon_path = get_resource_path("assets/icon.ico")
        app.setWindowIcon(QIcon(icon_path))
        
        # Set Windows taskbar icon explicitly
        if hasattr(sys, 'frozen'):
            import ctypes
            myappid = 'manastuspace.vapridexanalyzer.1.0'
            ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(myappid)
        
        # Create and show splash screen
        splash = CustomSplashScreen()
        splash.show()
        
        # Create main window
        main_window = MainWindow()
        
        # Setup splash screen completion
        def on_splash_finished():
            """Handle splash screen completion."""
            splash.play_boot_sound()
            # Adding a small delay to ensure sound starts playing before window transition
            QTimer.singleShot(500, lambda: (splash.close(), main_window.show()))
        
        # Connect splash screen signals
        splash.initialization_finished.connect(on_splash_finished)
        
        # Start the application
        logger.info("Starting VAPR-iDEX Thruster Analysis Application")
        sys.exit(app.exec())
        
    except Exception as e:
        logger.error(f"Failed to start application: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
