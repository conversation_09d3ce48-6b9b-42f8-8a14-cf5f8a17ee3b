#!/usr/bin/env python3
"""
Test script to verify color assignment functionality
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gui.widgets.temperature_selection import TemperatureSelectionWidget
from color_utils import assign_color

def test_temperature_selection_widget_colors():
    """Test that the temperature selection widget assigns distinct colors"""
    print("Testing TemperatureSelectionWidget color assignment...")
    
    # Create widget instance
    widget = TemperatureSelectionWidget()
    
    # Test columns with different names
    test_columns = [
        "Tank Bottom Temperature",
        "Tank Center Temperature", 
        "Tank Lid Temperature",
        "Catalyst Chamber Temperature",
        "Nozzle Convergent Temperature",
        "Nozzle Exit Temperature",
        "Custom Column 1",
        "Custom Column 2",
        "Custom Column 3",
        "Custom Column 4",
        "Custom Column 5"
    ]
    
    # Add columns to widget
    widget.add_temperature_columns(test_columns)
    
    # Check color assignments
    colors_assigned = {}
    for column in test_columns:
        color = widget.get_column_color(column)
        colors_assigned[column] = color
        print(f"{column}: {color}")
    
    # Check for duplicates
    color_values = list(colors_assigned.values())
    unique_colors = set(color_values)
    
    print(f"\nTotal columns: {len(test_columns)}")
    print(f"Unique colors assigned: {len(unique_colors)}")
    print(f"Color palette size: {len(widget.COLOR_PALETTE)}")
    
    # Check if we have reasonable color distribution
    if len(unique_colors) >= min(len(test_columns), len(widget.COLOR_PALETTE)):
        print("✅ Color assignment test PASSED - Good color distribution")
    else:
        print("❌ Color assignment test FAILED - Too many duplicate colors")
        
    # Check that no colors are black or very dark
    dark_colors = []
    for column, color in colors_assigned.items():
        if color.lower() in ['#000000', '#000', 'black'] or color.startswith('#00'):
            dark_colors.append((column, color))
    
    if dark_colors:
        print(f"⚠️  Warning: Found potentially dark colors: {dark_colors}")
    else:
        print("✅ No black/dark colors detected")
    
    return colors_assigned

def test_color_utils():
    """Test the color_utils assign_color function"""
    print("\nTesting color_utils.assign_color function...")
    
    color_dict = {}
    test_columns = [
        "Temperature Sensor 1",
        "Temperature Sensor 2", 
        "Temperature Sensor 3",
        "Pressure Sensor A",
        "Pressure Sensor B"
    ]
    
    colors_assigned = {}
    for column in test_columns:
        color = assign_color(column, color_dict)
        colors_assigned[column] = color
        print(f"{column}: {color}")
    
    # Check for duplicates
    color_values = list(colors_assigned.values())
    unique_colors = set(color_values)
    
    print(f"\nTotal columns: {len(test_columns)}")
    print(f"Unique colors assigned: {len(unique_colors)}")
    
    if len(unique_colors) == len(test_columns):
        print("✅ color_utils test PASSED - All unique colors")
    else:
        print("❌ color_utils test FAILED - Duplicate colors found")
    
    return colors_assigned

def test_hash_based_colors():
    """Test to show the problem with hash-based color assignment"""
    print("\nTesting old hash-based color assignment (for comparison)...")
    
    test_columns = [
        "Column A",
        "Column B", 
        "Column C",
        "Column D",
        "Column E"
    ]
    
    hash_colors = {}
    for column in test_columns:
        # This is the old problematic method
        hash_color = f"#{hash(column) % 0xFFFFFF:06x}"
        hash_colors[column] = hash_color
        print(f"{column}: {hash_color}")
    
    # Check for dark colors
    dark_hash_colors = []
    for column, color in hash_colors.items():
        # Convert hex to int to check brightness
        hex_val = int(color[1:], 16)
        # If the color value is low, it's dark
        if hex_val < 0x333333:  # Threshold for "dark"
            dark_hash_colors.append((column, color))
    
    if dark_hash_colors:
        print(f"⚠️  Hash-based method produced dark colors: {dark_hash_colors}")
    else:
        print("✅ Hash-based method produced no dark colors")
    
    return hash_colors

if __name__ == "__main__":
    print("=" * 60)
    print("COLOR ASSIGNMENT TEST SUITE")
    print("=" * 60)
    
    try:
        # Test the new improved color assignment
        widget_colors = test_temperature_selection_widget_colors()
        utils_colors = test_color_utils()
        hash_colors = test_hash_based_colors()
        
        print("\n" + "=" * 60)
        print("SUMMARY")
        print("=" * 60)
        print("The new color assignment system should provide:")
        print("1. Distinct colors for different columns")
        print("2. No black or very dark colors")
        print("3. Consistent color assignment across components")
        print("\nIf you were seeing black colors before, they should now be")
        print("replaced with bright, distinct colors from the palette.")
        
    except Exception as e:
        print(f"Error running tests: {str(e)}")
        import traceback
        traceback.print_exc()
