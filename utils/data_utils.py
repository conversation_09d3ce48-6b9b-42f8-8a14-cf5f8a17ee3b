"""
Data Utilities
Common utility functions for data handling.
"""


def safe_float(value, default='NA'):
    """
    Safely convert a value to float with fallback.
    
    Args:
        value: Value to convert to float
        default: Default value if conversion fails
        
    Returns:
        Float value or default if conversion fails
    """
    try:
        if value and str(value).strip():
            return float(value)
        return default
    except (ValueError, TypeError):
        return default


def safe_int(value, default=0):
    """
    Safely convert a value to int with fallback.
    
    Args:
        value: Value to convert to int
        default: Default value if conversion fails
        
    Returns:
        Int value or default if conversion fails
    """
    try:
        if value and str(value).strip():
            return int(value)
        return default
    except (ValueError, TypeError):
        return default


def safe_str(value, default=''):
    """
    Safely convert a value to string with fallback.
    
    Args:
        value: Value to convert to string
        default: Default value if conversion fails
        
    Returns:
        String value or default if conversion fails
    """
    try:
        if value is not None:
            return str(value).strip()
        return default
    except Exception:
        return default


def validate_numeric_range(value, min_val=None, max_val=None):
    """
    Validate if a numeric value is within specified range.
    
    Args:
        value: Numeric value to validate
        min_val: Minimum allowed value (optional)
        max_val: Maximum allowed value (optional)
        
    Returns:
        bool: True if value is within range, False otherwise
    """
    try:
        num_value = float(value)
        
        if min_val is not None and num_value < min_val:
            return False
            
        if max_val is not None and num_value > max_val:
            return False
            
        return True
    except (ValueError, TypeError):
        return False


def format_scientific_notation(value, precision=2):
    """
    Format a number in scientific notation.
    
    Args:
        value: Numeric value to format
        precision: Number of decimal places
        
    Returns:
        Formatted string in scientific notation
    """
    try:
        num_value = float(value)
        return f"{num_value:.{precision}e}"
    except (ValueError, TypeError):
        return str(value)
